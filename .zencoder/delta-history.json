{"snapshots": {"/terminal_output": {"filePath": "/terminal_output", "baseContent": "xrgouda@Amrs-MacBook-Air AFA_app % \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n", "baseTimestamp": 1754858649820, "deltas": [{"timestamp": 1754929662781, "changes": [{"type": "MODIFY", "lineNumber": 0, "content": "xrgouda@Amrs-MacBook-Air AFA_app % flutter build appbundle --release", "oldContent": "xrgouda@Amrs-MacBook-Air AFA_app % "}]}, {"timestamp": 1754929973643, "changes": [{"type": "INSERT", "lineNumber": 1, "content": "Running Gradle task 'bundleRelease'...                                 ⣟"}, {"type": "DELETE", "lineNumber": 15, "oldContent": ""}]}, {"timestamp": 1754930037261, "changes": [{"type": "MODIFY", "lineNumber": 1, "content": "Running Gradle task 'bundleRelease'...                                 ⣯", "oldContent": "Running Gradle task 'bundleRelease'...                                 ⣟"}]}, {"timestamp": 1754930048445, "changes": [{"type": "MODIFY", "lineNumber": 1, "content": "Running Gradle task 'bundleRelease'...                                 ⣾", "oldContent": "Running Gradle task 'bundleRelease'...                                 ⣯"}]}, {"timestamp": 1754930453025, "changes": [{"type": "DELETE", "lineNumber": 1, "oldContent": "Running Gradle task 'bundleRelease'...                                 ⣾"}, {"type": "INSERT", "lineNumber": 2, "content": "FAILURE: Build failed with an exception."}, {"type": "INSERT", "lineNumber": 4, "content": "* Where:"}, {"type": "INSERT", "lineNumber": 5, "content": "Build file '/Users/<USER>/Flutter-Projects/AFA_app/android/build.gradle' line: 13"}, {"type": "INSERT", "lineNumber": 7, "content": "* What went wrong:"}, {"type": "INSERT", "lineNumber": 8, "content": "A problem occurred evaluating root project 'android'."}, {"type": "INSERT", "lineNumber": 9, "content": "> A problem occurred configuring project ':app'."}, {"type": "INSERT", "lineNumber": 10, "content": "   > [CXX1101] NDK at /Users/<USER>/Library/Android/sdk/ndk/26.3.11579264 did not have a source.properties file"}, {"type": "INSERT", "lineNumber": 12, "content": "* Try:"}, {"type": "INSERT", "lineNumber": 13, "content": "> Run with --stacktrace option to get the stack trace."}, {"type": "INSERT", "lineNumber": 14, "content": "> Run with --info or --debug option to get more log output."}, {"type": "INSERT", "lineNumber": 15, "content": "> Run with --scan to get full insights."}, {"type": "INSERT", "lineNumber": 16, "content": "> Get more help at https://help.gradle.org."}, {"type": "DELETE", "lineNumber": 7, "oldContent": ""}, {"type": "DELETE", "lineNumber": 8, "oldContent": ""}, {"type": "DELETE", "lineNumber": 9, "oldContent": ""}, {"type": "DELETE", "lineNumber": 10, "oldContent": ""}, {"type": "DELETE", "lineNumber": 11, "oldContent": ""}, {"type": "DELETE", "lineNumber": 12, "oldContent": ""}, {"type": "DELETE", "lineNumber": 13, "oldContent": ""}, {"type": "DELETE", "lineNumber": 14, "oldContent": ""}, {"type": "DELETE", "lineNumber": 15, "oldContent": ""}, {"type": "INSERT", "lineNumber": 18, "content": "BUILD FAILED in 13m 4s"}, {"type": "INSERT", "lineNumber": 19, "content": "Running Gradle task 'bundleRelease'...                            786.4s"}, {"type": "INSERT", "lineNumber": 20, "content": "Gradle task bundleRelease failed with exit code 1"}, {"type": "INSERT", "lineNumber": 21, "content": "xrgouda@Amrs-MacBook-Air AFA_app % "}]}, {"timestamp": 1754930631685, "changes": [{"type": "INSERT", "lineNumber": 1, "content": ""}, {"type": "DELETE", "lineNumber": 3, "oldContent": ""}, {"type": "DELETE", "lineNumber": 5, "oldContent": ""}, {"type": "DELETE", "lineNumber": 8, "oldContent": ""}, {"type": "INSERT", "lineNumber": 11, "content": ""}, {"type": "DELETE", "lineNumber": 18, "oldContent": "xrgouda@Amrs-MacBook-Air AFA_app % "}, {"type": "DELETE", "lineNumber": 19, "oldContent": "Gradle task bundleRelease failed with exit code 1"}, {"type": "INSERT", "lineNumber": 17, "content": ""}, {"type": "INSERT", "lineNumber": 20, "content": "Gradle task bundleRelease failed with exit code 1"}, {"type": "INSERT", "lineNumber": 21, "content": "xrgouda@Amrs-MacBook-Air AFA_app % flutter build appbundle --release"}, {"type": "INSERT", "lineNumber": 22, "content": ""}]}, {"timestamp": 1754931246886, "changes": [{"type": "DELETE", "lineNumber": 9, "oldContent": ""}, {"type": "INSERT", "lineNumber": 11, "content": ""}, {"type": "DELETE", "lineNumber": 16, "oldContent": ""}, {"type": "DELETE", "lineNumber": 18, "oldContent": "Gradle task bundleRelease failed with exit code 1"}, {"type": "INSERT", "lineNumber": 17, "content": ""}, {"type": "MODIFY", "lineNumber": 20, "content": "Gradle task bundleRelease failed with exit code 1", "oldContent": "xrgouda@Amrs-MacBook-Air AFA_app % flutter build appbundle --release"}, {"type": "INSERT", "lineNumber": 21, "content": "xrgouda@Amrs-MacBook-Air AFA_app % flutter build appbundle --release"}, {"type": "INSERT", "lineNumber": 22, "content": "Font asset \"MaterialIcons-Regular.otf\" was tree-shaken, reducing it from 1645184 to 4716 bytes (99.7% reduction). Tree-shaking can be disabled by providing the --no-tree-shake-icons flag when building your app."}, {"type": "INSERT", "lineNumber": 24, "content": "FAILURE: Build completed with 2 failures."}, {"type": "INSERT", "lineNumber": 25, "content": ""}, {"type": "INSERT", "lineNumber": 26, "content": "1: Task failed with an exception."}, {"type": "INSERT", "lineNumber": 27, "content": "-----------"}, {"type": "INSERT", "lineNumber": 28, "content": "* What went wrong:"}, {"type": "INSERT", "lineNumber": 29, "content": "Execution failed for task ':app:shrinkBundleReleaseResources'."}, {"type": "INSERT", "lineNumber": 30, "content": "> A failure occurred while executing com.android.build.gradle.internal.transforms.ShrinkAppBundleResourcesAction"}, {"type": "INSERT", "lineNumber": 31, "content": "   > No space left on device"}, {"type": "INSERT", "lineNumber": 32, "content": ""}, {"type": "INSERT", "lineNumber": 33, "content": "* Try:"}, {"type": "INSERT", "lineNumber": 34, "content": "> Run with --stacktrace option to get the stack trace."}, {"type": "INSERT", "lineNumber": 35, "content": "> Run with --info or --debug option to get more log output."}, {"type": "INSERT", "lineNumber": 36, "content": "> Run with --scan to get full insights."}, {"type": "INSERT", "lineNumber": 37, "content": "> Get more help at https://help.gradle.org."}, {"type": "INSERT", "lineNumber": 38, "content": "=============================================================================="}, {"type": "INSERT", "lineNumber": 39, "content": ""}, {"type": "INSERT", "lineNumber": 40, "content": "2: Task failed with an exception."}, {"type": "INSERT", "lineNumber": 41, "content": "-----------"}, {"type": "INSERT", "lineNumber": 42, "content": "* What went wrong:"}, {"type": "INSERT", "lineNumber": 43, "content": "java.io.IOException: No space left on device"}, {"type": "INSERT", "lineNumber": 44, "content": ""}, {"type": "INSERT", "lineNumber": 45, "content": "* Try:"}, {"type": "INSERT", "lineNumber": 46, "content": "> Run with --stacktrace option to get the stack trace."}, {"type": "INSERT", "lineNumber": 47, "content": "> Run with --info or --debug option to get more log output."}, {"type": "INSERT", "lineNumber": 48, "content": "> Run with --scan to get full insights."}, {"type": "INSERT", "lineNumber": 49, "content": "> Get more help at https://help.gradle.org."}, {"type": "INSERT", "lineNumber": 50, "content": "=============================================================================="}, {"type": "INSERT", "lineNumber": 51, "content": ""}, {"type": "INSERT", "lineNumber": 52, "content": "BUILD FAILED in 10m 4s"}, {"type": "INSERT", "lineNumber": 53, "content": ""}, {"type": "INSERT", "lineNumber": 54, "content": "FAILURE: Build failed with an exception."}, {"type": "INSERT", "lineNumber": 55, "content": ""}, {"type": "INSERT", "lineNumber": 56, "content": "* What went wrong:"}, {"type": "INSERT", "lineNumber": 57, "content": "Could not stop all services."}, {"type": "INSERT", "lineNumber": 58, "content": "> Could not stop all services."}, {"type": "INSERT", "lineNumber": 59, "content": "   > Failed to release lock on Build Output Cleanup Cache (/Users/<USER>/Flutter-Projects/AFA_app/android/.gradle/buildOutputCleanup)"}, {"type": "INSERT", "lineNumber": 60, "content": "   > Failed to release lock on execution history cache (/Users/<USER>/Flutter-Projects/AFA_app/android/.gradle/8.12/executionHistory)"}, {"type": "INSERT", "lineNumber": 61, "content": "> Could not stop all services."}, {"type": "INSERT", "lineNumber": 62, "content": "   > Failed to release lock on cache directory md-supplier (/Users/<USER>/.gradle/caches/8.12/md-supplier)"}, {"type": "INSERT", "lineNumber": 63, "content": "   > Could not stop all services."}, {"type": "INSERT", "lineNumber": 64, "content": "      > Failed to release lock on Build Output Cleanup Cache (/Users/<USER>/FlutterDev/flutter/packages/flutter_tools/gradle/.gradle/buildOutputCleanup)                                                                                                                                 "}, {"type": "INSERT", "lineNumber": 65, "content": "      > Failed to release lock on execution history cache (/Users/<USER>/FlutterDev/flutter/packages/flutter_tools/gradle/.gradle/8.12/executionHistory)                                                                                                                                 "}, {"type": "INSERT", "lineNumber": 66, "content": "   > Failed to release lock on cache directory md-rule (/Users/<USER>/.gradle/caches/8.12/md-rule)"}, {"type": "INSERT", "lineNumber": 67, "content": ""}, {"type": "INSERT", "lineNumber": 68, "content": "* Try:"}, {"type": "INSERT", "lineNumber": 69, "content": "> Run with --stacktrace option to get the stack trace."}, {"type": "INSERT", "lineNumber": 70, "content": "> Run with --info or --debug option to get more log output."}, {"type": "INSERT", "lineNumber": 71, "content": "> Run with --scan to get full insights."}, {"type": "INSERT", "lineNumber": 72, "content": "> Get more help at https://help.gradle.org."}, {"type": "INSERT", "lineNumber": 73, "content": ""}, {"type": "INSERT", "lineNumber": 74, "content": "BUILD FAILED in 10m 5s"}, {"type": "INSERT", "lineNumber": 75, "content": "Running Gradle task 'bundleRelease'...                            605.4s"}, {"type": "INSERT", "lineNumber": 76, "content": "Gradle task bundleRelease failed with exit code 1"}, {"type": "INSERT", "lineNumber": 77, "content": "xrgouda@Amrs-MacBook-Air AFA_app % "}]}, {"timestamp": 1754931465050, "changes": [{"type": "DELETE", "lineNumber": 0, "oldContent": "xrgouda@Amrs-MacBook-Air AFA_app % flutter build appbundle --release"}, {"type": "DELETE", "lineNumber": 1, "oldContent": ""}, {"type": "DELETE", "lineNumber": 2, "oldContent": "FAILURE: Build failed with an exception."}, {"type": "DELETE", "lineNumber": 3, "oldContent": ""}, {"type": "DELETE", "lineNumber": 4, "oldContent": "* Where:"}, {"type": "DELETE", "lineNumber": 5, "oldContent": "Build file '/Users/<USER>/Flutter-Projects/AFA_app/android/build.gradle' line: 13"}, {"type": "DELETE", "lineNumber": 6, "oldContent": ""}, {"type": "DELETE", "lineNumber": 7, "oldContent": "* What went wrong:"}, {"type": "DELETE", "lineNumber": 8, "oldContent": "A problem occurred evaluating root project 'android'."}, {"type": "DELETE", "lineNumber": 9, "oldContent": "> A problem occurred configuring project ':app'."}, {"type": "DELETE", "lineNumber": 10, "oldContent": ""}, {"type": "DELETE", "lineNumber": 11, "oldContent": "   > [CXX1101] NDK at /Users/<USER>/Library/Android/sdk/ndk/26.3.11579264 did not have a source.properties file"}, {"type": "DELETE", "lineNumber": 12, "oldContent": "* Try:"}, {"type": "DELETE", "lineNumber": 13, "oldContent": "> Run with --stacktrace option to get the stack trace."}, {"type": "DELETE", "lineNumber": 14, "oldContent": "> Run with --info or --debug option to get more log output."}, {"type": "DELETE", "lineNumber": 15, "oldContent": "> Run with --scan to get full insights."}, {"type": "DELETE", "lineNumber": 16, "oldContent": ""}, {"type": "DELETE", "lineNumber": 17, "oldContent": "> Get more help at https://help.gradle.org."}, {"type": "DELETE", "lineNumber": 18, "oldContent": "BUILD FAILED in 13m 4s"}, {"type": "DELETE", "lineNumber": 19, "oldContent": "Gradle task bundleRelease failed with exit code 1"}, {"type": "DELETE", "lineNumber": 20, "oldContent": "xrgouda@Amrs-MacBook-Air AFA_app % flutter build appbundle --release"}, {"type": "DELETE", "lineNumber": 21, "oldContent": "Running Gradle task 'bundleRelease'...                            786.4s"}, {"type": "DELETE", "lineNumber": 22, "oldContent": "Font asset \"MaterialIcons-Regular.otf\" was tree-shaken, reducing it from 1645184 to 4716 bytes (99.7% reduction). Tree-shaking can be disabled by providing the --no-tree-shake-icons flag when building your app."}, {"type": "DELETE", "lineNumber": 23, "oldContent": ""}, {"type": "DELETE", "lineNumber": 24, "oldContent": "xrgouda@Amrs-MacBook-Air AFA_app % "}, {"type": "DELETE", "lineNumber": 25, "oldContent": "FAILURE: Build completed with 2 failures."}, {"type": "DELETE", "lineNumber": 26, "oldContent": "Gradle task bundleRelease failed with exit code 1"}, {"type": "DELETE", "lineNumber": 27, "oldContent": ""}, {"type": "DELETE", "lineNumber": 28, "oldContent": "Running Gradle task 'bundleRelease'...                            605.4s"}, {"type": "DELETE", "lineNumber": 29, "oldContent": "1: Task failed with an exception."}, {"type": "DELETE", "lineNumber": 30, "oldContent": "BUILD FAILED in 10m 5s"}, {"type": "DELETE", "lineNumber": 31, "oldContent": "-----------"}, {"type": "DELETE", "lineNumber": 32, "oldContent": ""}, {"type": "DELETE", "lineNumber": 33, "oldContent": "* What went wrong:"}, {"type": "DELETE", "lineNumber": 34, "oldContent": "> Get more help at https://help.gradle.org."}, {"type": "DELETE", "lineNumber": 35, "oldContent": "Execution failed for task ':app:shrinkBundleReleaseResources'."}, {"type": "DELETE", "lineNumber": 36, "oldContent": "> Run with --scan to get full insights."}, {"type": "DELETE", "lineNumber": 37, "oldContent": "> A failure occurred while executing com.android.build.gradle.internal.transforms.ShrinkAppBundleResourcesAction"}, {"type": "DELETE", "lineNumber": 38, "oldContent": "> Run with --info or --debug option to get more log output."}, {"type": "DELETE", "lineNumber": 39, "oldContent": "   > No space left on device"}, {"type": "DELETE", "lineNumber": 40, "oldContent": "> Run with --stacktrace option to get the stack trace."}, {"type": "DELETE", "lineNumber": 41, "oldContent": ""}, {"type": "DELETE", "lineNumber": 42, "oldContent": "* Try:"}, {"type": "DELETE", "lineNumber": 43, "oldContent": "* Try:"}, {"type": "DELETE", "lineNumber": 44, "oldContent": ""}, {"type": "DELETE", "lineNumber": 45, "oldContent": "> Run with --stacktrace option to get the stack trace."}, {"type": "DELETE", "lineNumber": 46, "oldContent": "   > Failed to release lock on cache directory md-rule (/Users/<USER>/.gradle/caches/8.12/md-rule)"}, {"type": "DELETE", "lineNumber": 47, "oldContent": "> Run with --info or --debug option to get more log output."}, {"type": "DELETE", "lineNumber": 48, "oldContent": "      > Failed to release lock on execution history cache (/Users/<USER>/FlutterDev/flutter/packages/flutter_tools/gradle/.gradle/8.12/executionHistory)                                                                                                                                 "}, {"type": "DELETE", "lineNumber": 49, "oldContent": "> Run with --scan to get full insights."}, {"type": "DELETE", "lineNumber": 50, "oldContent": "      > Failed to release lock on Build Output Cleanup Cache (/Users/<USER>/FlutterDev/flutter/packages/flutter_tools/gradle/.gradle/buildOutputCleanup)                                                                                                                                 "}, {"type": "DELETE", "lineNumber": 51, "oldContent": "> Get more help at https://help.gradle.org."}, {"type": "DELETE", "lineNumber": 52, "oldContent": "   > Could not stop all services."}, {"type": "DELETE", "lineNumber": 53, "oldContent": "=============================================================================="}, {"type": "DELETE", "lineNumber": 54, "oldContent": "   > Failed to release lock on cache directory md-supplier (/Users/<USER>/.gradle/caches/8.12/md-supplier)"}, {"type": "DELETE", "lineNumber": 55, "oldContent": ""}, {"type": "DELETE", "lineNumber": 56, "oldContent": "> Could not stop all services."}, {"type": "DELETE", "lineNumber": 57, "oldContent": "2: Task failed with an exception."}, {"type": "DELETE", "lineNumber": 58, "oldContent": "   > Failed to release lock on execution history cache (/Users/<USER>/Flutter-Projects/AFA_app/android/.gradle/8.12/executionHistory)"}, {"type": "DELETE", "lineNumber": 59, "oldContent": "-----------"}, {"type": "DELETE", "lineNumber": 60, "oldContent": "   > Failed to release lock on Build Output Cleanup Cache (/Users/<USER>/Flutter-Projects/AFA_app/android/.gradle/buildOutputCleanup)"}, {"type": "DELETE", "lineNumber": 61, "oldContent": "* What went wrong:"}, {"type": "DELETE", "lineNumber": 62, "oldContent": "> Could not stop all services."}, {"type": "DELETE", "lineNumber": 63, "oldContent": "java.io.IOException: No space left on device"}, {"type": "DELETE", "lineNumber": 64, "oldContent": "Could not stop all services."}, {"type": "DELETE", "lineNumber": 65, "oldContent": ""}, {"type": "DELETE", "lineNumber": 66, "oldContent": "* What went wrong:"}, {"type": "DELETE", "lineNumber": 67, "oldContent": "* Try:"}, {"type": "DELETE", "lineNumber": 68, "oldContent": ""}, {"type": "DELETE", "lineNumber": 69, "oldContent": "> Run with --stacktrace option to get the stack trace."}, {"type": "DELETE", "lineNumber": 70, "oldContent": "FAILURE: Build failed with an exception."}, {"type": "DELETE", "lineNumber": 71, "oldContent": "> Run with --info or --debug option to get more log output."}, {"type": "DELETE", "lineNumber": 72, "oldContent": ""}, {"type": "DELETE", "lineNumber": 73, "oldContent": "> Run with --scan to get full insights."}, {"type": "DELETE", "lineNumber": 74, "oldContent": "BUILD FAILED in 10m 4s"}, {"type": "DELETE", "lineNumber": 75, "oldContent": "> Get more help at https://help.gradle.org."}, {"type": "DELETE", "lineNumber": 76, "oldContent": ""}, {"type": "DELETE", "lineNumber": 77, "oldContent": "=============================================================================="}, {"type": "INSERT", "lineNumber": 0, "content": "                                                                                                                                             "}]}, {"timestamp": 1754931467176, "changes": [{"type": "MODIFY", "lineNumber": 0, "content": "", "oldContent": "                                                                                                                                             "}, {"type": "INSERT", "lineNumber": 1, "content": ""}, {"type": "INSERT", "lineNumber": 2, "content": ""}, {"type": "INSERT", "lineNumber": 3, "content": ""}, {"type": "INSERT", "lineNumber": 4, "content": ""}, {"type": "INSERT", "lineNumber": 5, "content": ""}, {"type": "INSERT", "lineNumber": 6, "content": ""}, {"type": "INSERT", "lineNumber": 7, "content": ""}, {"type": "INSERT", "lineNumber": 8, "content": ""}, {"type": "INSERT", "lineNumber": 9, "content": ""}, {"type": "INSERT", "lineNumber": 10, "content": ""}, {"type": "INSERT", "lineNumber": 11, "content": ""}, {"type": "INSERT", "lineNumber": 12, "content": ""}, {"type": "INSERT", "lineNumber": 13, "content": ""}, {"type": "INSERT", "lineNumber": 14, "content": ""}, {"type": "INSERT", "lineNumber": 15, "content": ""}]}, {"timestamp": 1754931473341, "changes": [{"type": "INSERT", "lineNumber": 0, "content": "xrgouda@Amrs-MacBook-Air AFA_app % "}, {"type": "DELETE", "lineNumber": 12, "oldContent": ""}, {"type": "DELETE", "lineNumber": 13, "oldContent": ""}, {"type": "DELETE", "lineNumber": 14, "oldContent": ""}, {"type": "DELETE", "lineNumber": 15, "oldContent": ""}]}, {"timestamp": 1754931482023, "changes": [{"type": "MODIFY", "lineNumber": 0, "content": "xrgouda@Amrs-MacBook-Air AFA_app % flutter build appbundle --release", "oldContent": "xrgouda@Amrs-MacBook-Air AFA_app % "}, {"type": "INSERT", "lineNumber": 13, "content": ""}]}, {"timestamp": 1754931568535, "changes": [{"type": "INSERT", "lineNumber": 1, "content": "Font asset \"MaterialIcons-Regular.otf\" was tree-shaken, reducing it from 1645184 to 4716 bytes (99.7% reduction). Tree-shaking can be disabled by providing the --no-tree-shake-icons flag when building your app."}, {"type": "INSERT", "lineNumber": 2, "content": "Running Gradle task 'bundleRelease'...                             61.6s"}, {"type": "INSERT", "lineNumber": 3, "content": "✓ Built build/app/outputs/bundle/release/app-release.aab (47.3MB)"}, {"type": "INSERT", "lineNumber": 4, "content": "xrgouda@Amrs-MacBook-Air AFA_app % "}, {"type": "DELETE", "lineNumber": 9, "oldContent": ""}, {"type": "DELETE", "lineNumber": 10, "oldContent": ""}, {"type": "DELETE", "lineNumber": 11, "oldContent": ""}, {"type": "DELETE", "lineNumber": 12, "oldContent": ""}, {"type": "DELETE", "lineNumber": 13, "oldContent": ""}]}, {"timestamp": 1755083768549, "changes": [{"type": "DELETE", "lineNumber": 0, "oldContent": "xrgouda@Amrs-MacBook-Air AFA_app % flutter build appbundle --release"}, {"type": "DELETE", "lineNumber": 1, "oldContent": "Font asset \"MaterialIcons-Regular.otf\" was tree-shaken, reducing it from 1645184 to 4716 bytes (99.7% reduction). Tree-shaking can be disabled by providing the --no-tree-shake-icons flag when building your app."}, {"type": "INSERT", "lineNumber": 0, "content": "xrgouda@Amrs-MacBook-Air AFA_app % "}, {"type": "DELETE", "lineNumber": 3, "oldContent": "Running Gradle task 'bundleRelease'...                             61.6s"}, {"type": "DELETE", "lineNumber": 5, "oldContent": "✓ Built build/app/outputs/bundle/release/app-release.aab (47.3MB)"}, {"type": "DELETE", "lineNumber": 7, "oldContent": "xrgouda@Amrs-MacBook-Air AFA_app % "}, {"type": "INSERT", "lineNumber": 9, "content": ""}, {"type": "INSERT", "lineNumber": 10, "content": ""}, {"type": "INSERT", "lineNumber": 11, "content": ""}, {"type": "INSERT", "lineNumber": 12, "content": ""}, {"type": "INSERT", "lineNumber": 13, "content": ""}]}, {"timestamp": 1755093616857, "changes": [{"type": "MODIFY", "lineNumber": 0, "content": "xrgouda@Amrs-MacBook-Air AFA_app % flutter apk --release ", "oldContent": "xrgouda@Amrs-MacBook-Air AFA_app % "}, {"type": "INSERT", "lineNumber": 14, "content": ""}, {"type": "INSERT", "lineNumber": 15, "content": ""}, {"type": "INSERT", "lineNumber": 16, "content": ""}, {"type": "INSERT", "lineNumber": 17, "content": ""}, {"type": "INSERT", "lineNumber": 18, "content": ""}, {"type": "INSERT", "lineNumber": 19, "content": ""}, {"type": "INSERT", "lineNumber": 20, "content": ""}]}, {"timestamp": 1755093664669, "changes": [{"type": "MODIFY", "lineNumber": 0, "content": "xrgouda@Amrs-MacBook-Air AFA_app % flutter build apk --release", "oldContent": "xrgouda@Amrs-MacBook-Air AFA_app % flutter apk --release "}]}, {"timestamp": 1755094490921, "changes": [{"type": "MODIFY", "lineNumber": 0, "content": "xrgouda@Amrs-MacBook-Air AFA_app % ", "oldContent": "xrgouda@Amrs-MacBook-Air AFA_app % flutter build apk --release"}, {"type": "DELETE", "lineNumber": 8, "oldContent": ""}, {"type": "DELETE", "lineNumber": 9, "oldContent": ""}, {"type": "DELETE", "lineNumber": 10, "oldContent": ""}, {"type": "DELETE", "lineNumber": 11, "oldContent": ""}, {"type": "DELETE", "lineNumber": 12, "oldContent": ""}, {"type": "DELETE", "lineNumber": 13, "oldContent": ""}, {"type": "DELETE", "lineNumber": 14, "oldContent": ""}, {"type": "DELETE", "lineNumber": 15, "oldContent": ""}, {"type": "DELETE", "lineNumber": 16, "oldContent": ""}, {"type": "DELETE", "lineNumber": 17, "oldContent": ""}, {"type": "DELETE", "lineNumber": 18, "oldContent": ""}, {"type": "DELETE", "lineNumber": 19, "oldContent": ""}, {"type": "DELETE", "lineNumber": 20, "oldContent": ""}]}, {"timestamp": 1755094572923, "changes": [{"type": "INSERT", "lineNumber": 8, "content": ""}, {"type": "INSERT", "lineNumber": 9, "content": ""}, {"type": "INSERT", "lineNumber": 10, "content": ""}, {"type": "INSERT", "lineNumber": 11, "content": ""}, {"type": "INSERT", "lineNumber": 12, "content": ""}, {"type": "INSERT", "lineNumber": 13, "content": ""}, {"type": "INSERT", "lineNumber": 14, "content": ""}, {"type": "INSERT", "lineNumber": 15, "content": ""}, {"type": "INSERT", "lineNumber": 16, "content": ""}, {"type": "INSERT", "lineNumber": 17, "content": ""}, {"type": "INSERT", "lineNumber": 18, "content": ""}, {"type": "INSERT", "lineNumber": 19, "content": ""}, {"type": "INSERT", "lineNumber": 20, "content": ""}, {"type": "INSERT", "lineNumber": 21, "content": ""}]}, {"timestamp": 1755094577022, "changes": [{"type": "MODIFY", "lineNumber": 0, "content": "xrgouda@Amrs-MacBook-Air AFA_app % flutter build apk --release", "oldContent": "xrgouda@Amrs-MacBook-Air AFA_app % "}]}, {"timestamp": 1755096483503, "changes": [{"type": "INSERT", "lineNumber": 1, "content": "e: The daemon has terminated unexpectedly on startup attempt #1 with error code: 0. The daemon process output:"}, {"type": "INSERT", "lineNumber": 2, "content": "    1. <PERSON><PERSON><PERSON> compile daemon is ready"}, {"type": "INSERT", "lineNumber": 3, "content": "Font asset \"MaterialIcons-Regular.otf\" was tree-shaken, reducing it from 1645184 to 4828 bytes (99.7% reduction). Tree-shaking can be disabled by providing the --no-tree-shake-icons flag when building your app."}, {"type": "INSERT", "lineNumber": 4, "content": "Running Gradle task 'assembleRelease'...                         1900.3s"}, {"type": "INSERT", "lineNumber": 5, "content": "✓ Built build/app/outputs/flutter-apk/app-release.apk (57.4MB)"}, {"type": "INSERT", "lineNumber": 6, "content": "xrgouda@Amrs-MacBook-Air AFA_app % "}, {"type": "DELETE", "lineNumber": 15, "oldContent": ""}, {"type": "DELETE", "lineNumber": 16, "oldContent": ""}, {"type": "DELETE", "lineNumber": 17, "oldContent": ""}, {"type": "DELETE", "lineNumber": 18, "oldContent": ""}, {"type": "DELETE", "lineNumber": 19, "oldContent": ""}, {"type": "DELETE", "lineNumber": 20, "oldContent": ""}, {"type": "DELETE", "lineNumber": 21, "oldContent": ""}]}, {"timestamp": 1755099762762, "changes": [{"type": "DELETE", "lineNumber": 0, "oldContent": "xrgouda@Amrs-MacBook-Air AFA_app % flutter build apk --release"}, {"type": "DELETE", "lineNumber": 1, "oldContent": "e: The daemon has terminated unexpectedly on startup attempt #1 with error code: 0. The daemon process output:"}, {"type": "INSERT", "lineNumber": 0, "content": "xrgouda@Amrs-MacBook-Air AFA_app % "}, {"type": "DELETE", "lineNumber": 3, "oldContent": "    1. <PERSON><PERSON><PERSON> compile daemon is ready"}, {"type": "DELETE", "lineNumber": 5, "oldContent": "Font asset \"MaterialIcons-Regular.otf\" was tree-shaken, reducing it from 1645184 to 4828 bytes (99.7% reduction). Tree-shaking can be disabled by providing the --no-tree-shake-icons flag when building your app."}, {"type": "DELETE", "lineNumber": 7, "oldContent": "Running Gradle task 'assembleRelease'...                         1900.3s"}, {"type": "DELETE", "lineNumber": 9, "oldContent": "✓ Built build/app/outputs/flutter-apk/app-release.apk (57.4MB)"}, {"type": "DELETE", "lineNumber": 11, "oldContent": "xrgouda@Amrs-MacBook-Air AFA_app % "}, {"type": "INSERT", "lineNumber": 15, "content": ""}, {"type": "INSERT", "lineNumber": 16, "content": ""}, {"type": "INSERT", "lineNumber": 17, "content": ""}, {"type": "INSERT", "lineNumber": 18, "content": ""}, {"type": "INSERT", "lineNumber": 19, "content": ""}, {"type": "INSERT", "lineNumber": 20, "content": ""}, {"type": "INSERT", "lineNumber": 21, "content": ""}]}]}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/main.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/main.dart", "baseContent": "import 'dart:io';\nimport 'package:afa_app/app_config/routes.dart';\nimport 'package:firebase_core/firebase_core.dart';\nimport 'package:flutter/material.dart';\nimport 'package:flutter/services.dart';\nimport 'package:flutter_riverpod/flutter_riverpod.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:riverpod_context/riverpod_context.dart';\n\nFuture<void> main() async {\n  WidgetsFlutterBinding.ensureInitialized();\n  await Firebase.initializeApp(\n    options: FirebaseOptions(\n      apiKey: Platform.isAndroid\n          ? \"AIzaSyCE9JqVCG_Whw3SY9pQ0S31GGPI2h1ajyc\"\n          : \"AIzaSyB5Ofub3ytPQElCglUR49t3kMdfsgL3xus\",\n      appId: Platform.isAndroid\n          ? \"1:686384903399:android:1ed0ac4fd0502ce2145e42\"\n          : \"1:686384903399:ios:b8d3ee2d026ce2ed145e42\",\n      messagingSenderId: \"686384903399\",\n      projectId: \"arab-fertilizer\",\n    ),\n  );\n\n  runApp(const ProviderScope(child: InheritedConsumer(child: AfaApp())));\n}\n\nclass AfaApp extends StatefulWidget {\n  const AfaApp({super.key});\n\n  @override\n  State<AfaApp> createState() => _AfaAppState();\n}\n\nclass _AfaAppState extends State<AfaApp> {\n  @override\n  Widget build(BuildContext context) {\n    SystemChrome.setPreferredOrientations([\n      DeviceOrientation.portraitUp,\n      DeviceOrientation.portraitDown,\n    ]);\n    debugInvertOversizedImages = true;\n    return ScreenUtilInit(\n      designSize: const Size(430, 932),\n      minTextAdapt: true,\n      builder: (context, child) => MaterialApp(\n        debugShowCheckedModeBanner: false,\n        theme: ThemeData(\n          fontFamily: \"Roboto\",\n          scaffoldBackgroundColor: Colors.white,\n        ),\n        initialRoute: PATHS.splashScreen,\n        routes: routes,\n      ),\n    );\n  }\n}\n", "baseTimestamp": 1754858823245, "deltas": [{"timestamp": 1755084749532, "changes": [{"type": "INSERT", "lineNumber": 23, "content": "  "}, {"type": "INSERT", "lineNumber": 24, "content": "  "}]}, {"timestamp": 1755084751739, "changes": [{"type": "DELETE", "lineNumber": 23, "oldContent": "  "}, {"type": "DELETE", "lineNumber": 25, "oldContent": "  "}]}, {"timestamp": 1755088471715, "changes": [{"type": "INSERT", "lineNumber": 9, "content": "import 'notification_handler.dart';"}, {"type": "INSERT", "lineNumber": 10, "content": ""}, {"type": "INSERT", "lineNumber": 26, "content": "  NotificationHandler.init();"}, {"type": "INSERT", "lineNumber": 27, "content": "  "}, {"type": "INSERT", "lineNumber": 28, "content": ""}]}, {"timestamp": 1755088478141, "changes": [{"type": "MODIFY", "lineNumber": 11, "content": "Future<void> main() async {", "oldContent": "Future<void> main() async {"}, {"type": "INSERT", "lineNumber": 26, "content": "  await NotificationHandler.init();"}, {"type": "INSERT", "lineNumber": 27, "content": "  "}, {"type": "INSERT", "lineNumber": 28, "content": ""}, {"type": "DELETE", "lineNumber": 28, "oldContent": "  NotificationHandler.init();"}, {"type": "DELETE", "lineNumber": 30, "oldContent": "  "}, {"type": "DELETE", "lineNumber": 32, "oldContent": ""}]}, {"timestamp": 1755088491541, "changes": [{"type": "INSERT", "lineNumber": 10, "content": ""}, {"type": "DELETE", "lineNumber": 11, "oldContent": "Future<void> main() async {"}, {"type": "DELETE", "lineNumber": 26, "oldContent": "  await NotificationHandler.init();"}, {"type": "DELETE", "lineNumber": 27, "oldContent": "  runApp(const ProviderScope(child: InheritedConsumer(child: <PERSON><PERSON><PERSON><PERSON>())));"}, {"type": "INSERT", "lineNumber": 26, "content": "  try {"}, {"type": "INSERT", "lineNumber": 27, "content": "    await NotificationHandler.init();"}, {"type": "INSERT", "lineNumber": 28, "content": "  } "}, {"type": "INSERT", "lineNumber": 30, "content": ""}, {"type": "INSERT", "lineNumber": 31, "content": "  runApp(const ProviderScope(child: InheritedConsumer(child: <PERSON><PERSON><PERSON><PERSON>())));"}, {"type": "DELETE", "lineNumber": 31, "oldContent": ""}]}, {"timestamp": 1755088493712, "changes": [{"type": "MODIFY", "lineNumber": 28, "content": "  } catch (e) {", "oldContent": "  } "}, {"type": "INSERT", "lineNumber": 29, "content": "    debugPrint(\"NotificationHandler.init() error::=>$e\");"}, {"type": "INSERT", "lineNumber": 30, "content": "  }"}, {"type": "DELETE", "lineNumber": 30, "oldContent": "}"}, {"type": "DELETE", "lineNumber": 32, "oldContent": ""}, {"type": "INSERT", "lineNumber": 34, "content": "}"}, {"type": "INSERT", "lineNumber": 35, "content": ""}]}, {"timestamp": 1755088496255, "changes": [{"type": "DELETE", "lineNumber": 30, "oldContent": "  "}, {"type": "MODIFY", "lineNumber": 35, "content": "class AfaApp extends StatefulWidget {", "oldContent": "class AfaApp extends StatefulWidget {"}]}]}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/notification_handler.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/notification_handler.dart", "baseContent": "import 'package:firebase_messaging/firebase_messaging.dart';\nimport 'package:flutter_local_notifications/flutter_local_notifications.dart';\n\nclass NotificationHandler {\n  static final _notification = FlutterLocalNotificationsPlugin();\n  static void init() async {\n    await FirebaseMessaging.instance.requestPermission();\n    _notification.initialize(\n      const InitializationSettings(\n        android: AndroidInitializationSettings('@mipmap/ic_launcher'),\n        iOS: DarwinInitializationSettings(),\n      ),\n      onDidReceiveNotificationResponse: (data) {},\n    );\n  }\n\n  static Future<void> pushNotification(RemoteMessage message) async {\n    const androidNotificationDetails = AndroidNotificationDetails(\n      \"high_importance_channelss\",\n      'High Importance Notifications',\n      channelDescription: 'channel description',\n      importance: Importance.max,\n      priority: Priority.high,\n      enableVibration: true,\n      playSound: true,\n    );\n\n    const darwinNotificationDetails = DarwinNotificationDetails(\n      presentAlert: true,\n      presentBadge: true,\n      presentSound: true,\n    );\n\n    const notificationDetails = NotificationDetails(\n      android: androidNotificationDetails,\n      iOS: darwinNotificationDetails,\n    );\n\n    await _notification.show(\n      0,\n      message.notification!.title,\n      message.notification!.body,\n      notificationDetails,\n    );\n  }\n}\n", "baseTimestamp": 1754858823277, "deltas": [{"timestamp": 1755088482989, "changes": [{"type": "MODIFY", "lineNumber": 5, "content": "  static Future<void> init() async {", "oldContent": "  static void init() async {"}]}]}, "/Users/<USER>/Flutter-Projects/AFA_app/pubspec.yaml": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/pubspec.yaml", "baseContent": "name: afa_app\ndescription: \"A new Flutter project.\"\n# The following line prevents the package from being accidentally published to\n# pub.dev using `flutter pub publish`. This is preferred for private packages.\npublish_to: 'none' # Remove this line if you wish to publish to pub.dev\n\n# The following defines the version and build number for your application.\n# A version number is three numbers separated by dots, like 1.2.43\n# followed by an optional build number separated by a +.\n# Both the version and the builder number may be overridden in flutter\n# build by specifying --build-name and --build-number, respectively.\n# In Android, build-name is used as versionName while build-number used as versionCode.\n# Read more about Android versioning at https://developer.android.com/studio/publish/versioning\n# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.\n# Read more about iOS versioning at\n# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html\n# In Windows, build-name is used as the major, minor, and patch parts\n# of the product and file versions while build-number is used as the build suffix.\nversion: 4.0.0+4\n\nenvironment:\n  sdk: ^3.8.1\n\n# Dependencies specify other packages that your package needs in order to work.\n# To automatically upgrade your package dependencies to the latest versions\n# consider running `flutter pub upgrade --major-versions`. Alternatively,\n# dependencies can be manually updated by changing the version numbers below to\n# the latest version available on pub.dev. To see which dependencies have newer\n# versions available, run `flutter pub outdated`.\ndependencies:\n  \n\n  # The following adds the Cupertino Icons font to your application.\n  # Use with the CupertinoIcons class for iOS style icons.\n  \n  badges: ^3.1.2\n  cached_network_image: ^3.4.1\n  connectivity_plus: ^6.1.4\n  cupertino_icons: ^1.0.8\n  dropdown_search: ^6.0.2\n  firebase_core: ^3.15.2\n  firebase_messaging: ^15.2.10\n  flutter:\n    sdk: flutter\n  flutter_lints: ^6.0.0\n  flutter_local_notifications: ^19.3.1\n  flutter_polls: ^0.1.6\n  flutter_riverpod: ^2.6.1\n  flutter_screenutil: ^5.9.3\n  google_maps_flutter: ^2.12.3\n  http: ^1.4.0\n  image_picker: ^1.1.2\n  intl: ^0.20.2\n  permission_handler: ^12.0.1\n  pin_code_fields: ^8.0.1\n  pull_to_refresh: ^2.0.0\n  riverpod_context: ^0.3.0\n  saver_gallery: ^4.0.1\n  shared_preferences: ^2.5.3\n  uid: ^0.0.2\n  url_launcher: ^6.3.1\n  \n\n\n  \n  \n  \n\ndev_dependencies:\n  flutter_test:\n    sdk: flutter\n\n  # The \"flutter_lints\" package below contains a set of recommended lints to\n  # encourage good coding practices. The lint set provided by the package is\n  # activated in the `analysis_options.yaml` file located at the root of your\n  # package. See that file for information about deactivating specific lint\n  # rules and activating additional ones.\n  \n\n# For information on the generic Dart part of this file, see the\n# following page: https://dart.dev/tools/pub/pubspec\n\n# The following section is specific to Flutter packages.\nflutter:\n\n  # The following line ensures that the Material Icons font is\n  # included with your application, so that you can use the icons in\n  # the material Icons class.\n  uses-material-design: true\n\n  # To add assets to your application, add an assets section, like this:\n  assets:\n    - assets/images/\n  #   - images/a_dot_ham.jpeg\n\n  # An image asset can refer to one or more resolution-specific \"variants\", see\n  # https://flutter.dev/to/resolution-aware-images\n\n  # For details regarding adding assets from package dependencies, see\n  # https://flutter.dev/to/asset-from-package\n\n  # To add custom fonts to your application, add a fonts section here,\n  # in this \"flutter\" section. Each entry in this list should have a\n  # \"family\" key with the font family name, and a \"fonts\" key with a\n  # list giving the asset and other descriptors for the font. For\n  # example:\n  fonts:\n    - family: Roboto\n      fonts:\n        - asset: assets/font/Roboto-Regular.ttf\n        - asset: assets/font/Roboto-Bold.ttf\n          weight: 500\n  #   - family: Trajan Pro\n  #     fonts:\n  #       - asset: fonts/TrajanPro.ttf\n  #       - asset: fonts/TrajanPro_Bold.ttf\n  #         weight: 700\n  #\n  # For details regarding fonts from package dependencies,\n  # see https://flutter.dev/to/font-from-package\n", "baseTimestamp": 1754858828779, "deltas": [{"timestamp": 1754858859367, "changes": [{"type": "MODIFY", "lineNumber": 18, "content": "version: 1.16.5+165", "oldContent": "version: 4.0.0+4"}]}, {"timestamp": 1754858864593, "changes": [{"type": "MODIFY", "lineNumber": 18, "content": "version: 1.17.5+165", "oldContent": "version: 1.16.5+165"}]}, {"timestamp": 1754858866935, "changes": [{"type": "MODIFY", "lineNumber": 18, "content": "version: 1.17.0+165", "oldContent": "version: 1.17.5+165"}]}, {"timestamp": 1754858871240, "changes": [{"type": "MODIFY", "lineNumber": 18, "content": "version: 1.17.0+170", "oldContent": "version: 1.17.0+165"}]}, {"timestamp": 1754929646952, "changes": [{"type": "MODIFY", "lineNumber": 18, "content": "version: 1.17.1+171", "oldContent": "version: 1.17.0+170"}]}, {"timestamp": 1755089240330, "changes": [{"type": "INSERT", "lineNumber": 39, "content": "  collection:"}]}]}, "/Dummy.txt": {"filePath": "/Dummy.txt", "baseContent": "Updates", "baseTimestamp": 1754858830618}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/screens/home_screens/home_screen.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/screens/home_screens/home_screen.dart", "baseContent": "import 'dart:async';\nimport 'package:afa_app/app_config/app_colors.dart';\nimport 'package:afa_app/app_config/common_components.dart';\nimport 'package:afa_app/widgets/home_screens_widgets.dart/home_screen_widgets.dart';\nimport 'package:flutter/material.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\n\nclass HomeScreen extends StatefulWidget {\n  const HomeScreen({super.key});\n\n  @override\n  State<HomeScreen> createState() => _HomeScreenState();\n}\n\nclass _HomeScreenState extends State<HomeScreen> with WidgetsBindingObserver {\n  Timer? timer;\n  Duration difference = const Duration();\n  bool _isTimerActive = false;\n\n  @override\n  void initState() {\n    super.initState();\n    WidgetsBinding.instance.addObserver(this);\n    _startTimer();\n  }\n\n  @override\n  void didChangeAppLifecycleState(AppLifecycleState state) {\n    super.didChangeAppLifecycleState(state);\n    switch (state) {\n      case AppLifecycleState.resumed:\n        if (!_isTimerActive) {\n          _startTimer();\n        }\n        break;\n      case AppLifecycleState.paused:\n      case AppLifecycleState.inactive:\n        _stopTimer();\n        break;\n      case AppLifecycleState.detached:\n        _stopTimer();\n        break;\n      case AppLifecycleState.hidden:\n        _stopTimer();\n        break;\n    }\n  }\n\n  void _startTimer() {\n    if (_isTimerActive) return;\n\n    _isTimerActive = true;\n    timer = Timer.periodic(const Duration(seconds: 1), (timer) {\n      if (!mounted) {\n        _stopTimer();\n        return;\n      }\n\n      try {\n        final now = DateTime.now();\n        // Target date: September 16th, 2024 at 12:00 PM\n        final targetDate = DateTime(2024, 9, 16, 12, 0, 0);\n\n        final newDifference = targetDate.difference(now);\n\n        if (mounted) {\n          setState(() {\n            difference = newDifference;\n          });\n        }\n      } catch (e) {\n        // Handle any errors in timer calculation\n        debugPrint('Timer calculation error: $e');\n        if (mounted) {\n          setState(() {\n            difference = const Duration();\n          });\n        }\n      }\n    });\n  }\n\n  void _stopTimer() {\n    if (timer != null && timer!.isActive) {\n      timer!.cancel();\n    }\n    _isTimerActive = false;\n  }\n\n  List<Map<String, dynamic>> formatDuration(Duration d) {\n    // Handle negative durations (when event has passed)\n    final isNegative = d.isNegative;\n    final absoluteDuration = isNegative ? -d : d;\n\n    final days = absoluteDuration.inDays;\n    final hours = absoluteDuration.inHours % 24;\n    final minutes = absoluteDuration.inMinutes % 60;\n    final seconds = absoluteDuration.inSeconds % 60;\n\n    final List<Map<String, dynamic>> eventTimer = [\n      {\"time\": isNegative ? 0 : days, \"title\": \"Days\"},\n      {\"time\": isNegative ? 0 : hours, \"title\": \"Hours\"},\n      {\"time\": isNegative ? 0 : minutes, \"title\": \"Minutes\"},\n      {\"time\": isNegative ? 0 : seconds, \"title\": \"Seconds\"},\n    ];\n    return eventTimer;\n  }\n\n  @override\n  void dispose() {\n    WidgetsBinding.instance.removeObserver(this);\n    _stopTimer();\n    super.dispose();\n  }\n\n  @override\n  Widget build(BuildContext context) {\n    return Column(\n      crossAxisAlignment: CrossAxisAlignment.start,\n      children: [\n        Center(\n          child: HomeScreenWidgets.headerScreenWidget(\n            context: context,\n            eventTimer: formatDuration(difference!),\n          ),\n        ),\n\n        SizedBox(height: 35.0.h),\n        GridView.builder(\n          padding: EdgeInsets.all(10.0.h),\n          shrinkWrap: true,\n          itemCount: HomeScreenWidgets.homFiledsList.length,\n          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(\n            crossAxisCount: 4,\n            mainAxisExtent: 100.0.h,\n            mainAxisSpacing: 20.0.h,\n          ),\n\n          itemBuilder: (context, index) => InkWell(\n            onTap: () {\n              Navigator.pushNamed(\n                context,\n                HomeScreenWidgets.homFiledsList[index]['path'],\n              );\n            },\n            child: Center(\n              child: Column(\n                children: [\n                  Container(\n                    padding: EdgeInsets.all(15.0.h),\n                    decoration: const BoxDecoration(\n                      shape: BoxShape.circle,\n                      color: AppColors.darkGreenColor,\n                    ),\n                    child: CommonComponents.imageAssetWithCache(\n                      context: context,\n                      image: HomeScreenWidgets.homFiledsList[index]['image'],\n                      height: 36.0.h,\n                      width: 36.0.w,\n                      fit: BoxFit.contain,\n                    ),\n                  ),\n                  SizedBox(height: 10.0.h),\n                  Text(\n                    HomeScreenWidgets.homFiledsList[index]['title'],\n                    style: TextStyle(\n                      fontSize: 12.0.sp,\n                      fontWeight: FontWeight.bold,\n                      color: AppColors.blackColor,\n                    ),\n                  ),\n                ],\n              ),\n            ),\n          ),\n        ),\n      ],\n    );\n  }\n}\n", "baseTimestamp": 1754859876966, "deltas": [{"timestamp": 1754859887174, "changes": [{"type": "MODIFY", "lineNumber": 123, "content": "            eventTimer: formatDuration(difference),", "oldContent": "            eventTimer: formatDuration(difference!),"}]}, {"timestamp": 1754860186364, "changes": [{"type": "MODIFY", "lineNumber": 14, "content": "class _HomeScreenState extends State<HomeScreen> {", "oldContent": "class _HomeScreenState extends State<HomeScreen> with WidgetsBindingObserver {"}, {"type": "DELETE", "lineNumber": 16, "oldContent": "  Duration difference = const Duration();"}, {"type": "DELETE", "lineNumber": 17, "oldContent": "  bool _isTimerActive = false;"}, {"type": "INSERT", "lineNumber": 16, "content": "  Duration? difference = const Duration();"}, {"type": "DELETE", "lineNumber": 21, "oldContent": "    super.initState();"}, {"type": "DELETE", "lineNumber": 22, "oldContent": "    WidgetsBinding.instance.addObserver(this);"}, {"type": "INSERT", "lineNumber": 21, "content": "    super.initState();"}, {"type": "DELETE", "lineNumber": 26, "oldContent": "  @override"}, {"type": "DELETE", "lineNumber": 27, "oldContent": "  void didChangeAppLifecycleState(AppLifecycleState state) {"}, {"type": "DELETE", "lineNumber": 28, "oldContent": "    super.didChangeAppLifecycleState(state);"}, {"type": "DELETE", "lineNumber": 29, "oldContent": "    switch (state) {"}, {"type": "DELETE", "lineNumber": 30, "oldContent": "      case AppLifecycleState.resumed:"}, {"type": "DELETE", "lineNumber": 31, "oldContent": "        if (!_isTimerActive) {"}, {"type": "DELETE", "lineNumber": 32, "oldContent": "          _startTimer();"}, {"type": "DELETE", "lineNumber": 33, "oldContent": "        }"}, {"type": "DELETE", "lineNumber": 34, "oldContent": "        break;"}, {"type": "DELETE", "lineNumber": 35, "oldContent": "      case AppLifecycleState.paused:"}, {"type": "DELETE", "lineNumber": 36, "oldContent": "      case AppLifecycleState.inactive:"}, {"type": "DELETE", "lineNumber": 37, "oldContent": "        _stopTimer();"}, {"type": "DELETE", "lineNumber": 38, "oldContent": "        break;"}, {"type": "DELETE", "lineNumber": 39, "oldContent": "      case AppLifecycleState.detached:"}, {"type": "DELETE", "lineNumber": 40, "oldContent": "        _stopTimer();"}, {"type": "DELETE", "lineNumber": 41, "oldContent": "        break;"}, {"type": "DELETE", "lineNumber": 42, "oldContent": "      case AppLifecycleState.hidden:"}, {"type": "DELETE", "lineNumber": 43, "oldContent": "        _stopTimer();"}, {"type": "DELETE", "lineNumber": 44, "oldContent": "        break;"}, {"type": "DELETE", "lineNumber": 45, "oldContent": "    }"}, {"type": "DELETE", "lineNumber": 46, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 47, "oldContent": ""}, {"type": "DELETE", "lineNumber": 49, "oldContent": "    if (_isTimerActive) return;"}, {"type": "INSERT", "lineNumber": 25, "content": "    timer = Timer.periodic(const Duration(seconds: 1), (time) {"}, {"type": "INSERT", "lineNumber": 26, "content": "      final now = DateTime.now();"}, {"type": "DELETE", "lineNumber": 51, "oldContent": "    _isTimerActive = true;"}, {"type": "DELETE", "lineNumber": 52, "oldContent": "    timer = Timer.periodic(const Duration(seconds: 1), (timer) {"}, {"type": "DELETE", "lineNumber": 53, "oldContent": "      if (!mounted) {"}, {"type": "DELETE", "lineNumber": 54, "oldContent": "        _stopTimer();"}, {"type": "DELETE", "lineNumber": 55, "oldContent": "        return;"}, {"type": "DELETE", "lineNumber": 56, "oldContent": "      }"}, {"type": "INSERT", "lineNumber": 28, "content": "      final targetDate = DateTime(now.year, 9, 16, 12, 0, 0);"}, {"type": "DELETE", "lineNumber": 58, "oldContent": "      try {"}, {"type": "DELETE", "lineNumber": 59, "oldContent": "        final now = DateTime.now();"}, {"type": "DELETE", "lineNumber": 60, "oldContent": "        // Target date: September 16th, 2024 at 12:00 PM"}, {"type": "DELETE", "lineNumber": 61, "oldContent": "        final targetDate = DateTime(2024, 9, 16, 12, 0, 0);"}, {"type": "DELETE", "lineNumber": 62, "oldContent": ""}, {"type": "DELETE", "lineNumber": 63, "oldContent": "        final newDifference = targetDate.difference(now);"}, {"type": "DELETE", "lineNumber": 64, "oldContent": ""}, {"type": "DELETE", "lineNumber": 65, "oldContent": "        if (mounted) {"}, {"type": "DELETE", "lineNumber": 66, "oldContent": "          setState(() {"}, {"type": "DELETE", "lineNumber": 67, "oldContent": "            difference = newDifference;"}, {"type": "DELETE", "lineNumber": 68, "oldContent": "          });"}, {"type": "DELETE", "lineNumber": 69, "oldContent": "        }"}, {"type": "DELETE", "lineNumber": 70, "oldContent": "      } catch (e) {"}, {"type": "DELETE", "lineNumber": 71, "oldContent": "        // Handle any errors in timer calculation"}, {"type": "DELETE", "lineNumber": 72, "oldContent": "        debugPrint('Timer calculation error: $e');"}, {"type": "DELETE", "lineNumber": 73, "oldContent": "        if (mounted) {"}, {"type": "DELETE", "lineNumber": 74, "oldContent": "          setState(() {"}, {"type": "DELETE", "lineNumber": 75, "oldContent": "            difference = const Duration();"}, {"type": "DELETE", "lineNumber": 76, "oldContent": "          });"}, {"type": "DELETE", "lineNumber": 77, "oldContent": "        }"}, {"type": "DELETE", "lineNumber": 78, "oldContent": "      }"}, {"type": "INSERT", "lineNumber": 30, "content": "      setState(() {"}, {"type": "INSERT", "lineNumber": 31, "content": "        difference = targetDate.difference(now);"}, {"type": "INSERT", "lineNumber": 32, "content": "      });"}, {"type": "DELETE", "lineNumber": 82, "oldContent": "  void _stopTimer() {"}, {"type": "DELETE", "lineNumber": 83, "oldContent": "    if (timer != null && timer!.isActive) {"}, {"type": "DELETE", "lineNumber": 84, "oldContent": "      timer!.cancel();"}, {"type": "DELETE", "lineNumber": 85, "oldContent": "    }"}, {"type": "DELETE", "lineNumber": 86, "oldContent": "    _isTimerActive = false;"}, {"type": "DELETE", "lineNumber": 87, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 88, "oldContent": ""}, {"type": "DELETE", "lineNumber": 90, "oldContent": "    // Handle negative durations (when event has passed)"}, {"type": "DELETE", "lineNumber": 91, "oldContent": "    final isNegative = d.isNegative;"}, {"type": "DELETE", "lineNumber": 92, "oldContent": "    final absoluteDuration = isNegative ? -d : d;"}, {"type": "INSERT", "lineNumber": 37, "content": "    final days = d.inDays;"}, {"type": "INSERT", "lineNumber": 38, "content": "    final hours = d.inDays % 24;"}, {"type": "INSERT", "lineNumber": 39, "content": "    final minutes = d.in<PERSON><PERSON> % 60;"}, {"type": "INSERT", "lineNumber": 40, "content": "    final seconds = d.in<PERSON><PERSON><PERSON><PERSON> % 60;"}, {"type": "DELETE", "lineNumber": 94, "oldContent": "    final days = absoluteDuration.inDays;"}, {"type": "DELETE", "lineNumber": 95, "oldContent": "    final hours = absoluteDuration.inHours % 24;"}, {"type": "DELETE", "lineNumber": 96, "oldContent": "    final minutes = absoluteDuration.inMinutes % 60;"}, {"type": "DELETE", "lineNumber": 97, "oldContent": "    final seconds = absoluteDuration.inSeconds % 60;"}, {"type": "DELETE", "lineNumber": 98, "oldContent": ""}, {"type": "DELETE", "lineNumber": 100, "oldContent": "      {\"time\": isNegative ? 0 : days, \"title\": \"Days\"},"}, {"type": "DELETE", "lineNumber": 101, "oldContent": "      {\"time\": isNegative ? 0 : hours, \"title\": \"Hours\"},"}, {"type": "DELETE", "lineNumber": 102, "oldContent": "      {\"time\": isNegative ? 0 : minutes, \"title\": \"Minutes\"},"}, {"type": "DELETE", "lineNumber": 103, "oldContent": "      {\"time\": isNegative ? 0 : seconds, \"title\": \"Seconds\"},"}, {"type": "INSERT", "lineNumber": 43, "content": "      {\"time\": days, \"title\": \"Days\"},"}, {"type": "INSERT", "lineNumber": 44, "content": "      {\"time\": hours - 6, \"title\": \"Hours\"},"}, {"type": "INSERT", "lineNumber": 45, "content": "      {\"time\": minutes, \"title\": \"Minute\"},"}, {"type": "INSERT", "lineNumber": 46, "content": "      {\"time\": seconds, \"title\": \"Seconds\"},"}, {"type": "DELETE", "lineNumber": 110, "oldContent": "    WidgetsBinding.instance.removeObserver(this);"}, {"type": "DELETE", "lineNumber": 111, "oldContent": "    _stopTimer();"}, {"type": "INSERT", "lineNumber": 53, "content": "    timer!.cancel();"}, {"type": "DELETE", "lineNumber": 123, "oldContent": "            eventTimer: formatDuration(difference),"}, {"type": "INSERT", "lineNumber": 65, "content": "            eventTimer: formatDuration(difference!),"}]}, {"timestamp": 1754860528638, "changes": [{"type": "MODIFY", "lineNumber": 21, "content": "    super.initState();", "oldContent": "    super.initState();"}, {"type": "DELETE", "lineNumber": 23, "oldContent": "    timer = Timer.periodic(const Duration(seconds: 1), (time) {"}, {"type": "INSERT", "lineNumber": 24, "content": "  void _startTimer() {"}, {"type": "INSERT", "lineNumber": 25, "content": "    timer = Timer.periodic(const Duration(seconds: 1), (time) {"}, {"type": "INSERT", "lineNumber": 27, "content": ""}, {"type": "INSERT", "lineNumber": 29, "content": ""}, {"type": "DELETE", "lineNumber": 30, "oldContent": "    final days = d.inDays;"}, {"type": "DELETE", "lineNumber": 31, "oldContent": "    final hours = d.inDays % 24;"}, {"type": "DELETE", "lineNumber": 32, "oldContent": "    final minutes = d.in<PERSON><PERSON> % 60;"}, {"type": "DELETE", "lineNumber": 33, "oldContent": "    final seconds = d.in<PERSON><PERSON><PERSON><PERSON> % 60;"}, {"type": "DELETE", "lineNumber": 34, "oldContent": "      {\"time\": days, \"title\": \"Days\"},"}, {"type": "DELETE", "lineNumber": 35, "oldContent": "      {\"time\": hours - 6, \"title\": \"Hours\"},"}, {"type": "DELETE", "lineNumber": 36, "oldContent": "      {\"time\": minutes, \"title\": \"Minute\"},"}, {"type": "DELETE", "lineNumber": 37, "oldContent": "      {\"time\": seconds, \"title\": \"Seconds\"},"}, {"type": "DELETE", "lineNumber": 38, "oldContent": "  void _startTimer() {"}, {"type": "DELETE", "lineNumber": 39, "oldContent": ""}, {"type": "DELETE", "lineNumber": 40, "oldContent": "    timer!.cancel();"}, {"type": "DELETE", "lineNumber": 41, "oldContent": ""}, {"type": "DELETE", "lineNumber": 42, "oldContent": "            eventTimer: formatDuration(difference!),"}, {"type": "INSERT", "lineNumber": 37, "content": "    final days = d.inDays;"}, {"type": "INSERT", "lineNumber": 38, "content": "    final hours = d.inHours % 24;"}, {"type": "INSERT", "lineNumber": 39, "content": "    final minutes = d.in<PERSON><PERSON> % 60;"}, {"type": "INSERT", "lineNumber": 40, "content": "    final seconds = d.in<PERSON><PERSON><PERSON><PERSON> % 60;"}, {"type": "INSERT", "lineNumber": 43, "content": "      {\"time\": days, \"title\": \"Days\"},"}, {"type": "INSERT", "lineNumber": 44, "content": "      {\"time\": hours, \"title\": \"Hours\"},"}, {"type": "INSERT", "lineNumber": 45, "content": "      {\"time\": minutes, \"title\": \"Minute\"},"}, {"type": "INSERT", "lineNumber": 46, "content": "      {\"time\": seconds, \"title\": \"Seconds\"},"}, {"type": "INSERT", "lineNumber": 53, "content": "    timer!.cancel();"}, {"type": "INSERT", "lineNumber": 65, "content": "            eventTimer: formatDuration(difference!),"}]}]}, "/Users/<USER>/Flutter-Projects/AFA_app/android/key.properties": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/android/key.properties", "baseContent": "storePassword=123456\nkeyPassword=123456\nkeyAlias=upload\nstoreFile=src/upload-keystore.jks\n", "baseTimestamp": 1754929605606, "deltas": [{"timestamp": 1754929610090, "changes": [{"type": "MODIFY", "lineNumber": 3, "content": "storeFile=upload-keystore.jks", "oldContent": "storeFile=src/upload-keystore.jks"}]}]}, "/Users/<USER>/Flutter-Projects/AFA_app/android/app/build.gradle": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/android/app/build.gradle", "baseContent": "plugins {\n    id \"com.android.application\"\n    id \"kotlin-android\"\n    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.\n    id \"dev.flutter.flutter-gradle-plugin\"\n    id 'com.google.gms.google-services'\n}\n\ndef localProperties = new Properties()\ndef localPropertiesFile = rootProject.file(\"local.properties\")\nif (localPropertiesFile.exists()) {\n    localPropertiesFile.withReader(\"UTF-8\") { reader ->\n        localProperties.load(reader)\n    }\n}\n\ndef flutterVersionCode = localProperties.getProperty(\"flutter.versionCode\")\nif (flutterVersionCode == null) {\n    flutterVersionCode = \"1\"\n}\n\ndef flutterVersionName = localProperties.getProperty(\"flutter.versionName\")\nif (flutterVersionName == null) {\n    flutterVersionName = \"1.0\"\n}\n\ndef keystoreProperties = new Properties()\ndef keystorePropertiesFile = rootProject.file('key.properties')\nif (keystorePropertiesFile.exists()) {\n    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))\n}\n\nandroid {\n    namespace = \"afa_app.com\"\n    compileSdk = 35\n    ndkVersion = \n\n    compileOptions {\n        coreLibraryDesugaringEnabled true\n        sourceCompatibility = JavaVersion.VERSION_17\n        targetCompatibility = JavaVersion.VERSION_17\n    }\n\n    kotlinOptions {\n        jvmTarget = '17'\n    }\n\n    sourceSets {\n        main.java.srcDirs += 'src/main/kotlin'\n    }\n\n    defaultConfig {\n        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).\n        applicationId = \"afa_app.com\"\n        // You can update the following values to match your application needs.\n        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.\n        minSdk = 23\n        targetSdk = 35\n        versionCode = flutterVersionCode.toInteger()\n        versionName = flutterVersionName\n    }\n\n    signingConfigs {\n        release {\n            keyAlias keystoreProperties['keyAlias']\n            keyPassword keystoreProperties['keyPassword']\n            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null\n            storePassword keystoreProperties['storePassword']\n        }\n    }\n    buildTypes {\n        release {\n            // TODO: Add your own signing config for the release build.\n            // Signing with the debug keys for now, so `flutter run --release` works.\n            signingConfig signingConfigs.release\n        }\n    }\n}\n\nflutter {\n    source = \"../..\"\n}\n\ndependencies {\n    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.4'\n    implementation \"org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.0\"\n    implementation platform('com.google.firebase:firebase-bom:34.0.0')\n    implementation 'com.google.firebase:firebase-analytics'\n}\n", "baseTimestamp": 1754930470757, "deltas": [{"timestamp": 1754930627825, "changes": [{"type": "MODIFY", "lineNumber": 35, "content": "    ndkVersion \"28.0.13004108\"", "oldContent": "    ndkVersion = "}]}, {"timestamp": 1754930630443, "changes": [{"type": "MODIFY", "lineNumber": 35, "content": "    ndkVersion = \"28.0.13004108\"", "oldContent": "    ndkVersion \"28.0.13004108\""}]}]}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/widgets/home_screens_widgets.dart/questions_and_answers_screens_widgets/session_questions_screen_widgets.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/widgets/home_screens_widgets.dart/questions_and_answers_screens_widgets/session_questions_screen_widgets.dart", "baseContent": "import 'package:afa_app/app_config/api_providers/home_screen_categories_providers.dart';\nimport 'package:afa_app/app_config/app_colors.dart';\nimport 'package:afa_app/app_config/app_images.dart';\nimport 'package:afa_app/app_config/common_components.dart';\nimport 'package:afa_app/models/home_screens_models/sessions_q&a_models/question_and_answers_speakers_model.dart';\nimport 'package:flutter/material.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:riverpod_context/riverpod_context.dart';\n\nclass SessionQuestionsScreenWidgets {\n  static Widget bubleDaysWidget({\n    required BuildContext context,\n    required String title,\n    required String subTitle,\n    required Color textColor,\n    required Color containerColor,\n  }) {\n    return Container(\n      padding: EdgeInsets.all(15.0.h),\n      alignment: Alignment.center,\n\n      decoration: BoxDecoration(shape: BoxShape.circle, color: containerColor),\n      child: Column(\n        mainAxisAlignment: MainAxisAlignment.center,\n        children: [\n          Text(\n            title,\n            style: TextStyle(\n              fontSize: 12.0.sp,\n              fontWeight: FontWeight.bold,\n              color: textColor,\n            ),\n          ),\n          Text(\n            subTitle,\n            style: TextStyle(\n              fontSize: 12.0.sp,\n              fontWeight: FontWeight.bold,\n              color: textColor,\n            ),\n          ),\n        ],\n      ),\n    );\n  }\n\n  static Widget showSpeakersListWidget({\n    required BuildContext context,\n    required String image,\n    required List<QandASpeakersModel> speakerList,\n  }) {\n    return Column(\n      crossAxisAlignment: CrossAxisAlignment.start,\n      children: [\n        Text(\n          \"Speakers\",\n          style: TextStyle(\n            fontSize: 12.0.sp,\n            fontWeight: FontWeight.bold,\n            color: AppColors.greyColor,\n          ),\n        ),\n        SizedBox(height: 5.0.h),\n        SizedBox(\n          height: 30.0.h,\n          width: speakerList.length * 32.0.w,\n          child: ListView.separated(\n            separatorBuilder: (context, index) => SizedBox(width: 2.0.w),\n            padding: EdgeInsets.zero,\n            scrollDirection: Axis.horizontal,\n            itemCount: speakerList.length,\n\n            itemBuilder: (context, index) => InkWell(\n              onTap: () {\n                // Navigator.pushNamed(\n                //   context,\n                //   PATHS.agendaDetailsSpeakerScreen,\n                //   arguments: AgendaDetailsSpeakerScreen(\n                //     speakerId: speakerList[index].speakerID,\n                //   ),\n                // );\n              },\n              child: ClipOval(\n                child: speakerList[index].speakerImage != null\n                    ? CommonComponents.imageWithNetworkCache(\n                        context: context,\n                        image: speakerList[index].speakerImage!,\n                        height: 24.0.h,\n                        width: 24.0.w,\n                        fit: BoxFit.contain,\n                      )\n                    : CommonComponents.imageAssetWithCache(\n                        context: context,\n                        image: AppImages.userPhoto,\n                        height: 30.0.h,\n                        width: 30.0.w,\n                        fit: BoxFit.contain,\n                      ),\n              ),\n            ),\n          ),\n        ),\n      ],\n    );\n  }\n\n  static Future showAddQuestionAlertWidget({\n    required BuildContext context,\n    required TextEditingController controller,\n    required GlobalKey<FormState> formKey,\n    required int sessionID,\n  }) async {\n    return await showGeneralDialog(\n      context: context,\n      pageBuilder: (context, animation1, animation2) => Container(),\n      transitionDuration: const Duration(milliseconds: 400),\n      transitionBuilder: (context, animation1, animation2, child) =>\n          ScaleTransition(\n            scale: animation1,\n            child: AlertDialog(\n              contentPadding: EdgeInsets.all(10.0.h),\n              shape: RoundedRectangleBorder(\n                borderRadius: BorderRadius.circular(20.0),\n              ),\n              backgroundColor: Colors.white,\n              title: Center(\n                child: Text(\n                  \"Add Question\",\n                  style: TextStyle(\n                    fontSize: 20.0.sp,\n                    fontWeight: FontWeight.bold,\n                    color: AppColors.blackColor,\n                  ),\n                ),\n              ),\n              content: Form(\n                key: formKey,\n                child: Column(\n                  crossAxisAlignment: CrossAxisAlignment.start,\n                  mainAxisSize: MainAxisSize.min,\n                  children: [\n                    Text(\n                      \"Question\",\n                      style: TextStyle(\n                        fontSize: 14.0.sp,\n                        fontWeight: FontWeight.bold,\n                      ),\n                    ),\n                    SizedBox(height: 5.0.h),\n                    TextFormField(\n                      controller: controller,\n                      validator: (value) =>\n                          value!.isEmpty ? \"Please Enter Your Question\" : null,\n                      maxLines: 3,\n                      style: TextStyle(\n                        fontSize: 12.0.sp,\n                        fontWeight: FontWeight.bold,\n                      ),\n                      decoration: InputDecoration(\n                        hintText: \"Enter\",\n                        hintStyle: TextStyle(\n                          fontSize: 12.0.sp,\n                          fontWeight: FontWeight.bold,\n                          color: AppColors.greyColor,\n                        ),\n                        border: OutlineInputBorder(\n                          borderSide: const BorderSide(\n                            color: AppColors.greyColor,\n                          ),\n                          borderRadius: BorderRadius.all(\n                            Radius.circular(16.0.r),\n                          ),\n                        ),\n                      ),\n                    ),\n                    SizedBox(height: 25.0.h),\n                    ElevatedButton(\n                      onPressed: () async {\n                        if (formKey.currentState!.validate()) {\n                          await context\n                              .read(\n                                HomeScreenCategoriesProviders\n                                    .questionAndAnswersProvidersApis,\n                              )\n                              .createQuestion(\n                                context: context,\n                                question: controller.text,\n                                sessionID: sessionID,\n                              );\n                        }\n                      },\n                      style: ElevatedButton.styleFrom(\n                        foregroundColor: Colors.white,\n                        backgroundColor: AppColors.midLevelGreenColor,\n                        textStyle: TextStyle(\n                          fontSize: 16.0.sp,\n                          fontWeight: FontWeight.bold,\n                        ),\n                        minimumSize: Size(398.0.w, 46.0.h),\n                      ),\n                      child: const Text(\"Send\"),\n                    ),\n                  ],\n                ),\n              ),\n            ),\n          ),\n    );\n  }\n}\n", "baseTimestamp": 1755084703010}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/widgets/home_screens_widgets.dart/questions_and_answers_screens_widgets/questions_screen_widgets.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/widgets/home_screens_widgets.dart/questions_and_answers_screens_widgets/questions_screen_widgets.dart", "baseContent": "import 'package:afa_app/app_config/app_colors.dart';\nimport 'package:afa_app/app_config/app_images.dart';\nimport 'package:afa_app/app_config/common_components.dart';\nimport 'package:flutter/material.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\n\nimport '../../../models/home_screens_models/sessions_q&a_models/answers_model.dart';\n\nclass QuestionsScreenWidgets {\n  static Future showAnswerAlertWidget({\n    required BuildContext context,\n    required List<AnswersModel> answersList,\n  }) async {\n    return await showGeneralDialog(\n      context: context,\n      pageBuilder: (context, animation1, animation2) => Container(),\n      transitionDuration: const Duration(milliseconds: 400),\n      transitionBuilder: (context, animation1, animation2, child) =>\n          ScaleTransition(\n            scale: animation1,\n            child: AlertDialog(\n              contentPadding: EdgeInsets.all(10.0.h),\n              shape: RoundedRectangleBorder(\n                borderRadius: BorderRadius.circular(20.0),\n              ),\n              backgroundColor: Colors.white,\n              title: Center(\n                child: Text(\n                  \"Answers\",\n                  style: TextStyle(\n                    fontSize: 20.0.sp,\n                    fontWeight: FontWeight.bold,\n                    color: AppColors.blackColor,\n                  ),\n                ),\n              ),\n              content: SizedBox(\n                height: 200.0.h,\n                width: double.maxFinite,\n                child: Column(\n                  children: [\n                    Expanded(\n                      child: ListView.separated(\n                        separatorBuilder: (context, index) =>\n                            SizedBox(height: 10.0.h),\n                        itemCount: answersList.length,\n                        itemBuilder: (context, index) => Container(\n                          padding: EdgeInsets.all(10.0.h),\n                          decoration: BoxDecoration(\n                            border: Border.all(color: AppColors.greyColor),\n                            borderRadius: BorderRadius.all(\n                              Radius.circular(16.0.r),\n                            ),\n                          ),\n                          child: Column(\n                            crossAxisAlignment: CrossAxisAlignment.start,\n                            children: [\n                              Row(\n                                children: [\n                                  ClipOval(\n                                    child: answersList[index].userImage != null\n                                        ? CommonComponents.imageWithNetworkCache(\n                                            context: context,\n                                            image:\n                                                answersList[index].userImage!,\n                                            height: 40.0.h,\n                                            width: 40.0.w,\n                                            fit: BoxFit.contain,\n                                          )\n                                        : CommonComponents.imageAssetWithCache(\n                                            context: context,\n                                            image: AppImages.userPhoto,\n                                            height: 40.0.h,\n                                            width: 40.0.w,\n                                            fit: BoxFit.contain,\n                                          ),\n                                  ),\n                                  SizedBox(width: 5.0.w),\n                                  Text(\n                                    answersList[index].userName!,\n                                    style: TextStyle(\n                                      fontSize: 14.0.sp,\n                                      fontWeight: FontWeight.bold,\n                                      color: AppColors.blackColor,\n                                    ),\n                                  ),\n                                ],\n                              ),\n                              const Divider(color: AppColors.greyColor),\n                              Text(\n                                answersList[index].answer!,\n                                style: TextStyle(\n                                  fontSize: 12.0.sp,\n                                  color: AppColors.blackColor,\n                                  fontWeight: FontWeight.bold,\n                                ),\n                              ),\n                            ],\n                          ),\n                        ),\n                      ),\n                    ),\n\n                    ElevatedButton(\n                      onPressed: () {\n                        Navigator.pop(context);\n                      },\n                      style: ElevatedButton.styleFrom(\n                        foregroundColor: Colors.white,\n                        backgroundColor: AppColors.midLevelGreenColor,\n                        textStyle: TextStyle(\n                          fontSize: 16.0.sp,\n                          fontWeight: FontWeight.bold,\n                        ),\n                        minimumSize: Size(398.0.w, 46.0.h),\n                        shape: RoundedRectangleBorder(\n                          borderRadius: BorderRadius.all(\n                            Radius.circular(32.0.r),\n                          ),\n                        ),\n                      ),\n                      child: const Text(\"OK\"),\n                    ),\n                  ],\n                ),\n              ),\n            ),\n          ),\n    );\n  }\n}\n", "baseTimestamp": 1755084943930, "deltas": [{"timestamp": 1755084997090, "changes": [{"type": "DELETE", "lineNumber": 17, "oldContent": "      transitionBuilder: (context, animation1, animation2, child) =>"}, {"type": "DELETE", "lineNumber": 18, "oldContent": "          ScaleTransition("}, {"type": "DELETE", "lineNumber": 19, "oldContent": "            scale: animation1,"}, {"type": "DELETE", "lineNumber": 20, "oldContent": "            child: <PERSON><PERSON><PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 21, "oldContent": "              contentPadding: EdgeInsets.all(10.0.h),"}, {"type": "DELETE", "lineNumber": 22, "oldContent": "              shape: RoundedRectangleBorder("}, {"type": "DELETE", "lineNumber": 23, "oldContent": "                borderRadius: BorderRadius.circular(20.0),"}, {"type": "INSERT", "lineNumber": 17, "content": "      transitionBuilder: (context, animation1, animation2, child) => ScaleTransition("}, {"type": "INSERT", "lineNumber": 18, "content": "        scale: animation1,"}, {"type": "INSERT", "lineNumber": 19, "content": "        child: <PERSON><PERSON><PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 20, "content": "          contentPadding: EdgeInsets.all(10.0.h),"}, {"type": "INSERT", "lineNumber": 21, "content": "          shape: RoundedRectangleBorder("}, {"type": "INSERT", "lineNumber": 22, "content": "            borderRadius: BorderRadius.circular(20.0),"}, {"type": "INSERT", "lineNumber": 23, "content": "          ),"}, {"type": "INSERT", "lineNumber": 24, "content": "          backgroundColor: Colors.white,"}, {"type": "INSERT", "lineNumber": 25, "content": "          title: Center("}, {"type": "INSERT", "lineNumber": 26, "content": "            child: Text("}, {"type": "INSERT", "lineNumber": 27, "content": "              \"Answers\","}, {"type": "INSERT", "lineNumber": 28, "content": "              style: TextStyle("}, {"type": "INSERT", "lineNumber": 29, "content": "                fontSize: 20.0.sp,"}, {"type": "INSERT", "lineNumber": 30, "content": "                fontWeight: FontWeight.bold,"}, {"type": "INSERT", "lineNumber": 31, "content": "                color: AppColors.blackColor,"}, {"type": "DELETE", "lineNumber": 25, "oldContent": "              backgroundColor: Colors.white,"}, {"type": "DELETE", "lineNumber": 26, "oldContent": "              title: Center("}, {"type": "DELETE", "lineNumber": 27, "oldContent": "                child: Text("}, {"type": "DELETE", "lineNumber": 28, "oldContent": "                  \"Answers\","}, {"type": "DELETE", "lineNumber": 29, "oldContent": "                  style: TextStyle("}, {"type": "DELETE", "lineNumber": 30, "oldContent": "                    fontSize: 20.0.sp,"}, {"type": "DELETE", "lineNumber": 31, "oldContent": "                    fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 32, "oldContent": "                    color: AppColors.blackColor,"}, {"type": "DELETE", "lineNumber": 33, "oldContent": "                  ),"}, {"type": "DELETE", "lineNumber": 34, "oldContent": "                ),"}, {"type": "DELETE", "lineNumber": 35, "oldContent": "              ),"}, {"type": "DELETE", "lineNumber": 36, "oldContent": "              content: SizedBox("}, {"type": "DELETE", "lineNumber": 37, "oldContent": "                height: 200.0.h,"}, {"type": "DELETE", "lineNumber": 38, "oldContent": "                width: double.maxFinite,"}, {"type": "DELETE", "lineNumber": 39, "oldContent": "                child: <PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 40, "oldContent": "                  children: ["}, {"type": "DELETE", "lineNumber": 41, "oldContent": "                    Expanded("}, {"type": "DELETE", "lineNumber": 42, "oldContent": "                      child: <PERSON><PERSON><PERSON><PERSON>.separated("}, {"type": "DELETE", "lineNumber": 43, "oldContent": "                        separatorBuilder: (context, index) =>"}, {"type": "DELETE", "lineNumber": 44, "oldContent": "                            SizedBox(height: 10.0.h),"}, {"type": "DELETE", "lineNumber": 45, "oldContent": "                        itemCount: answersList.length,"}, {"type": "DELETE", "lineNumber": 46, "oldContent": "                        itemBuilder: (context, index) => Container("}, {"type": "DELETE", "lineNumber": 47, "oldContent": "                          padding: EdgeInsets.all(10.0.h),"}, {"type": "DELETE", "lineNumber": 48, "oldContent": "                          decoration: BoxDecoration("}, {"type": "DELETE", "lineNumber": 49, "oldContent": "                            border: Border.all(color: AppColors.greyColor),"}, {"type": "DELETE", "lineNumber": 50, "oldContent": "                            borderRadius: BorderRadius.all("}, {"type": "DELETE", "lineNumber": 51, "oldContent": "                              Radius.circular(16.0.r),"}, {"type": "DELETE", "lineNumber": 52, "oldContent": "                            ),"}, {"type": "DELETE", "lineNumber": 53, "oldContent": "                          ),"}, {"type": "DELETE", "lineNumber": 54, "oldContent": "                          child: <PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 55, "oldContent": "                            crossAxisAlignment: CrossAxisAlignment.start,"}, {"type": "INSERT", "lineNumber": 33, "content": "            ),"}, {"type": "INSERT", "lineNumber": 34, "content": "          ),"}, {"type": "INSERT", "lineNumber": 35, "content": "          content: SizedBox("}, {"type": "INSERT", "lineNumber": 36, "content": "            height: 200.0.h,"}, {"type": "INSERT", "lineNumber": 37, "content": "            width: double.maxFinite,"}, {"type": "INSERT", "lineNumber": 38, "content": "            child: <PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 39, "content": "              children: ["}, {"type": "INSERT", "lineNumber": 40, "content": "                Expanded("}, {"type": "INSERT", "lineNumber": 41, "content": "                  child: <PERSON><PERSON><PERSON><PERSON>.separated("}, {"type": "INSERT", "lineNumber": 42, "content": "                    separatorBuilder: (context, index) =>"}, {"type": "INSERT", "lineNumber": 43, "content": "                        SizedBox(height: 10.0.h),"}, {"type": "INSERT", "lineNumber": 44, "content": "                    itemCount: answersList.length,"}, {"type": "INSERT", "lineNumber": 45, "content": "                    itemBuilder: (context, index) => Container("}, {"type": "INSERT", "lineNumber": 46, "content": "                      padding: EdgeInsets.all(10.0.h),"}, {"type": "INSERT", "lineNumber": 47, "content": "                      decoration: BoxDecoration("}, {"type": "INSERT", "lineNumber": 48, "content": "                        border: Border.all(color: AppColors.greyColor),"}, {"type": "INSERT", "lineNumber": 49, "content": "                        borderRadius: BorderRadius.all(Radius.circular(16.0.r)),"}, {"type": "INSERT", "lineNumber": 50, "content": "                      ),"}, {"type": "INSERT", "lineNumber": 51, "content": "                      child: <PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 52, "content": "                        crossAxisAlignment: CrossAxisAlignment.start,"}, {"type": "INSERT", "lineNumber": 53, "content": "                        children: ["}, {"type": "INSERT", "lineNumber": 54, "content": "                          Row("}, {"type": "DELETE", "lineNumber": 57, "oldContent": "                              Row("}, {"type": "DELETE", "lineNumber": 58, "oldContent": "                                children: ["}, {"type": "DELETE", "lineNumber": 59, "oldContent": "                                  ClipOval("}, {"type": "DELETE", "lineNumber": 60, "oldContent": "                                    child: answersList[index].userImage != null"}, {"type": "DELETE", "lineNumber": 61, "oldContent": "                                        ? CommonComponents.imageWithNetworkCache("}, {"type": "DELETE", "lineNumber": 62, "oldContent": "                                            context: context,"}, {"type": "DELETE", "lineNumber": 63, "oldContent": "                                            image:"}, {"type": "DELETE", "lineNumber": 64, "oldContent": "                                                answersList[index].userImage!,"}, {"type": "DELETE", "lineNumber": 65, "oldContent": "                                            height: 40.0.h,"}, {"type": "DELETE", "lineNumber": 66, "oldContent": "                                            width: 40.0.w,"}, {"type": "DELETE", "lineNumber": 67, "oldContent": "                                            fit: BoxFit.contain,"}, {"type": "DELETE", "lineNumber": 68, "oldContent": "                                          )"}, {"type": "DELETE", "lineNumber": 69, "oldContent": "                                        : CommonComponents.imageAssetWithCache("}, {"type": "DELETE", "lineNumber": 70, "oldContent": "                                            context: context,"}, {"type": "DELETE", "lineNumber": 71, "oldContent": "                                            image: AppImages.userPhoto,"}, {"type": "DELETE", "lineNumber": 72, "oldContent": "                                            height: 40.0.h,"}, {"type": "DELETE", "lineNumber": 73, "oldContent": "                                            width: 40.0.w,"}, {"type": "DELETE", "lineNumber": 74, "oldContent": "                                            fit: BoxFit.contain,"}, {"type": "DELETE", "lineNumber": 75, "oldContent": "                                          ),"}, {"type": "DELETE", "lineNumber": 76, "oldContent": "                                  ),"}, {"type": "DELETE", "lineNumber": 77, "oldContent": "                                  SizedBox(width: 5.0.w),"}, {"type": "DELETE", "lineNumber": 78, "oldContent": "                                  Text("}, {"type": "DELETE", "lineNumber": 79, "oldContent": "                                    answersList[index].userName!,"}, {"type": "DELETE", "lineNumber": 80, "oldContent": "                                    style: TextStyle("}, {"type": "DELETE", "lineNumber": 81, "oldContent": "                                      fontSize: 14.0.sp,"}, {"type": "DELETE", "lineNumber": 82, "oldContent": "                                      fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 83, "oldContent": "                                      color: AppColors.blackColor,"}, {"type": "DELETE", "lineNumber": 84, "oldContent": "                                    ),"}, {"type": "DELETE", "lineNumber": 85, "oldContent": "                                  ),"}, {"type": "DELETE", "lineNumber": 86, "oldContent": "                                ],"}, {"type": "DELETE", "lineNumber": 87, "oldContent": "                              ),"}, {"type": "DELETE", "lineNumber": 88, "oldContent": "                              const Divider(color: AppColors.greyColor),"}, {"type": "INSERT", "lineNumber": 56, "content": "                              // ClipOval("}, {"type": "INSERT", "lineNumber": 57, "content": "                              //   child: answersList[index].userImage != null"}, {"type": "INSERT", "lineNumber": 58, "content": "                              //       ? CommonComponents.imageWithNetworkCache("}, {"type": "INSERT", "lineNumber": 59, "content": "                              //           context: context,"}, {"type": "INSERT", "lineNumber": 60, "content": "                              //           image:"}, {"type": "INSERT", "lineNumber": 61, "content": "                              //               answersList[index].userImage!,"}, {"type": "INSERT", "lineNumber": 62, "content": "                              //           height: 40.0.h,"}, {"type": "INSERT", "lineNumber": 63, "content": "                              //           width: 40.0.w,"}, {"type": "INSERT", "lineNumber": 64, "content": "                              //           fit: BoxFit.contain,"}, {"type": "INSERT", "lineNumber": 65, "content": "                              //         )"}, {"type": "INSERT", "lineNumber": 66, "content": "                              //       : CommonComponents.imageAssetWithCache("}, {"type": "INSERT", "lineNumber": 67, "content": "                              //           context: context,"}, {"type": "INSERT", "lineNumber": 68, "content": "                              //           image: AppImages.userPhoto,"}, {"type": "INSERT", "lineNumber": 69, "content": "                              //           height: 40.0.h,"}, {"type": "INSERT", "lineNumber": 70, "content": "                              //           width: 40.0.w,"}, {"type": "INSERT", "lineNumber": 71, "content": "                              //           fit: BoxFit.contain,"}, {"type": "INSERT", "lineNumber": 72, "content": "                              //         ),"}, {"type": "INSERT", "lineNumber": 73, "content": "                              // ),"}, {"type": "INSERT", "lineNumber": 74, "content": "                              SizedBox(width: 5.0.w),"}, {"type": "DELETE", "lineNumber": 90, "oldContent": "                                answersList[index].answer!,"}, {"type": "INSERT", "lineNumber": 76, "content": "                                answersList[index].userName!,"}, {"type": "DELETE", "lineNumber": 92, "oldContent": "                                  fontSize: 12.0.sp,"}, {"type": "DELETE", "lineNumber": 93, "oldContent": "                                  color: AppColors.blackColor,"}, {"type": "INSERT", "lineNumber": 78, "content": "                                  fontSize: 14.0.sp,"}, {"type": "INSERT", "lineNumber": 80, "content": "                                  color: AppColors.blackColor,"}, {"type": "DELETE", "lineNumber": 99, "oldContent": "                        ),"}, {"type": "INSERT", "lineNumber": 85, "content": "                          const Divider(color: AppColors.greyColor),"}, {"type": "INSERT", "lineNumber": 86, "content": "                          Text("}, {"type": "INSERT", "lineNumber": 87, "content": "                            answersList[index].answer!,"}, {"type": "INSERT", "lineNumber": 88, "content": "                            style: TextStyle("}, {"type": "INSERT", "lineNumber": 89, "content": "                              fontSize: 12.0.sp,"}, {"type": "INSERT", "lineNumber": 90, "content": "                              color: AppColors.blackColor,"}, {"type": "INSERT", "lineNumber": 91, "content": "                              fontWeight: FontWeight.bold,"}, {"type": "INSERT", "lineNumber": 92, "content": "                            ),"}, {"type": "INSERT", "lineNumber": 93, "content": "                          ),"}, {"type": "INSERT", "lineNumber": 94, "content": "                        ],"}, {"type": "INSERT", "lineNumber": 97, "content": "                  ),"}, {"type": "INSERT", "lineNumber": 98, "content": "                ),"}, {"type": "DELETE", "lineNumber": 103, "oldContent": "                    ElevatedButton("}, {"type": "DELETE", "lineNumber": 104, "oldContent": "                      onPressed: () {"}, {"type": "DELETE", "lineNumber": 105, "oldContent": "                        Navigator.pop(context);"}, {"type": "DELETE", "lineNumber": 106, "oldContent": "                      },"}, {"type": "DELETE", "lineNumber": 107, "oldContent": "                      style: ElevatedButton.styleFrom("}, {"type": "DELETE", "lineNumber": 108, "oldContent": "                        foregroundColor: Colors.white,"}, {"type": "DELETE", "lineNumber": 109, "oldContent": "                        backgroundColor: AppColors.midLevelGreenColor,"}, {"type": "DELETE", "lineNumber": 110, "oldContent": "                        textStyle: TextStyle("}, {"type": "DELETE", "lineNumber": 111, "oldContent": "                          fontSize: 16.0.sp,"}, {"type": "DELETE", "lineNumber": 112, "oldContent": "                          fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 113, "oldContent": "                        ),"}, {"type": "DELETE", "lineNumber": 114, "oldContent": "                        minimumSize: <PERSON><PERSON>(398.0.w, 46.0.h),"}, {"type": "DELETE", "lineNumber": 115, "oldContent": "                        shape: RoundedRectangleBorder("}, {"type": "DELETE", "lineNumber": 116, "oldContent": "                          borderRadius: BorderRadius.all("}, {"type": "DELETE", "lineNumber": 117, "oldContent": "                            Radius.circular(32.0.r),"}, {"type": "DELETE", "lineNumber": 118, "oldContent": "                          ),"}, {"type": "DELETE", "lineNumber": 119, "oldContent": "                        ),"}, {"type": "DELETE", "lineNumber": 120, "oldContent": "                      ),"}, {"type": "DELETE", "lineNumber": 121, "oldContent": "                      child: const Text(\"OK\"),"}, {"type": "INSERT", "lineNumber": 100, "content": "                ElevatedButton("}, {"type": "INSERT", "lineNumber": 101, "content": "                  onPressed: () {"}, {"type": "INSERT", "lineNumber": 102, "content": "                    Navigator.pop(context);"}, {"type": "INSERT", "lineNumber": 103, "content": "                  },"}, {"type": "INSERT", "lineNumber": 104, "content": "                  style: ElevatedButton.styleFrom("}, {"type": "INSERT", "lineNumber": 105, "content": "                    foregroundColor: Colors.white,"}, {"type": "INSERT", "lineNumber": 106, "content": "                    backgroundColor: AppColors.midLevelGreenColor,"}, {"type": "INSERT", "lineNumber": 107, "content": "                    textStyle: TextStyle("}, {"type": "INSERT", "lineNumber": 108, "content": "                      fontSize: 16.0.sp,"}, {"type": "INSERT", "lineNumber": 109, "content": "                      fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 123, "oldContent": "                  ],"}, {"type": "INSERT", "lineNumber": 111, "content": "                    minimumSize: <PERSON><PERSON>(398.0.w, 46.0.h),"}, {"type": "INSERT", "lineNumber": 112, "content": "                    shape: RoundedRectangleBorder("}, {"type": "INSERT", "lineNumber": 113, "content": "                      borderRadius: BorderRadius.all(Radius.circular(32.0.r)),"}, {"type": "INSERT", "lineNumber": 114, "content": "                    ),"}, {"type": "INSERT", "lineNumber": 115, "content": "                  ),"}, {"type": "INSERT", "lineNumber": 116, "content": "                  child: const Text(\"OK\"),"}, {"type": "DELETE", "lineNumber": 125, "oldContent": "              ),"}, {"type": "INSERT", "lineNumber": 118, "content": "              ],"}, {"type": "INSERT", "lineNumber": 121, "content": "        ),"}, {"type": "INSERT", "lineNumber": 122, "content": "      ),"}]}, {"timestamp": 1755085001591, "changes": [{"type": "DELETE", "lineNumber": 17, "oldContent": "      transitionBuilder: (context, animation1, animation2, child) => ScaleTransition("}, {"type": "DELETE", "lineNumber": 18, "oldContent": "        scale: animation1,"}, {"type": "DELETE", "lineNumber": 19, "oldContent": "        child: <PERSON><PERSON><PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 20, "oldContent": "          contentPadding: EdgeInsets.all(10.0.h),"}, {"type": "DELETE", "lineNumber": 21, "oldContent": "          shape: RoundedRectangleBorder("}, {"type": "DELETE", "lineNumber": 22, "oldContent": "            borderRadius: BorderRadius.circular(20.0),"}, {"type": "DELETE", "lineNumber": 23, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 24, "oldContent": "          backgroundColor: Colors.white,"}, {"type": "INSERT", "lineNumber": 17, "content": "      transitionBuilder: (context, animation1, animation2, child) =>"}, {"type": "INSERT", "lineNumber": 18, "content": "          ScaleTransition("}, {"type": "INSERT", "lineNumber": 19, "content": "            scale: animation1,"}, {"type": "INSERT", "lineNumber": 20, "content": "            child: <PERSON><PERSON><PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 21, "content": "              contentPadding: EdgeInsets.all(10.0.h),"}, {"type": "INSERT", "lineNumber": 22, "content": "              shape: RoundedRectangleBorder("}, {"type": "INSERT", "lineNumber": 23, "content": "                borderRadius: BorderRadius.circular(20.0),"}, {"type": "DELETE", "lineNumber": 26, "oldContent": "          title: Center("}, {"type": "DELETE", "lineNumber": 27, "oldContent": "            child: Text("}, {"type": "DELETE", "lineNumber": 28, "oldContent": "              \"Answers\","}, {"type": "DELETE", "lineNumber": 29, "oldContent": "              style: TextStyle("}, {"type": "DELETE", "lineNumber": 30, "oldContent": "                fontSize: 20.0.sp,"}, {"type": "DELETE", "lineNumber": 31, "oldContent": "                fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 32, "oldContent": "                color: AppColors.blackColor,"}, {"type": "DELETE", "lineNumber": 33, "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 34, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 35, "oldContent": "          content: SizedBox("}, {"type": "DELETE", "lineNumber": 36, "oldContent": "            height: 200.0.h,"}, {"type": "DELETE", "lineNumber": 37, "oldContent": "            width: double.maxFinite,"}, {"type": "DELETE", "lineNumber": 38, "oldContent": "            child: <PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 39, "oldContent": "              children: ["}, {"type": "DELETE", "lineNumber": 40, "oldContent": "                Expanded("}, {"type": "DELETE", "lineNumber": 41, "oldContent": "                  child: <PERSON><PERSON><PERSON><PERSON>.separated("}, {"type": "DELETE", "lineNumber": 42, "oldContent": "                    separatorBuilder: (context, index) =>"}, {"type": "DELETE", "lineNumber": 43, "oldContent": "                        SizedBox(height: 10.0.h),"}, {"type": "DELETE", "lineNumber": 44, "oldContent": "                    itemCount: answersList.length,"}, {"type": "DELETE", "lineNumber": 45, "oldContent": "                    itemBuilder: (context, index) => Container("}, {"type": "DELETE", "lineNumber": 46, "oldContent": "                      padding: EdgeInsets.all(10.0.h),"}, {"type": "DELETE", "lineNumber": 47, "oldContent": "                      decoration: BoxDecoration("}, {"type": "DELETE", "lineNumber": 48, "oldContent": "                        border: Border.all(color: AppColors.greyColor),"}, {"type": "DELETE", "lineNumber": 49, "oldContent": "                        borderRadius: BorderRadius.all(Radius.circular(16.0.r)),"}, {"type": "DELETE", "lineNumber": 50, "oldContent": "                      ),"}, {"type": "DELETE", "lineNumber": 51, "oldContent": "                      child: <PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 52, "oldContent": "                        crossAxisAlignment: CrossAxisAlignment.start,"}, {"type": "DELETE", "lineNumber": 53, "oldContent": "                        children: ["}, {"type": "DELETE", "lineNumber": 54, "oldContent": "                          Row("}, {"type": "DELETE", "lineNumber": 55, "oldContent": "                              // ClipOval("}, {"type": "INSERT", "lineNumber": 25, "content": "              backgroundColor: Colors.white,"}, {"type": "INSERT", "lineNumber": 26, "content": "              title: Center("}, {"type": "INSERT", "lineNumber": 27, "content": "                child: Text("}, {"type": "INSERT", "lineNumber": 28, "content": "                  \"Answers\","}, {"type": "INSERT", "lineNumber": 29, "content": "                  style: TextStyle("}, {"type": "INSERT", "lineNumber": 30, "content": "                    fontSize: 20.0.sp,"}, {"type": "INSERT", "lineNumber": 31, "content": "                    fontWeight: FontWeight.bold,"}, {"type": "INSERT", "lineNumber": 32, "content": "                    color: AppColors.blackColor,"}, {"type": "INSERT", "lineNumber": 33, "content": "                  ),"}, {"type": "INSERT", "lineNumber": 34, "content": "                ),"}, {"type": "INSERT", "lineNumber": 35, "content": "              ),"}, {"type": "INSERT", "lineNumber": 36, "content": "              content: SizedBox("}, {"type": "INSERT", "lineNumber": 37, "content": "                height: 200.0.h,"}, {"type": "INSERT", "lineNumber": 38, "content": "                width: double.maxFinite,"}, {"type": "INSERT", "lineNumber": 39, "content": "                child: <PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 40, "content": "                  children: ["}, {"type": "INSERT", "lineNumber": 41, "content": "                    Expanded("}, {"type": "INSERT", "lineNumber": 42, "content": "                      child: <PERSON><PERSON><PERSON><PERSON>.separated("}, {"type": "INSERT", "lineNumber": 43, "content": "                        separatorBuilder: (context, index) =>"}, {"type": "INSERT", "lineNumber": 44, "content": "                            SizedBox(height: 10.0.h),"}, {"type": "INSERT", "lineNumber": 45, "content": "                        itemCount: answersList.length,"}, {"type": "INSERT", "lineNumber": 46, "content": "                        itemBuilder: (context, index) => Container("}, {"type": "INSERT", "lineNumber": 47, "content": "                          padding: EdgeInsets.all(10.0.h),"}, {"type": "INSERT", "lineNumber": 48, "content": "                          decoration: BoxDecoration("}, {"type": "INSERT", "lineNumber": 49, "content": "                            border: Border.all(color: AppColors.greyColor),"}, {"type": "INSERT", "lineNumber": 50, "content": "                            borderRadius: BorderRadius.all("}, {"type": "INSERT", "lineNumber": 51, "content": "                              Radius.circular(16.0.r),"}, {"type": "INSERT", "lineNumber": 52, "content": "                            ),"}, {"type": "INSERT", "lineNumber": 53, "content": "                          ),"}, {"type": "INSERT", "lineNumber": 54, "content": "                          child: <PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 55, "content": "                            crossAxisAlignment: CrossAxisAlignment.start,"}, {"type": "DELETE", "lineNumber": 57, "oldContent": "                              //   child: answersList[index].userImage != null"}, {"type": "DELETE", "lineNumber": 58, "oldContent": "                              //       ? CommonComponents.imageWithNetworkCache("}, {"type": "DELETE", "lineNumber": 59, "oldContent": "                              //           context: context,"}, {"type": "DELETE", "lineNumber": 60, "oldContent": "                              //           image:"}, {"type": "DELETE", "lineNumber": 61, "oldContent": "                              //               answersList[index].userImage!,"}, {"type": "DELETE", "lineNumber": 62, "oldContent": "                              //           height: 40.0.h,"}, {"type": "DELETE", "lineNumber": 63, "oldContent": "                              //           width: 40.0.w,"}, {"type": "DELETE", "lineNumber": 64, "oldContent": "                              //           fit: BoxFit.contain,"}, {"type": "DELETE", "lineNumber": 65, "oldContent": "                              //         )"}, {"type": "DELETE", "lineNumber": 66, "oldContent": "                              //       : CommonComponents.imageAssetWithCache("}, {"type": "DELETE", "lineNumber": 67, "oldContent": "                              //           context: context,"}, {"type": "DELETE", "lineNumber": 68, "oldContent": "                              //           image: AppImages.userPhoto,"}, {"type": "DELETE", "lineNumber": 69, "oldContent": "                              //           height: 40.0.h,"}, {"type": "DELETE", "lineNumber": 70, "oldContent": "                              //           width: 40.0.w,"}, {"type": "DELETE", "lineNumber": 71, "oldContent": "                              //           fit: BoxFit.contain,"}, {"type": "DELETE", "lineNumber": 72, "oldContent": "                              //         ),"}, {"type": "DELETE", "lineNumber": 73, "oldContent": "                              // ),"}, {"type": "DELETE", "lineNumber": 74, "oldContent": "                              SizedBox(width: 5.0.w),"}, {"type": "DELETE", "lineNumber": 75, "oldContent": "                                answersList[index].userName!,"}, {"type": "DELETE", "lineNumber": 76, "oldContent": "                                  fontSize: 14.0.sp,"}, {"type": "DELETE", "lineNumber": 77, "oldContent": "                                  color: AppColors.blackColor,"}, {"type": "DELETE", "lineNumber": 78, "oldContent": "                          const Divider(color: AppColors.greyColor),"}, {"type": "DELETE", "lineNumber": 79, "oldContent": "                          Text("}, {"type": "DELETE", "lineNumber": 80, "oldContent": "                            answersList[index].answer!,"}, {"type": "DELETE", "lineNumber": 81, "oldContent": "                            style: TextStyle("}, {"type": "DELETE", "lineNumber": 82, "oldContent": "                              fontSize: 12.0.sp,"}, {"type": "INSERT", "lineNumber": 57, "content": "                              Row("}, {"type": "INSERT", "lineNumber": 58, "content": "                                children: ["}, {"type": "INSERT", "lineNumber": 59, "content": "                                  ClipOval("}, {"type": "INSERT", "lineNumber": 60, "content": "                                    child: answersList[index].userImage != null"}, {"type": "INSERT", "lineNumber": 61, "content": "                                        ? CommonComponents.imageWithNetworkCache("}, {"type": "INSERT", "lineNumber": 62, "content": "                                            context: context,"}, {"type": "INSERT", "lineNumber": 63, "content": "                                            image:"}, {"type": "INSERT", "lineNumber": 64, "content": "                                                answersList[index].userImage!,"}, {"type": "INSERT", "lineNumber": 65, "content": "                                            height: 40.0.h,"}, {"type": "INSERT", "lineNumber": 66, "content": "                                            width: 40.0.w,"}, {"type": "INSERT", "lineNumber": 67, "content": "                                            fit: BoxFit.contain,"}, {"type": "INSERT", "lineNumber": 68, "content": "                                          )"}, {"type": "INSERT", "lineNumber": 69, "content": "                                        : CommonComponents.imageAssetWithCache("}, {"type": "INSERT", "lineNumber": 70, "content": "                                            context: context,"}, {"type": "INSERT", "lineNumber": 71, "content": "                                            image: AppImages.userPhoto,"}, {"type": "INSERT", "lineNumber": 72, "content": "                                            height: 40.0.h,"}, {"type": "INSERT", "lineNumber": 73, "content": "                                            width: 40.0.w,"}, {"type": "INSERT", "lineNumber": 74, "content": "                                            fit: BoxFit.contain,"}, {"type": "INSERT", "lineNumber": 75, "content": "                                          ),"}, {"type": "INSERT", "lineNumber": 76, "content": "                                  ),"}, {"type": "INSERT", "lineNumber": 77, "content": "                                  SizedBox(width: 5.0.w),"}, {"type": "INSERT", "lineNumber": 78, "content": "                                  Text("}, {"type": "INSERT", "lineNumber": 79, "content": "                                    answersList[index].userName!,"}, {"type": "INSERT", "lineNumber": 80, "content": "                                    style: TextStyle("}, {"type": "INSERT", "lineNumber": 81, "content": "                                      fontSize: 14.0.sp,"}, {"type": "INSERT", "lineNumber": 82, "content": "                                      fontWeight: FontWeight.bold,"}, {"type": "INSERT", "lineNumber": 83, "content": "                                      color: AppColors.blackColor,"}, {"type": "INSERT", "lineNumber": 84, "content": "                                    ),"}, {"type": "INSERT", "lineNumber": 85, "content": "                                  ),"}, {"type": "INSERT", "lineNumber": 86, "content": "                                ],"}, {"type": "INSERT", "lineNumber": 87, "content": "                              ),"}, {"type": "INSERT", "lineNumber": 88, "content": "                              const Divider(color: AppColors.greyColor),"}, {"type": "DELETE", "lineNumber": 84, "oldContent": "                              color: AppColors.blackColor,"}, {"type": "DELETE", "lineNumber": 85, "oldContent": "                              fontWeight: FontWeight.bold,"}, {"type": "INSERT", "lineNumber": 90, "content": "                                answersList[index].answer!,"}, {"type": "DELETE", "lineNumber": 87, "oldContent": "                            ),"}, {"type": "DELETE", "lineNumber": 88, "oldContent": "                          ),"}, {"type": "DELETE", "lineNumber": 89, "oldContent": "                        ],"}, {"type": "INSERT", "lineNumber": 92, "content": "                                  fontSize: 12.0.sp,"}, {"type": "INSERT", "lineNumber": 93, "content": "                                  color: AppColors.blackColor,"}, {"type": "DELETE", "lineNumber": 93, "oldContent": "                  ),"}, {"type": "DELETE", "lineNumber": 95, "oldContent": "                ),"}, {"type": "DELETE", "lineNumber": 97, "oldContent": "                ElevatedButton("}, {"type": "INSERT", "lineNumber": 99, "content": "                        ),"}, {"type": "DELETE", "lineNumber": 99, "oldContent": "                  onPressed: () {"}, {"type": "DELETE", "lineNumber": 101, "oldContent": "                    Navigator.pop(context);"}, {"type": "DELETE", "lineNumber": 103, "oldContent": "                  },"}, {"type": "DELETE", "lineNumber": 104, "oldContent": "                  style: ElevatedButton.styleFrom("}, {"type": "DELETE", "lineNumber": 105, "oldContent": "                    foregroundColor: Colors.white,"}, {"type": "DELETE", "lineNumber": 106, "oldContent": "                    backgroundColor: AppColors.midLevelGreenColor,"}, {"type": "DELETE", "lineNumber": 107, "oldContent": "                    textStyle: TextStyle("}, {"type": "DELETE", "lineNumber": 108, "oldContent": "                      fontSize: 16.0.sp,"}, {"type": "DELETE", "lineNumber": 109, "oldContent": "                      fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 110, "oldContent": "                    minimumSize: <PERSON><PERSON>(398.0.w, 46.0.h),"}, {"type": "DELETE", "lineNumber": 111, "oldContent": "                    shape: RoundedRectangleBorder("}, {"type": "DELETE", "lineNumber": 112, "oldContent": "                      borderRadius: BorderRadius.all(Radius.circular(32.0.r)),"}, {"type": "INSERT", "lineNumber": 103, "content": "                    ElevatedButton("}, {"type": "INSERT", "lineNumber": 104, "content": "                      onPressed: () {"}, {"type": "INSERT", "lineNumber": 105, "content": "                        Navigator.pop(context);"}, {"type": "INSERT", "lineNumber": 106, "content": "                      },"}, {"type": "INSERT", "lineNumber": 107, "content": "                      style: ElevatedButton.styleFrom("}, {"type": "INSERT", "lineNumber": 108, "content": "                        foregroundColor: Colors.white,"}, {"type": "INSERT", "lineNumber": 109, "content": "                        backgroundColor: AppColors.midLevelGreenColor,"}, {"type": "INSERT", "lineNumber": 110, "content": "                        textStyle: TextStyle("}, {"type": "INSERT", "lineNumber": 111, "content": "                          fontSize: 16.0.sp,"}, {"type": "INSERT", "lineNumber": 112, "content": "                          fontWeight: FontWeight.bold,"}, {"type": "INSERT", "lineNumber": 113, "content": "                        ),"}, {"type": "INSERT", "lineNumber": 114, "content": "                        minimumSize: <PERSON><PERSON>(398.0.w, 46.0.h),"}, {"type": "INSERT", "lineNumber": 115, "content": "                        shape: RoundedRectangleBorder("}, {"type": "INSERT", "lineNumber": 116, "content": "                          borderRadius: BorderRadius.all("}, {"type": "INSERT", "lineNumber": 117, "content": "                            Radius.circular(32.0.r),"}, {"type": "INSERT", "lineNumber": 118, "content": "                          ),"}, {"type": "INSERT", "lineNumber": 119, "content": "                        ),"}, {"type": "INSERT", "lineNumber": 120, "content": "                      ),"}, {"type": "INSERT", "lineNumber": 121, "content": "                      child: const Text(\"OK\"),"}, {"type": "DELETE", "lineNumber": 114, "oldContent": "                  ),"}, {"type": "DELETE", "lineNumber": 115, "oldContent": "                  child: const Text(\"OK\"),"}, {"type": "DELETE", "lineNumber": 116, "oldContent": "              ],"}, {"type": "DELETE", "lineNumber": 117, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 118, "oldContent": "      ),"}, {"type": "DELETE", "lineNumber": 119, "oldContent": "                    ),"}, {"type": "INSERT", "lineNumber": 123, "content": "                  ],"}, {"type": "INSERT", "lineNumber": 125, "content": "              ),"}]}, {"timestamp": 1755085035362, "changes": [{"type": "DELETE", "lineNumber": 17, "oldContent": "      transitionBuilder: (context, animation1, animation2, child) =>"}, {"type": "DELETE", "lineNumber": 18, "oldContent": "          ScaleTransition("}, {"type": "DELETE", "lineNumber": 19, "oldContent": "            scale: animation1,"}, {"type": "DELETE", "lineNumber": 20, "oldContent": "            child: <PERSON><PERSON><PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 21, "oldContent": "              contentPadding: EdgeInsets.all(10.0.h),"}, {"type": "DELETE", "lineNumber": 22, "oldContent": "              shape: RoundedRectangleBorder("}, {"type": "DELETE", "lineNumber": 23, "oldContent": "                borderRadius: BorderRadius.circular(20.0),"}, {"type": "DELETE", "lineNumber": 24, "oldContent": "              backgroundColor: Colors.white,"}, {"type": "INSERT", "lineNumber": 17, "content": "      transitionBuilder: (context, animation1, animation2, child) => ScaleTransition("}, {"type": "INSERT", "lineNumber": 18, "content": "        scale: animation1,"}, {"type": "INSERT", "lineNumber": 19, "content": "        child: <PERSON><PERSON><PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 20, "content": "          contentPadding: EdgeInsets.all(10.0.h),"}, {"type": "INSERT", "lineNumber": 21, "content": "          shape: RoundedRectangleBorder("}, {"type": "INSERT", "lineNumber": 22, "content": "            borderRadius: BorderRadius.circular(20.0),"}, {"type": "INSERT", "lineNumber": 23, "content": "          ),"}, {"type": "INSERT", "lineNumber": 24, "content": "          backgroundColor: Colors.white,"}, {"type": "INSERT", "lineNumber": 25, "content": "          title: Center("}, {"type": "INSERT", "lineNumber": 26, "content": "            child: Text("}, {"type": "INSERT", "lineNumber": 27, "content": "              \"Answers\","}, {"type": "INSERT", "lineNumber": 28, "content": "              style: TextStyle("}, {"type": "INSERT", "lineNumber": 29, "content": "                fontSize: 20.0.sp,"}, {"type": "INSERT", "lineNumber": 30, "content": "                fontWeight: FontWeight.bold,"}, {"type": "INSERT", "lineNumber": 31, "content": "                color: AppColors.blackColor,"}, {"type": "DELETE", "lineNumber": 26, "oldContent": "              title: Center("}, {"type": "DELETE", "lineNumber": 27, "oldContent": "                child: Text("}, {"type": "DELETE", "lineNumber": 28, "oldContent": "                  \"Answers\","}, {"type": "DELETE", "lineNumber": 29, "oldContent": "                  style: TextStyle("}, {"type": "DELETE", "lineNumber": 30, "oldContent": "                    fontSize: 20.0.sp,"}, {"type": "DELETE", "lineNumber": 31, "oldContent": "                    fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 32, "oldContent": "                    color: AppColors.blackColor,"}, {"type": "DELETE", "lineNumber": 33, "oldContent": "                  ),"}, {"type": "DELETE", "lineNumber": 34, "oldContent": "                ),"}, {"type": "DELETE", "lineNumber": 35, "oldContent": "              ),"}, {"type": "DELETE", "lineNumber": 36, "oldContent": "              content: SizedBox("}, {"type": "DELETE", "lineNumber": 37, "oldContent": "                height: 200.0.h,"}, {"type": "DELETE", "lineNumber": 38, "oldContent": "                width: double.maxFinite,"}, {"type": "DELETE", "lineNumber": 39, "oldContent": "                child: <PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 40, "oldContent": "                  children: ["}, {"type": "DELETE", "lineNumber": 41, "oldContent": "                    Expanded("}, {"type": "DELETE", "lineNumber": 42, "oldContent": "                      child: <PERSON><PERSON><PERSON><PERSON>.separated("}, {"type": "DELETE", "lineNumber": 43, "oldContent": "                        separatorBuilder: (context, index) =>"}, {"type": "DELETE", "lineNumber": 44, "oldContent": "                            SizedBox(height: 10.0.h),"}, {"type": "DELETE", "lineNumber": 45, "oldContent": "                        itemCount: answersList.length,"}, {"type": "DELETE", "lineNumber": 46, "oldContent": "                        itemBuilder: (context, index) => Container("}, {"type": "DELETE", "lineNumber": 47, "oldContent": "                          padding: EdgeInsets.all(10.0.h),"}, {"type": "DELETE", "lineNumber": 48, "oldContent": "                          decoration: BoxDecoration("}, {"type": "DELETE", "lineNumber": 49, "oldContent": "                            border: Border.all(color: AppColors.greyColor),"}, {"type": "DELETE", "lineNumber": 50, "oldContent": "                            borderRadius: BorderRadius.all("}, {"type": "DELETE", "lineNumber": 51, "oldContent": "                              Radius.circular(16.0.r),"}, {"type": "DELETE", "lineNumber": 52, "oldContent": "                            ),"}, {"type": "DELETE", "lineNumber": 53, "oldContent": "                          ),"}, {"type": "DELETE", "lineNumber": 54, "oldContent": "                          child: <PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 55, "oldContent": "                            crossAxisAlignment: CrossAxisAlignment.start,"}, {"type": "INSERT", "lineNumber": 33, "content": "            ),"}, {"type": "INSERT", "lineNumber": 34, "content": "          ),"}, {"type": "INSERT", "lineNumber": 35, "content": "          content: SizedBox("}, {"type": "INSERT", "lineNumber": 36, "content": "            height: 200.0.h,"}, {"type": "INSERT", "lineNumber": 37, "content": "            width: double.maxFinite,"}, {"type": "INSERT", "lineNumber": 38, "content": "            child: <PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 39, "content": "              children: ["}, {"type": "INSERT", "lineNumber": 40, "content": "                Expanded("}, {"type": "INSERT", "lineNumber": 41, "content": "                  child: <PERSON><PERSON><PERSON><PERSON>.separated("}, {"type": "INSERT", "lineNumber": 42, "content": "                    separatorBuilder: (context, index) =>"}, {"type": "INSERT", "lineNumber": 43, "content": "                        SizedBox(height: 10.0.h),"}, {"type": "INSERT", "lineNumber": 44, "content": "                    itemCount: answersList.length,"}, {"type": "INSERT", "lineNumber": 45, "content": "                    itemBuilder: (context, index) => Container("}, {"type": "INSERT", "lineNumber": 46, "content": "                      padding: EdgeInsets.all(10.0.h),"}, {"type": "INSERT", "lineNumber": 47, "content": "                      decoration: BoxDecoration("}, {"type": "INSERT", "lineNumber": 48, "content": "                        border: Border.all(color: AppColors.greyColor),"}, {"type": "INSERT", "lineNumber": 49, "content": "                        borderRadius: BorderRadius.all(Radius.circular(16.0.r)),"}, {"type": "INSERT", "lineNumber": 50, "content": "                      ),"}, {"type": "INSERT", "lineNumber": 51, "content": "                      child: <PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 52, "content": "                        crossAxisAlignment: CrossAxisAlignment.start,"}, {"type": "INSERT", "lineNumber": 53, "content": "                        children: ["}, {"type": "INSERT", "lineNumber": 54, "content": "                          Row("}, {"type": "DELETE", "lineNumber": 57, "oldContent": "                              Row("}, {"type": "DELETE", "lineNumber": 58, "oldContent": "                                children: ["}, {"type": "DELETE", "lineNumber": 59, "oldContent": "                                  ClipOval("}, {"type": "DELETE", "lineNumber": 60, "oldContent": "                                    child: answersList[index].userImage != null"}, {"type": "DELETE", "lineNumber": 61, "oldContent": "                                        ? CommonComponents.imageWithNetworkCache("}, {"type": "DELETE", "lineNumber": 62, "oldContent": "                                            context: context,"}, {"type": "DELETE", "lineNumber": 63, "oldContent": "                                            image:"}, {"type": "DELETE", "lineNumber": 64, "oldContent": "                                                answersList[index].userImage!,"}, {"type": "DELETE", "lineNumber": 65, "oldContent": "                                            height: 40.0.h,"}, {"type": "DELETE", "lineNumber": 66, "oldContent": "                                            width: 40.0.w,"}, {"type": "DELETE", "lineNumber": 67, "oldContent": "                                            fit: BoxFit.contain,"}, {"type": "DELETE", "lineNumber": 68, "oldContent": "                                          )"}, {"type": "DELETE", "lineNumber": 69, "oldContent": "                                        : CommonComponents.imageAssetWithCache("}, {"type": "DELETE", "lineNumber": 70, "oldContent": "                                            context: context,"}, {"type": "DELETE", "lineNumber": 71, "oldContent": "                                            image: AppImages.userPhoto,"}, {"type": "DELETE", "lineNumber": 72, "oldContent": "                                            height: 40.0.h,"}, {"type": "DELETE", "lineNumber": 73, "oldContent": "                                            width: 40.0.w,"}, {"type": "DELETE", "lineNumber": 74, "oldContent": "                                            fit: BoxFit.contain,"}, {"type": "DELETE", "lineNumber": 75, "oldContent": "                                          ),"}, {"type": "DELETE", "lineNumber": 76, "oldContent": "                                  ),"}, {"type": "DELETE", "lineNumber": 77, "oldContent": "                                  SizedBox(width: 5.0.w),"}, {"type": "DELETE", "lineNumber": 78, "oldContent": "                                  Text("}, {"type": "DELETE", "lineNumber": 79, "oldContent": "                                    answersList[index].userName!,"}, {"type": "DELETE", "lineNumber": 80, "oldContent": "                                    style: TextStyle("}, {"type": "DELETE", "lineNumber": 81, "oldContent": "                                      fontSize: 14.0.sp,"}, {"type": "DELETE", "lineNumber": 82, "oldContent": "                                      fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 83, "oldContent": "                                      color: AppColors.blackColor,"}, {"type": "INSERT", "lineNumber": 56, "content": "                              // ClipOval("}, {"type": "INSERT", "lineNumber": 57, "content": "                              //   child: answersList[index].userImage != null"}, {"type": "INSERT", "lineNumber": 58, "content": "                              //       ? CommonComponents.imageWithNetworkCache("}, {"type": "INSERT", "lineNumber": 59, "content": "                              //           context: context,"}, {"type": "INSERT", "lineNumber": 60, "content": "                              //           image:"}, {"type": "INSERT", "lineNumber": 61, "content": "                              //               answersList[index].userImage!,"}, {"type": "INSERT", "lineNumber": 62, "content": "                              //           height: 40.0.h,"}, {"type": "INSERT", "lineNumber": 63, "content": "                              //           width: 40.0.w,"}, {"type": "INSERT", "lineNumber": 64, "content": "                              //           fit: BoxFit.contain,"}, {"type": "INSERT", "lineNumber": 65, "content": "                              //         )"}, {"type": "INSERT", "lineNumber": 66, "content": "                              //       : CommonComponents.imageAssetWithCache("}, {"type": "INSERT", "lineNumber": 67, "content": "                              //           context: context,"}, {"type": "INSERT", "lineNumber": 68, "content": "                              //           image: AppImages.userPhoto,"}, {"type": "INSERT", "lineNumber": 69, "content": "                              //           height: 40.0.h,"}, {"type": "INSERT", "lineNumber": 70, "content": "                              //           width: 40.0.w,"}, {"type": "INSERT", "lineNumber": 71, "content": "                              //           fit: BoxFit.contain,"}, {"type": "INSERT", "lineNumber": 72, "content": "                              //         ),"}, {"type": "INSERT", "lineNumber": 73, "content": "                              // ),"}, {"type": "INSERT", "lineNumber": 74, "content": "                              SizedBox(width: 5.0.w),"}, {"type": "DELETE", "lineNumber": 85, "oldContent": "                                    ),"}, {"type": "DELETE", "lineNumber": 86, "oldContent": "                                  ),"}, {"type": "DELETE", "lineNumber": 87, "oldContent": "                                ],"}, {"type": "INSERT", "lineNumber": 76, "content": "                                answersList[index].userName!,"}, {"type": "DELETE", "lineNumber": 89, "oldContent": "                              ),"}, {"type": "DELETE", "lineNumber": 90, "oldContent": "                              const Divider(color: AppColors.greyColor),"}, {"type": "DELETE", "lineNumber": 91, "oldContent": "                                answersList[index].answer!,"}, {"type": "INSERT", "lineNumber": 78, "content": "                                  fontSize: 14.0.sp,"}, {"type": "INSERT", "lineNumber": 80, "content": "                                  color: AppColors.blackColor,"}, {"type": "DELETE", "lineNumber": 94, "oldContent": "                                  fontSize: 12.0.sp,"}, {"type": "DELETE", "lineNumber": 96, "oldContent": "                                  color: AppColors.blackColor,"}, {"type": "INSERT", "lineNumber": 85, "content": "                          const Divider(color: AppColors.greyColor),"}, {"type": "INSERT", "lineNumber": 86, "content": "                          Text("}, {"type": "INSERT", "lineNumber": 87, "content": "                            answersList[index].answer!,"}, {"type": "INSERT", "lineNumber": 88, "content": "                            style: TextStyle("}, {"type": "INSERT", "lineNumber": 89, "content": "                              fontSize: 12.0.sp,"}, {"type": "INSERT", "lineNumber": 90, "content": "                              color: AppColors.blackColor,"}, {"type": "INSERT", "lineNumber": 91, "content": "                              fontWeight: FontWeight.bold,"}, {"type": "INSERT", "lineNumber": 92, "content": "                            ),"}, {"type": "INSERT", "lineNumber": 93, "content": "                          ),"}, {"type": "INSERT", "lineNumber": 94, "content": "                        ],"}, {"type": "DELETE", "lineNumber": 100, "oldContent": "                        ),"}, {"type": "INSERT", "lineNumber": 97, "content": "                  ),"}, {"type": "INSERT", "lineNumber": 98, "content": "                ),"}, {"type": "DELETE", "lineNumber": 103, "oldContent": "                    ElevatedButton("}, {"type": "DELETE", "lineNumber": 104, "oldContent": "                      onPressed: () {"}, {"type": "DELETE", "lineNumber": 105, "oldContent": "                        Navigator.pop(context);"}, {"type": "DELETE", "lineNumber": 106, "oldContent": "                      },"}, {"type": "DELETE", "lineNumber": 107, "oldContent": "                      style: ElevatedButton.styleFrom("}, {"type": "DELETE", "lineNumber": 108, "oldContent": "                        foregroundColor: Colors.white,"}, {"type": "DELETE", "lineNumber": 109, "oldContent": "                        backgroundColor: AppColors.midLevelGreenColor,"}, {"type": "DELETE", "lineNumber": 110, "oldContent": "                        textStyle: TextStyle("}, {"type": "DELETE", "lineNumber": 111, "oldContent": "                          fontSize: 16.0.sp,"}, {"type": "DELETE", "lineNumber": 112, "oldContent": "                          fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 113, "oldContent": "                        ),"}, {"type": "INSERT", "lineNumber": 100, "content": "                ElevatedButton("}, {"type": "INSERT", "lineNumber": 101, "content": "                  onPressed: () {"}, {"type": "INSERT", "lineNumber": 102, "content": "                    Navigator.pop(context);"}, {"type": "INSERT", "lineNumber": 103, "content": "                  },"}, {"type": "INSERT", "lineNumber": 104, "content": "                  style: ElevatedButton.styleFrom("}, {"type": "INSERT", "lineNumber": 105, "content": "                    foregroundColor: Colors.white,"}, {"type": "INSERT", "lineNumber": 106, "content": "                    backgroundColor: AppColors.midLevelGreenColor,"}, {"type": "INSERT", "lineNumber": 107, "content": "                    textStyle: TextStyle("}, {"type": "INSERT", "lineNumber": 108, "content": "                      fontSize: 16.0.sp,"}, {"type": "INSERT", "lineNumber": 109, "content": "                      fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 115, "oldContent": "                        minimumSize: <PERSON><PERSON>(398.0.w, 46.0.h),"}, {"type": "DELETE", "lineNumber": 116, "oldContent": "                        shape: RoundedRectangleBorder("}, {"type": "DELETE", "lineNumber": 117, "oldContent": "                          borderRadius: BorderRadius.all("}, {"type": "DELETE", "lineNumber": 118, "oldContent": "                            Radius.circular(32.0.r),"}, {"type": "DELETE", "lineNumber": 119, "oldContent": "                          ),"}, {"type": "DELETE", "lineNumber": 120, "oldContent": "                        ),"}, {"type": "DELETE", "lineNumber": 121, "oldContent": "                      ),"}, {"type": "INSERT", "lineNumber": 111, "content": "                    minimumSize: <PERSON><PERSON>(398.0.w, 46.0.h),"}, {"type": "INSERT", "lineNumber": 112, "content": "                    shape: RoundedRectangleBorder("}, {"type": "INSERT", "lineNumber": 113, "content": "                      borderRadius: BorderRadius.all(Radius.circular(32.0.r)),"}, {"type": "INSERT", "lineNumber": 114, "content": "                    ),"}, {"type": "INSERT", "lineNumber": 115, "content": "                  ),"}, {"type": "INSERT", "lineNumber": 116, "content": "                  child: const Text(\"OK\"),"}, {"type": "DELETE", "lineNumber": 123, "oldContent": "                      child: const Text(\"OK\"),"}, {"type": "INSERT", "lineNumber": 118, "content": "              ],"}, {"type": "DELETE", "lineNumber": 126, "oldContent": "                  ],"}, {"type": "INSERT", "lineNumber": 121, "content": "        ),"}, {"type": "INSERT", "lineNumber": 122, "content": "      ),"}, {"type": "DELETE", "lineNumber": 129, "oldContent": "              ),"}]}, {"timestamp": 1755085042441, "changes": [{"type": "DELETE", "lineNumber": 17, "oldContent": "      transitionBuilder: (context, animation1, animation2, child) => ScaleTransition("}, {"type": "DELETE", "lineNumber": 18, "oldContent": "        scale: animation1,"}, {"type": "DELETE", "lineNumber": 19, "oldContent": "        child: <PERSON><PERSON><PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 20, "oldContent": "          contentPadding: EdgeInsets.all(10.0.h),"}, {"type": "DELETE", "lineNumber": 21, "oldContent": "          shape: RoundedRectangleBorder("}, {"type": "DELETE", "lineNumber": 22, "oldContent": "            borderRadius: BorderRadius.circular(20.0),"}, {"type": "DELETE", "lineNumber": 23, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 24, "oldContent": "          backgroundColor: Colors.white,"}, {"type": "DELETE", "lineNumber": 25, "oldContent": "          title: Center("}, {"type": "INSERT", "lineNumber": 17, "content": "      transitionBuilder: (context, animation1, animation2, child) =>"}, {"type": "INSERT", "lineNumber": 18, "content": "          ScaleTransition("}, {"type": "INSERT", "lineNumber": 19, "content": "            scale: animation1,"}, {"type": "INSERT", "lineNumber": 20, "content": "            child: <PERSON><PERSON><PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 21, "content": "              contentPadding: EdgeInsets.all(10.0.h),"}, {"type": "INSERT", "lineNumber": 22, "content": "              shape: RoundedRectangleBorder("}, {"type": "INSERT", "lineNumber": 23, "content": "                borderRadius: BorderRadius.circular(20.0),"}, {"type": "DELETE", "lineNumber": 27, "oldContent": "            child: Text("}, {"type": "DELETE", "lineNumber": 28, "oldContent": "              \"Answers\","}, {"type": "DELETE", "lineNumber": 29, "oldContent": "              style: TextStyle("}, {"type": "DELETE", "lineNumber": 30, "oldContent": "                fontSize: 20.0.sp,"}, {"type": "DELETE", "lineNumber": 31, "oldContent": "                fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 32, "oldContent": "                color: AppColors.blackColor,"}, {"type": "DELETE", "lineNumber": 33, "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 34, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 35, "oldContent": "          content: SizedBox("}, {"type": "DELETE", "lineNumber": 36, "oldContent": "            height: 200.0.h,"}, {"type": "DELETE", "lineNumber": 37, "oldContent": "            width: double.maxFinite,"}, {"type": "DELETE", "lineNumber": 38, "oldContent": "            child: <PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 39, "oldContent": "              children: ["}, {"type": "DELETE", "lineNumber": 40, "oldContent": "                Expanded("}, {"type": "DELETE", "lineNumber": 41, "oldContent": "                  child: <PERSON><PERSON><PERSON><PERSON>.separated("}, {"type": "DELETE", "lineNumber": 42, "oldContent": "                    separatorBuilder: (context, index) =>"}, {"type": "DELETE", "lineNumber": 43, "oldContent": "                        SizedBox(height: 10.0.h),"}, {"type": "DELETE", "lineNumber": 44, "oldContent": "                    itemCount: answersList.length,"}, {"type": "DELETE", "lineNumber": 45, "oldContent": "                    itemBuilder: (context, index) => Container("}, {"type": "DELETE", "lineNumber": 46, "oldContent": "                      padding: EdgeInsets.all(10.0.h),"}, {"type": "DELETE", "lineNumber": 47, "oldContent": "                      decoration: BoxDecoration("}, {"type": "DELETE", "lineNumber": 48, "oldContent": "                        border: Border.all(color: AppColors.greyColor),"}, {"type": "DELETE", "lineNumber": 49, "oldContent": "                        borderRadius: BorderRadius.all(Radius.circular(16.0.r)),"}, {"type": "DELETE", "lineNumber": 50, "oldContent": "                      ),"}, {"type": "DELETE", "lineNumber": 51, "oldContent": "                      child: <PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 52, "oldContent": "                        crossAxisAlignment: CrossAxisAlignment.start,"}, {"type": "DELETE", "lineNumber": 53, "oldContent": "                        children: ["}, {"type": "DELETE", "lineNumber": 54, "oldContent": "                          Row("}, {"type": "DELETE", "lineNumber": 55, "oldContent": "                              // ClipOval("}, {"type": "INSERT", "lineNumber": 25, "content": "              backgroundColor: Colors.white,"}, {"type": "INSERT", "lineNumber": 26, "content": "              title: Center("}, {"type": "INSERT", "lineNumber": 27, "content": "                child: Text("}, {"type": "INSERT", "lineNumber": 28, "content": "                  \"Answers\","}, {"type": "INSERT", "lineNumber": 29, "content": "                  style: TextStyle("}, {"type": "INSERT", "lineNumber": 30, "content": "                    fontSize: 20.0.sp,"}, {"type": "INSERT", "lineNumber": 31, "content": "                    fontWeight: FontWeight.bold,"}, {"type": "INSERT", "lineNumber": 32, "content": "                    color: AppColors.blackColor,"}, {"type": "INSERT", "lineNumber": 33, "content": "                  ),"}, {"type": "INSERT", "lineNumber": 34, "content": "                ),"}, {"type": "INSERT", "lineNumber": 35, "content": "              ),"}, {"type": "INSERT", "lineNumber": 36, "content": "              content: SizedBox("}, {"type": "INSERT", "lineNumber": 37, "content": "                height: 200.0.h,"}, {"type": "INSERT", "lineNumber": 38, "content": "                width: double.maxFinite,"}, {"type": "INSERT", "lineNumber": 39, "content": "                child: <PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 40, "content": "                  children: ["}, {"type": "INSERT", "lineNumber": 41, "content": "                    Expanded("}, {"type": "INSERT", "lineNumber": 42, "content": "                      child: <PERSON><PERSON><PERSON><PERSON>.separated("}, {"type": "INSERT", "lineNumber": 43, "content": "                        separatorBuilder: (context, index) =>"}, {"type": "INSERT", "lineNumber": 44, "content": "                            SizedBox(height: 10.0.h),"}, {"type": "INSERT", "lineNumber": 45, "content": "                        itemCount: answersList.length,"}, {"type": "INSERT", "lineNumber": 46, "content": "                        itemBuilder: (context, index) => Container("}, {"type": "INSERT", "lineNumber": 47, "content": "                          padding: EdgeInsets.all(10.0.h),"}, {"type": "INSERT", "lineNumber": 48, "content": "                          decoration: BoxDecoration("}, {"type": "INSERT", "lineNumber": 49, "content": "                            border: Border.all(color: AppColors.greyColor),"}, {"type": "INSERT", "lineNumber": 50, "content": "                            borderRadius: BorderRadius.all("}, {"type": "INSERT", "lineNumber": 51, "content": "                              Radius.circular(16.0.r),"}, {"type": "INSERT", "lineNumber": 52, "content": "                            ),"}, {"type": "INSERT", "lineNumber": 53, "content": "                          ),"}, {"type": "INSERT", "lineNumber": 54, "content": "                          child: <PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 55, "content": "                            crossAxisAlignment: CrossAxisAlignment.start,"}, {"type": "DELETE", "lineNumber": 57, "oldContent": "                              //   child: answersList[index].userImage != null"}, {"type": "DELETE", "lineNumber": 58, "oldContent": "                              //       ? CommonComponents.imageWithNetworkCache("}, {"type": "DELETE", "lineNumber": 59, "oldContent": "                              //           context: context,"}, {"type": "DELETE", "lineNumber": 60, "oldContent": "                              //           image:"}, {"type": "DELETE", "lineNumber": 61, "oldContent": "                              //               answersList[index].userImage!,"}, {"type": "DELETE", "lineNumber": 62, "oldContent": "                              //           height: 40.0.h,"}, {"type": "DELETE", "lineNumber": 63, "oldContent": "                              //           width: 40.0.w,"}, {"type": "DELETE", "lineNumber": 64, "oldContent": "                              //           fit: BoxFit.contain,"}, {"type": "DELETE", "lineNumber": 65, "oldContent": "                              //         )"}, {"type": "DELETE", "lineNumber": 66, "oldContent": "                              //       : CommonComponents.imageAssetWithCache("}, {"type": "DELETE", "lineNumber": 67, "oldContent": "                              //           context: context,"}, {"type": "DELETE", "lineNumber": 68, "oldContent": "                              //           image: AppImages.userPhoto,"}, {"type": "DELETE", "lineNumber": 69, "oldContent": "                              //           height: 40.0.h,"}, {"type": "DELETE", "lineNumber": 70, "oldContent": "                              //           width: 40.0.w,"}, {"type": "DELETE", "lineNumber": 71, "oldContent": "                              //           fit: BoxFit.contain,"}, {"type": "DELETE", "lineNumber": 72, "oldContent": "                              //         ),"}, {"type": "DELETE", "lineNumber": 73, "oldContent": "                              // ),"}, {"type": "DELETE", "lineNumber": 74, "oldContent": "                              SizedBox(width: 5.0.w),"}, {"type": "DELETE", "lineNumber": 75, "oldContent": "                                answersList[index].userName!,"}, {"type": "DELETE", "lineNumber": 76, "oldContent": "                                  fontSize: 14.0.sp,"}, {"type": "DELETE", "lineNumber": 77, "oldContent": "                                  color: AppColors.blackColor,"}, {"type": "INSERT", "lineNumber": 57, "content": "                              Row("}, {"type": "INSERT", "lineNumber": 58, "content": "                                children: ["}, {"type": "INSERT", "lineNumber": 59, "content": "                                  ClipOval("}, {"type": "INSERT", "lineNumber": 60, "content": "                                    child: answersList[index].userImage != null"}, {"type": "INSERT", "lineNumber": 61, "content": "                                        ? CommonComponents.imageWithNetworkCache("}, {"type": "INSERT", "lineNumber": 62, "content": "                                            context: context,"}, {"type": "INSERT", "lineNumber": 63, "content": "                                            image:"}, {"type": "INSERT", "lineNumber": 64, "content": "                                                answersList[index].userImage!,"}, {"type": "INSERT", "lineNumber": 65, "content": "                                            height: 40.0.h,"}, {"type": "INSERT", "lineNumber": 66, "content": "                                            width: 40.0.w,"}, {"type": "INSERT", "lineNumber": 67, "content": "                                            fit: BoxFit.contain,"}, {"type": "INSERT", "lineNumber": 68, "content": "                                          )"}, {"type": "INSERT", "lineNumber": 69, "content": "                                        : CommonComponents.imageAssetWithCache("}, {"type": "INSERT", "lineNumber": 70, "content": "                                            context: context,"}, {"type": "INSERT", "lineNumber": 71, "content": "                                            image: AppImages.userPhoto,"}, {"type": "INSERT", "lineNumber": 72, "content": "                                            height: 40.0.h,"}, {"type": "INSERT", "lineNumber": 73, "content": "                                            width: 40.0.w,"}, {"type": "INSERT", "lineNumber": 74, "content": "                                            fit: BoxFit.contain,"}, {"type": "INSERT", "lineNumber": 75, "content": "                                          ),"}, {"type": "INSERT", "lineNumber": 76, "content": "                                  ),"}, {"type": "INSERT", "lineNumber": 77, "content": "                                  SizedBox(width: 5.0.w),"}, {"type": "INSERT", "lineNumber": 78, "content": "                                  Text("}, {"type": "INSERT", "lineNumber": 79, "content": "                                    answersList[index].userName!,"}, {"type": "INSERT", "lineNumber": 80, "content": "                                    style: TextStyle("}, {"type": "INSERT", "lineNumber": 81, "content": "                                      fontSize: 14.0.sp,"}, {"type": "INSERT", "lineNumber": 82, "content": "                                      fontWeight: FontWeight.bold,"}, {"type": "INSERT", "lineNumber": 83, "content": "                                      color: AppColors.blackColor,"}, {"type": "INSERT", "lineNumber": 84, "content": "                                    ),"}, {"type": "INSERT", "lineNumber": 85, "content": "                                  ),"}, {"type": "INSERT", "lineNumber": 86, "content": "                                ],"}, {"type": "INSERT", "lineNumber": 87, "content": "                              ),"}, {"type": "INSERT", "lineNumber": 88, "content": "                              const Divider(color: AppColors.greyColor),"}, {"type": "DELETE", "lineNumber": 79, "oldContent": "                          const Divider(color: AppColors.greyColor),"}, {"type": "DELETE", "lineNumber": 80, "oldContent": "                          Text("}, {"type": "DELETE", "lineNumber": 81, "oldContent": "                            answersList[index].answer!,"}, {"type": "DELETE", "lineNumber": 82, "oldContent": "                            style: TextStyle("}, {"type": "INSERT", "lineNumber": 90, "content": "                                answersList[index].answer!,"}, {"type": "DELETE", "lineNumber": 84, "oldContent": "                              fontSize: 12.0.sp,"}, {"type": "DELETE", "lineNumber": 85, "oldContent": "                              color: AppColors.blackColor,"}, {"type": "DELETE", "lineNumber": 86, "oldContent": "                              fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 87, "oldContent": "                            ),"}, {"type": "INSERT", "lineNumber": 92, "content": "                                  fontSize: 12.0.sp,"}, {"type": "INSERT", "lineNumber": 93, "content": "                                  color: AppColors.blackColor,"}, {"type": "DELETE", "lineNumber": 89, "oldContent": "                          ),"}, {"type": "DELETE", "lineNumber": 91, "oldContent": "                        ],"}, {"type": "DELETE", "lineNumber": 93, "oldContent": "                  ),"}, {"type": "DELETE", "lineNumber": 95, "oldContent": "                ),"}, {"type": "INSERT", "lineNumber": 99, "content": "                        ),"}, {"type": "DELETE", "lineNumber": 98, "oldContent": "                ElevatedButton("}, {"type": "DELETE", "lineNumber": 99, "oldContent": "                  onPressed: () {"}, {"type": "DELETE", "lineNumber": 101, "oldContent": "                    Navigator.pop(context);"}, {"type": "DELETE", "lineNumber": 103, "oldContent": "                  },"}, {"type": "DELETE", "lineNumber": 104, "oldContent": "                  style: ElevatedButton.styleFrom("}, {"type": "DELETE", "lineNumber": 105, "oldContent": "                    foregroundColor: Colors.white,"}, {"type": "DELETE", "lineNumber": 106, "oldContent": "                    backgroundColor: AppColors.midLevelGreenColor,"}, {"type": "DELETE", "lineNumber": 107, "oldContent": "                    textStyle: TextStyle("}, {"type": "DELETE", "lineNumber": 108, "oldContent": "                      fontSize: 16.0.sp,"}, {"type": "DELETE", "lineNumber": 109, "oldContent": "                      fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 110, "oldContent": "                    minimumSize: <PERSON><PERSON>(398.0.w, 46.0.h),"}, {"type": "DELETE", "lineNumber": 111, "oldContent": "                    shape: RoundedRectangleBorder("}, {"type": "DELETE", "lineNumber": 112, "oldContent": "                      borderRadius: BorderRadius.all(Radius.circular(32.0.r)),"}, {"type": "INSERT", "lineNumber": 103, "content": "                    ElevatedButton("}, {"type": "INSERT", "lineNumber": 104, "content": "                      onPressed: () {"}, {"type": "INSERT", "lineNumber": 105, "content": "                        Navigator.pop(context);"}, {"type": "INSERT", "lineNumber": 106, "content": "                      },"}, {"type": "INSERT", "lineNumber": 107, "content": "                      style: ElevatedButton.styleFrom("}, {"type": "INSERT", "lineNumber": 108, "content": "                        foregroundColor: Colors.white,"}, {"type": "INSERT", "lineNumber": 109, "content": "                        backgroundColor: AppColors.midLevelGreenColor,"}, {"type": "INSERT", "lineNumber": 110, "content": "                        textStyle: TextStyle("}, {"type": "INSERT", "lineNumber": 111, "content": "                          fontSize: 16.0.sp,"}, {"type": "INSERT", "lineNumber": 112, "content": "                          fontWeight: FontWeight.bold,"}, {"type": "INSERT", "lineNumber": 113, "content": "                        ),"}, {"type": "INSERT", "lineNumber": 114, "content": "                        minimumSize: <PERSON><PERSON>(398.0.w, 46.0.h),"}, {"type": "INSERT", "lineNumber": 115, "content": "                        shape: RoundedRectangleBorder("}, {"type": "INSERT", "lineNumber": 116, "content": "                          borderRadius: BorderRadius.all("}, {"type": "INSERT", "lineNumber": 117, "content": "                            Radius.circular(32.0.r),"}, {"type": "INSERT", "lineNumber": 118, "content": "                          ),"}, {"type": "INSERT", "lineNumber": 119, "content": "                        ),"}, {"type": "INSERT", "lineNumber": 120, "content": "                      ),"}, {"type": "INSERT", "lineNumber": 121, "content": "                      child: const Text(\"OK\"),"}, {"type": "DELETE", "lineNumber": 114, "oldContent": "                    ),"}, {"type": "DELETE", "lineNumber": 115, "oldContent": "                  ),"}, {"type": "DELETE", "lineNumber": 116, "oldContent": "                  child: const Text(\"OK\"),"}, {"type": "DELETE", "lineNumber": 117, "oldContent": "              ],"}, {"type": "DELETE", "lineNumber": 118, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 119, "oldContent": "      ),"}, {"type": "INSERT", "lineNumber": 123, "content": "                  ],"}, {"type": "INSERT", "lineNumber": 125, "content": "              ),"}]}, {"timestamp": 1755085064487, "changes": [{"type": "INSERT", "lineNumber": 24, "content": "              ),"}, {"type": "DELETE", "lineNumber": 26, "oldContent": "              ),"}, {"type": "DELETE", "lineNumber": 79, "oldContent": "                              Text("}, {"type": "DELETE", "lineNumber": 85, "oldContent": "                                style: TextStyle("}, {"type": "DELETE", "lineNumber": 91, "oldContent": "                                  fontWeight: FontWeight.bold,"}, {"type": "INSERT", "lineNumber": 89, "content": "                              Text("}, {"type": "DELETE", "lineNumber": 93, "oldContent": "                                ),"}, {"type": "INSERT", "lineNumber": 91, "content": "                                style: TextStyle("}, {"type": "DELETE", "lineNumber": 95, "oldContent": "                              ),"}, {"type": "INSERT", "lineNumber": 94, "content": "                                  fontWeight: FontWeight.bold,"}, {"type": "INSERT", "lineNumber": 95, "content": "                                ),"}, {"type": "INSERT", "lineNumber": 96, "content": "                              ),"}, {"type": "MODIFY", "lineNumber": 100, "content": "                      ),", "oldContent": "                      ),"}, {"type": "DELETE", "lineNumber": 114, "oldContent": "                    ),"}, {"type": "MODIFY", "lineNumber": 122, "content": "                    ),", "oldContent": "                ),"}, {"type": "INSERT", "lineNumber": 123, "content": "                  ],"}, {"type": "INSERT", "lineNumber": 124, "content": "                ),"}, {"type": "INSERT", "lineNumber": 125, "content": "              ),"}, {"type": "DELETE", "lineNumber": 126, "oldContent": "                  ],"}, {"type": "DELETE", "lineNumber": 129, "oldContent": "              ),"}]}]}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/models/home_screens_models/sessions_q&a_models/answers_model.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/models/home_screens_models/sessions_q&a_models/answers_model.dart", "baseContent": "import 'dart:developer';\n\nclass AnswersModel {\n  AnswersModel.fromJson(Map<String, dynamic> jsonData) {\n    log('asfassaf $jsonData');\n    \n    answer = jsonData['answer'];\n    userName = jsonData['user'];\n    userImage = jsonData.containsKey('avatar') ? jsonData['avatar'] : null;\n  }\n  String? answer, userName, userImage;\n}\n", "baseTimestamp": 1755084955952, "deltas": [{"timestamp": 1755084958215, "changes": [{"type": "MODIFY", "lineNumber": 5, "content": "", "oldContent": "    "}]}]}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/screens/home_screens/home_categories_screens/session_q&a_details_screens/questions_screen.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/screens/home_screens/home_categories_screens/session_q&a_details_screens/questions_screen.dart", "baseContent": "import 'package:afa_app/app_config/api_providers/home_screen_categories_providers.dart';\nimport 'package:afa_app/app_config/app_colors.dart';\nimport 'package:afa_app/app_config/app_images.dart';\nimport 'package:afa_app/app_config/common_components.dart';\nimport 'package:afa_app/models/home_screens_models/sessions_q&a_models/question_model.dart';\nimport 'package:afa_app/widgets/home_screens_widgets.dart/questions_and_answers_screens_widgets/questions_screen_widgets.dart';\nimport 'package:flutter/material.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:riverpod_context/riverpod_context.dart';\n\nclass QuestionsScreen extends StatefulWidget {\n  const QuestionsScreen({super.key});\n\n  @override\n  State<QuestionsScreen> createState() => _QuestionsScreenState();\n}\n\nclass _QuestionsScreenState extends State<QuestionsScreen> {\n  Future<List<QuestionsModel>>? _fetchSessionQuestions;\n  @override\n  void initState() {\n    _fetchSessionQuestions = context\n        .read(HomeScreenCategoriesProviders.questionAndAnswersProvidersApis)\n        .getQuestions(context: context);\n    super.initState();\n  }\n\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      body: SafeArea(\n        child: Column(\n          children: [\n            CommonComponents.comonTitleScreen(\n              context: context,\n              title: \"Questions\",\n            ),\n\n            SizedBox(height: 20.0.h),\n            Expanded(\n              child: FutureBuilder(\n                future: _fetchSessionQuestions,\n                builder: (context, AsyncSnapshot<List<QuestionsModel>> snapshot) {\n                  if (snapshot.connectionState == ConnectionState.waiting) {\n                    return Center(\n                      child: CommonComponents.loadingDataFromServer(),\n                    );\n                  } else if (snapshot.data == null) {\n                    return Center(child: CommonComponents.noDataFoundWidget());\n                  } else {\n                    return ListView.separated(\n                      padding: EdgeInsets.all(10.0.h),\n                      separatorBuilder: (context, index) =>\n                          SizedBox(height: 20.0.h),\n                      itemCount: snapshot.data!.length,\n                      itemBuilder: (context, index) => Container(\n                        padding: EdgeInsets.all(10.0.h),\n                        decoration: BoxDecoration(\n                          border: Border.all(color: AppColors.greyColor),\n                          borderRadius: BorderRadius.all(\n                            Radius.circular(16.0.r),\n                          ),\n                        ),\n                        child: Column(\n                          crossAxisAlignment: CrossAxisAlignment.start,\n                          children: [\n                            Row(\n                              crossAxisAlignment: CrossAxisAlignment.center,\n                              children: [\n                                ClipOval(\n                                  child: snapshot.data![index].userImage != null\n                                      ? CommonComponents.imageWithNetworkCache(\n                                          context: context,\n                                          image:\n                                              snapshot.data![index].userImage!,\n                                          height: 40.0.h,\n                                          width: 40.0.w,\n                                          fit: BoxFit.contain,\n                                        )\n                                      : CommonComponents.imageAssetWithCache(\n                                          context: context,\n                                          image: AppImages.userPhoto,\n                                          height: 40.0.h,\n                                          width: 40.0.w,\n                                          fit: BoxFit.contain,\n                                        ),\n                                ),\n                                SizedBox(width: 10.0.w),\n                                Column(\n                                  crossAxisAlignment: CrossAxisAlignment.start,\n                                  children: [\n                                    Text(\n                                      snapshot.data![index].userName!,\n                                      style: TextStyle(\n                                        fontSize: 14.0.sp,\n                                        fontWeight: FontWeight.bold,\n                                        color: AppColors.blackColor,\n                                      ),\n                                    ),\n                                  ],\n                                ),\n                              ],\n                            ),\n                            const Divider(color: AppColors.greyColor),\n                            Text(\n                              snapshot.data![index].question!,\n                              style: TextStyle(\n                                fontSize: 12.0.sp,\n                                fontWeight: FontWeight.bold,\n                                color: AppColors.blackColor,\n                              ),\n                            ),\n                            SizedBox(height: 10.0.h),\n                            // Visibility(\n                            //   visible:\n                            //       snapshot.data![index].answers!.isNotEmpty,\n                            //   child: ElevatedButton(\n                            //     onPressed: () async {\n                            //       QuestionsScreenWidgets.showAnswerAlertWidget(\n                            //         context: context,\n                            //         answersList: snapshot.data![index].answers!,\n                            //       );\n                            //     },\n                            //     style: ElevatedButton.styleFrom(\n                            //       foregroundColor: Colors.white,\n                            //       backgroundColor: AppColors.midLevelGreenColor,\n                            //       textStyle: TextStyle(\n                            //         fontSize: 16.0.sp,\n                            //         fontWeight: FontWeight.bold,\n                            //       ),\n                            //       minimumSize: Size(398.0.w, 46.0.h),\n                            //       shape: RoundedRectangleBorder(\n                            //         borderRadius: BorderRadius.all(\n                            //           Radius.circular(32.0.r),\n                            //         ),\n                            //       ),\n                            //     ),\n                            //     child: const Text(\"View Answers\"),\n                            //   ),\n                            // ),\n                          ],\n                        ),\n                      ),\n                    );\n                  }\n                },\n              ),\n            ),\n          ],\n        ),\n      ),\n    );\n  }\n}\n", "baseTimestamp": 1755085052184, "deltas": [{"timestamp": 1755085062160, "changes": [{"type": "DELETE", "lineNumber": 113, "oldContent": "                            // Visibility("}, {"type": "DELETE", "lineNumber": 114, "oldContent": "                            //   visible:"}, {"type": "DELETE", "lineNumber": 115, "oldContent": "                            //       snapshot.data![index].answers!.isNotEmpty,"}, {"type": "DELETE", "lineNumber": 116, "oldContent": "                            //   child: Eleva<PERSON><PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 117, "oldContent": "                            //     onPressed: () async {"}, {"type": "DELETE", "lineNumber": 118, "oldContent": "                            //       QuestionsScreenWidgets.showAnswerAlertWidget("}, {"type": "DELETE", "lineNumber": 119, "oldContent": "                            //         context: context,"}, {"type": "DELETE", "lineNumber": 120, "oldContent": "                            //         answersList: snapshot.data![index].answers!,"}, {"type": "DELETE", "lineNumber": 121, "oldContent": "                            //       );"}, {"type": "DELETE", "lineNumber": 122, "oldContent": "                            //     },"}, {"type": "DELETE", "lineNumber": 123, "oldContent": "                            //     style: ElevatedButton.styleFrom("}, {"type": "DELETE", "lineNumber": 124, "oldContent": "                            //       foregroundColor: Colors.white,"}, {"type": "DELETE", "lineNumber": 125, "oldContent": "                            //       backgroundColor: AppColors.midLevelGreenColor,"}, {"type": "DELETE", "lineNumber": 126, "oldContent": "                            //       textStyle: TextStyle("}, {"type": "DELETE", "lineNumber": 127, "oldContent": "                            //         fontSize: 16.0.sp,"}, {"type": "DELETE", "lineNumber": 128, "oldContent": "                            //         fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 129, "oldContent": "                            //       ),"}, {"type": "DELETE", "lineNumber": 130, "oldContent": "                            //       minimumSize: Size(398.0.w, 46.0.h),"}, {"type": "DELETE", "lineNumber": 131, "oldContent": "                            //       shape: RoundedRectangleBorder("}, {"type": "DELETE", "lineNumber": 132, "oldContent": "                            //         borderRadius: BorderRadius.all("}, {"type": "DELETE", "lineNumber": 133, "oldContent": "                            //           Radius.circular(32.0.r),"}, {"type": "DELETE", "lineNumber": 134, "oldContent": "                            //         ),"}, {"type": "DELETE", "lineNumber": 135, "oldContent": "                            //       ),"}, {"type": "DELETE", "lineNumber": 136, "oldContent": "                            //     ),"}, {"type": "DELETE", "lineNumber": 137, "oldContent": "                            //     child: const Text(\"View Answers\"),"}, {"type": "DELETE", "lineNumber": 138, "oldContent": "                            //   ),"}, {"type": "DELETE", "lineNumber": 139, "oldContent": "                            // ),"}, {"type": "INSERT", "lineNumber": 113, "content": "                            Visibility("}, {"type": "INSERT", "lineNumber": 114, "content": "                              visible:"}, {"type": "INSERT", "lineNumber": 115, "content": "                                  snapshot.data![index].answers!.isNotEmpty,"}, {"type": "INSERT", "lineNumber": 116, "content": "                              child: El<PERSON><PERSON><PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 117, "content": "                                onPressed: () async {"}, {"type": "INSERT", "lineNumber": 118, "content": "                                  QuestionsScreenWidgets.showAnswerAlertWidget("}, {"type": "INSERT", "lineNumber": 119, "content": "                                    context: context,"}, {"type": "INSERT", "lineNumber": 120, "content": "                                    answersList: snapshot.data![index].answers!,"}, {"type": "INSERT", "lineNumber": 121, "content": "                                  );"}, {"type": "INSERT", "lineNumber": 122, "content": "                                },"}, {"type": "INSERT", "lineNumber": 123, "content": "                                style: ElevatedButton.styleFrom("}, {"type": "INSERT", "lineNumber": 124, "content": "                                  foregroundColor: Colors.white,"}, {"type": "INSERT", "lineNumber": 125, "content": "                                  backgroundColor: AppColors.midLevelGreenColor,"}, {"type": "INSERT", "lineNumber": 126, "content": "                                  textStyle: TextStyle("}, {"type": "INSERT", "lineNumber": 127, "content": "                                    fontSize: 16.0.sp,"}, {"type": "INSERT", "lineNumber": 128, "content": "                                    fontWeight: FontWeight.bold,"}, {"type": "INSERT", "lineNumber": 129, "content": "                                  ),"}, {"type": "INSERT", "lineNumber": 130, "content": "                                  minimumSize: <PERSON><PERSON>(398.0.w, 46.0.h),"}, {"type": "INSERT", "lineNumber": 131, "content": "                                  shape: RoundedRectangleBorder("}, {"type": "INSERT", "lineNumber": 132, "content": "                                    borderRadius: BorderRadius.all("}, {"type": "INSERT", "lineNumber": 133, "content": "                                      Radius.circular(32.0.r),"}, {"type": "INSERT", "lineNumber": 134, "content": "                                    ),"}, {"type": "INSERT", "lineNumber": 135, "content": "                                  ),"}, {"type": "INSERT", "lineNumber": 136, "content": "                                ),"}, {"type": "INSERT", "lineNumber": 137, "content": "                                child: const Text(\"View Answers\"),"}, {"type": "INSERT", "lineNumber": 138, "content": "                              ),"}, {"type": "INSERT", "lineNumber": 139, "content": "                            ),"}]}]}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/models/home_screens_models/sessions_q&a_models/question_model.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/models/home_screens_models/sessions_q&a_models/question_model.dart", "baseContent": "import 'dart:developer';\n\nimport 'package:afa_app/models/home_screens_models/sessions_q&a_models/answers_model.dart';\n\nclass QuestionsModel {\n  QuestionsModel.fromJson(Map<String, dynamic> jsonData) {\n    log('asfasfasf ${jsonData}');\n\n    question = jsonData['question'];\n    userName = jsonData['user'];\n    userImage = jsonData.containsKey('avatar') ? jsonData['avatar'] : null;\n    answers = (jsonData['answers'] as List)\n        .map((answer) => AnswersModel.fromJson(answer))\n        .toList();\n  }\n  String? question, userName, userImage;\n  List<AnswersModel>? answers;\n}\n", "baseTimestamp": 1755085099506}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/screens/home_screens/inbox_screen.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/screens/home_screens/inbox_screen.dart", "baseContent": "import 'package:afa_app/app_config/api_providers/chat_providers.dart';\nimport 'package:afa_app/app_config/app_colors.dart';\nimport 'package:afa_app/app_config/common_components.dart';\nimport 'package:afa_app/app_config/routes.dart';\nimport 'package:afa_app/models/chat_models/inbox_model.dart';\nimport 'package:flutter/material.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:riverpod_context/riverpod_context.dart';\n\nclass MessagesScreen extends StatefulWidget {\n  const MessagesScreen({super.key});\n\n  @override\n  State<MessagesScreen> createState() => _MessagesScreenState();\n}\n\nclass _MessagesScreenState extends State<MessagesScreen> {\n  Future<List<InboxModel>>? _fetchChatInbox;\n\n  @override\n  void initState() {\n    _fetchChatInbox = context\n        .read(ChatProviders.inboxProvidersApis)\n        .getChatInbox(context: context);\n    super.initState();\n  }\n\n  @override\n  Widget build(BuildContext context) {\n    return FutureBuilder(\n      future: _fetchChatInbox,\n      builder: (context, AsyncSnapshot<List<InboxModel>> snapshot) {\n        if (snapshot.connectionState == ConnectionState.waiting) {\n          return Center(child: CommonComponents.loadingDataFromServer());\n        } else if (snapshot.data == null) {\n          return Center(child: CommonComponents.noDataFoundWidget());\n        } else {\n          return ListView.separated(\n            physics: const BouncingScrollPhysics(),\n            shrinkWrap: true,\n            separatorBuilder: (context, index) =>\n                const Divider(color: AppColors.greyColor),\n            itemCount: snapshot.data!.length,\n            itemBuilder: (context, index) => InkWell(\n              onTap: () {\n                context\n                    .read(ChatProviders.inboxProvidersApis)\n                    .setThreadID(snapshot.data![index].threadId!);\n                // print(context.read(ChatProviders.inboxProvidersApis).threadID);\n\n                Navigator.pushNamed(context, PATHS.chatScreen);\n              },\n              child: Row(\n                mainAxisAlignment: MainAxisAlignment.spaceBetween,\n                children: [\n                  Row(\n                    children: [\n                      ClipOval(\n                        child: CommonComponents.imageWithNetworkCache(\n                          context: context,\n                          image: snapshot.data![index].senderUserImage!,\n                          height: 40.0.h,\n                          width: 40.0.w,\n                          fit: BoxFit.contain,\n                        ),\n                      ),\n                      SizedBox(width: 5.0.w),\n                      Column(\n                        crossAxisAlignment: CrossAxisAlignment.start,\n                        children: [\n                          Text(\n                            snapshot.data![index].senderUserName!,\n                            style: TextStyle(\n                              fontSize: 14.0.sp,\n                              fontWeight: FontWeight.bold,\n                            ),\n                          ),\n                        ],\n                      ),\n                    ],\n                  ),\n                  Visibility(\n                    visible: snapshot.data![index].unreadCount != 0,\n                    child: Container(\n                      height: 24.0.h,\n                      width: 24.0.w,\n                      alignment: Alignment.center,\n                      decoration: const BoxDecoration(\n                        shape: BoxShape.circle,\n                        color: Colors.red,\n                      ),\n                      child: Text(\n                        snapshot.data![index].unreadCount.toString(),\n                        style: TextStyle(\n                          fontSize: 10.0.sp,\n                          color: Colors.white,\n                          fontWeight: FontWeight.bold,\n                        ),\n                      ),\n                    ),\n                  ),\n                ],\n              ),\n            ),\n          );\n        }\n      },\n    );\n  }\n}\n", "baseTimestamp": 1755085541465}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/screens/home_screens/main_screen.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/screens/home_screens/main_screen.dart", "baseContent": "import 'package:afa_app/app_config/api_keys.dart';\nimport 'package:afa_app/app_config/api_providers/home_screen_categories_providers.dart';\nimport 'package:afa_app/app_config/api_providers/profile_providers.dart';\nimport 'package:afa_app/app_config/app_colors.dart';\nimport 'package:afa_app/app_config/common_components.dart';\nimport 'package:afa_app/app_config/routes.dart';\nimport 'package:afa_app/notification_handler.dart';\nimport 'package:afa_app/screens/home_screens/announcement_screen.dart';\nimport 'package:afa_app/screens/home_screens/home_screen.dart';\nimport 'package:afa_app/screens/home_screens/inbox_screen.dart';\nimport 'package:afa_app/screens/home_screens/polls_screen.dart';\nimport 'package:afa_app/widgets/home_screens_widgets.dart/main_screen_widgets.dart';\nimport 'package:badges/badges.dart' as badges;\nimport 'package:firebase_core/firebase_core.dart';\nimport 'package:firebase_messaging/firebase_messaging.dart';\nimport 'package:flutter/material.dart';\nimport 'package:flutter_riverpod/flutter_riverpod.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:riverpod_context/riverpod_context.dart';\n\n// Here the _messageHandler callback, will be called when application is in background or terminated state\n@pragma('vm:entry-point')\nFuture<void> _messageHandler(RemoteMessage message) async {\n  await Firebase.initializeApp();\n}\n\nclass MainScreen extends StatefulWidget {\n  const MainScreen({super.key});\n\n  @override\n  State<MainScreen> createState() => _MainScreenState();\n}\n\nclass _MainScreenState extends State<MainScreen> {\n  final List<Widget> _pages = const [\n    HomeScreen(),\n    AnnouncementScreen(),\n    PollsScreen(),\n    MessagesScreen(),\n  ];\n\n  String? userImage, userName, userPosition;\n\n  @override\n  void initState() {\n    //USE FIREBASE TOKEN TO REGITSER DEVICE TO FIREBASE MESSAGING AND EVERY TIME RUN APP THIS TOKEN CHANGED.\n\n    FirebaseMessaging.instance.getToken().then((value) => debugPrint(value));\n\n    // // execute navigation when app in background\n    FirebaseMessaging.onMessageOpenedApp.listen((event) {\n      // print(message.data['info']);\n    });\n\n    // ///THIS [onMessage] USE TO RETRIVE ALERT DIALIG WHEN APP IN FOREGROUND NOT SHOW NOTIFICATION ON STATUS BAR\n    FirebaseMessaging.onMessage.listen((RemoteMessage event) async {\n      NotificationHandler.init();\n      await NotificationHandler.pushNotification(event);\n    });\n\n    FirebaseMessaging.onBackgroundMessage(_messageHandler);\n\n    Future.delayed(Duration.zero, () async {\n      final String userImageCached = await CommonComponents.getSavedData(\n        ApiKeys.userImage,\n      );\n      final String userNameCached = await CommonComponents.getSavedData(\n        ApiKeys.userName,\n      );\n      final String userPositionCached = await CommonComponents.getSavedData(\n        ApiKeys.userPosition,\n      );\n      if (!mounted) return;\n\n      setState(() {\n        userName = userNameCached;\n        userImage = userImageCached;\n        userPosition = userPositionCached;\n      });\n    });\n\n    super.initState();\n  }\n\n  @override\n  void didChangeDependencies() {\n    context\n        .read(HomeScreenCategoriesProviders.notificationsScreenProvidersApis)\n        .getAllNotifications(context: context, showBadgeWithHome: true);\n    super.didChangeDependencies();\n  }\n\n  @override\n  Widget build(BuildContext context) {\n    if (userName == null || userImage == null || userPosition == null) {\n      return Center(child: CommonComponents.loadingDataFromServer());\n    }\n    return PopScope(\n      onPopInvokedWithResult: (value, popInvokedBy) {\n        context.read(ProfileProviders.commonApis).setCurrentIndex(0);\n      },\n      canPop: false,\n      child: Consumer(\n        builder: (context, watch, child) => Scaffold(\n          bottomNavigationBar: BottomNavigationBar(\n            selectedFontSize: 12.0.sp,\n            unselectedFontSize: 12.0.sp,\n            selectedItemColor: AppColors.midLevelGreenColor,\n            unselectedItemColor: AppColors.greyColor,\n            selectedLabelStyle: const TextStyle(fontWeight: FontWeight.bold),\n            unselectedLabelStyle: const TextStyle(fontWeight: FontWeight.bold),\n            type: BottomNavigationBarType.fixed,\n            onTap: (value) {\n              context.read(ProfileProviders.commonApis).setCurrentIndex(value);\n            },\n            currentIndex: watch\n                .watch(ProfileProviders.commonApis)\n                .currentIndexForMainScreen,\n            items: MainScreenWidgets.bottomBarItemsList\n                .map(\n                  (items) => BottomNavigationBarItem(\n                    activeIcon: CommonComponents.imageAssetWithCache(\n                      context: context,\n                      image: items['active_icon'],\n                      height: 24.0.h,\n                      width: 24.0.w,\n                      fit: BoxFit.contain,\n                    ),\n                    icon: CommonComponents.imageAssetWithCache(\n                      context: context,\n                      image: items['icon'],\n                      height: 24.0.h,\n                      width: 24.0.w,\n                      fit: BoxFit.contain,\n                    ),\n                    label: items['title'],\n                  ),\n                )\n                .toList(),\n          ),\n          body: SafeArea(\n            child: SingleChildScrollView(\n              padding: EdgeInsets.all(15.0.h),\n              physics: const BouncingScrollPhysics(),\n              child: Column(\n                children: [\n                  Row(\n                    mainAxisAlignment: MainAxisAlignment.spaceBetween,\n                    children: [\n                      InkWell(\n                        onTap: () {\n                          Navigator.pushNamed(context, PATHS.profileScreen);\n                        },\n                        child: Row(\n                          children: [\n                            ClipOval(\n                              child: CommonComponents.imageWithNetworkCache(\n                                context: context,\n                                image: userImage!,\n                                height: 48.0.h,\n                                width: 48.0.w,\n                                fit: BoxFit.contain,\n                              ),\n                            ),\n                            SizedBox(width: 10.0.w),\n                            Column(\n                              crossAxisAlignment: CrossAxisAlignment.start,\n                              children: [\n                                Text(\n                                  userPosition!,\n                                  style: TextStyle(\n                                    fontSize: 14.0.sp,\n                                    color: AppColors.greyColor,\n                                    fontWeight: FontWeight.bold,\n                                  ),\n                                ),\n                                Text(\n                                  userName!,\n                                  style: TextStyle(\n                                    fontSize: 16.0.sp,\n                                    color: AppColors.blackColor,\n                                    fontWeight: FontWeight.bold,\n                                  ),\n                                ),\n                              ],\n                            ),\n                          ],\n                        ),\n                      ),\n                      InkWell(\n                        onTap: () {\n                          Navigator.pushNamed(\n                            context,\n                            PATHS.notificationScreen,\n                          );\n                        },\n                        child: badges.Badge(\n                          showBadge:\n                              watch\n                                      .watch(\n                                        HomeScreenCategoriesProviders\n                                            .notificationsScreenProvidersApis,\n                                      )\n                                      .unreadNotificationCount ==\n                                  0\n                              ? false\n                              : true,\n\n                          badgeContent: Text(\n                            watch\n                                .watch(\n                                  HomeScreenCategoriesProviders\n                                      .notificationsScreenProvidersApis,\n                                )\n                                .unreadNotificationCount\n                                .toString(),\n                            style: TextStyle(\n                              fontSize: 14.0.sp,\n                              color: Colors.white,\n                            ),\n                          ),\n                          badgeStyle: badges.BadgeStyle(\n                            padding: EdgeInsetsGeometry.all(10.h),\n                          ),\n                          child: Icon(Icons.notifications, size: 48.0.h),\n                        ),\n                      ),\n                      // InkWell(\n                      //   onTap: () {\n                      //     Navigator.pushNamed(context, PATHS.notificationScreen);\n                      //   },\n                      //   child: Container(\n                      //     height: 48.0.h,\n                      //     width: 48.0.w,\n                      //     padding: EdgeInsets.all(10.0.h),\n                      //     decoration: const BoxDecoration(\n                      //       shape: BoxShape.circle,\n                      //       color: AppColors.darkGreenColor,\n                      //     ),\n                      //     child: Icon(\n                      //       Icons.notifications,\n                      //       size: 27.0.h,\n                      //       color: Colors.white,\n                      //     ),\n                      //   ),\n                      // ),\n                    ],\n                  ),\n                  SizedBox(height: 40.0.h),\n                  _pages[watch\n                      .watch(ProfileProviders.commonApis)\n                      .currentIndexForMainScreen],\n                ],\n              ),\n            ),\n          ),\n        ),\n      ),\n    );\n  }\n}\n", "baseTimestamp": 1755085541468, "deltas": [{"timestamp": 1755085547441, "changes": [{"type": "MODIFY", "lineNumber": 9, "content": "import 'package:afa_app/screens/home_screens/messages_screen.dart';", "oldContent": "import 'package:afa_app/screens/home_screens/inbox_screen.dart';"}]}]}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/screens/home_screens/chat_screen.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/screens/home_screens/chat_screen.dart", "baseContent": "import 'dart:async';\n\nimport 'package:afa_app/app_config/api_keys.dart';\nimport 'package:afa_app/app_config/api_providers/chat_providers.dart';\nimport 'package:afa_app/app_config/app_colors.dart';\nimport 'package:afa_app/app_config/app_images.dart';\nimport 'package:afa_app/app_config/common_components.dart';\nimport 'package:afa_app/models/chat_models/chat_screen_model.dart';\nimport 'package:flutter/material.dart';\nimport 'package:flutter_riverpod/flutter_riverpod.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:riverpod_context/riverpod_context.dart';\n\nclass ChatScreen extends StatefulWidget {\n  const ChatScreen({super.key});\n  // final int? threadID;\n\n  @override\n  State<ChatScreen> createState() => _ChatScreenState();\n}\n\nclass _ChatScreenState extends State<ChatScreen> {\n  final TextEditingController _messageController = TextEditingController();\n  Timer? _timer;\n  final StateProvider<List<ChatScreenModel>> _messages = StateProvider(\n    (ref) => [],\n  );\n  bool _initialLoading = true;\n  int? _myUserID;\n  String? _myUserName, _myUserImage;\n  bool _isDisposed = false;\n\n  @override\n  void initState() {\n    Future.delayed(Duration.zero, () async {\n      _myUserID = await CommonComponents.getSavedData(ApiKeys.userID);\n      _myUserName = await CommonComponents.getSavedData(ApiKeys.userName);\n      _myUserImage = await CommonComponents.getSavedData(ApiKeys.userImage);\n\n      if (!mounted) return;\n      await context\n          .read(ChatProviders.chatProvidersApis)\n          .getAllChat(context: context)\n          .then((value) {\n            if (mounted) {\n              setState(() {\n                context.read(_messages).clear();\n                context.read(_messages.notifier).state = value;\n                _initialLoading = false;\n              });\n            }\n          });\n    }).then((value) {\n      _timer = Timer.periodic(const Duration(seconds: 5), (timer) async {\n        if (!mounted || _isDisposed) {\n          _timer!.cancel();\n          return;\n        } else {\n          final getNewMessage = await context\n              .read(ChatProviders.chatProvidersApis)\n              .getAllChat(context: context);\n          if (!mounted) return;\n          context.read(_messages.notifier).state = getNewMessage;\n        }\n      });\n    });\n    super.initState();\n  }\n\n  @override\n  void dispose() {\n    _messageController.dispose();\n    _timer?.cancel();\n    _isDisposed = true;\n\n    super.dispose();\n  }\n\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      body: _initialLoading\n          ? Center(child: CommonComponents.loadingDataFromServer())\n          : SafeArea(\n              child: Padding(\n                padding: EdgeInsets.all(15.0.h),\n                child: Column(\n                  children: [\n                    CommonComponents.comonTitleScreen(\n                      context: context,\n                      title: \"Chat\",\n                    ),\n                    SizedBox(height: 10.0.h),\n                    Consumer(\n                      builder: (context, watch, child) => Expanded(\n                        child: ListView.separated(\n                          reverse: true,\n                          physics: const BouncingScrollPhysics(),\n                          separatorBuilder: (context, index) =>\n                              SizedBox(height: 10.0.h),\n                          itemCount: watch.watch(_messages).length,\n                          itemBuilder: (context, index) => Directionality(\n                            textDirection:\n                                watch.watch(_messages)[index].senderID ==\n                                    _myUserID\n                                ? TextDirection.rtl\n                                : TextDirection.ltr,\n\n                            child: Row(\n                              children: [\n                                ClipOval(\n                                  child: CommonComponents.imageWithNetworkCache(\n                                    context: context,\n                                    image: watch\n                                        .watch(_messages)[index]\n                                        .userImage!,\n                                    height: 40.0.h,\n                                    width: 40.0.w,\n                                    fit: BoxFit.contain,\n                                  ),\n                                ),\n                                SizedBox(width: 5.0.w),\n                                Expanded(\n                                  child: Column(\n                                    crossAxisAlignment:\n                                        CrossAxisAlignment.start,\n                                    children: [\n                                      Align(\n                                        alignment:\n                                            watch\n                                                    .watch(_messages)[index]\n                                                    .senderID ==\n                                                _myUserID\n                                            ? Alignment.centerRight\n                                            : Alignment.centerLeft,\n                                        child: Text(\n                                          watch\n                                              .watch(_messages)[index]\n                                              .userName!,\n                                          style: TextStyle(\n                                            fontSize: 12.0.sp,\n                                            fontWeight: FontWeight.bold,\n                                          ),\n                                        ),\n                                      ),\n                                      SizedBox(height: 5.0.h),\n                                      Container(\n                                        padding: EdgeInsets.all(10.0.h),\n                                        decoration: BoxDecoration(\n                                          color:\n                                              watch\n                                                      .watch(_messages)[index]\n                                                      .senderID ==\n                                                  _myUserID\n                                              ? AppColors.darkGreenColor\n                                              : AppColors.lightgreyColor,\n                                          borderRadius:\n                                              watch\n                                                      .watch(_messages)[index]\n                                                      .senderID ==\n                                                  _myUserID\n                                              ? BorderRadius.only(\n                                                  topRight: Radius.circular(\n                                                    24.0.r,\n                                                  ),\n                                                )\n                                              : BorderRadius.only(\n                                                  bottomLeft: Radius.circular(\n                                                    24.0.r,\n                                                  ),\n                                                ),\n                                        ),\n                                        child: Text(\n                                          watch\n                                              .watch(_messages)[index]\n                                              .message!,\n                                          style: TextStyle(\n                                            fontSize: 12.0.sp,\n                                            fontWeight: FontWeight.bold,\n                                            color:\n                                                watch\n                                                        .watch(_messages)[index]\n                                                        .senderID ==\n                                                    _myUserID\n                                                ? Colors.white\n                                                : AppColors.blackColor,\n                                          ),\n                                        ),\n                                      ),\n                                    ],\n                                  ),\n                                ),\n                              ],\n                            ),\n                          ),\n                        ),\n                      ),\n                    ),\n                    Row(\n                      children: [\n                        Expanded(\n                          child: TextFormField(\n                            controller: _messageController,\n                            style: TextStyle(\n                              fontSize: 14.0.sp,\n                              color: AppColors.blackColor,\n                              fontWeight: FontWeight.bold,\n                            ),\n                            decoration: InputDecoration(\n                              hintText: \"Type Message Here\",\n                              hintStyle: TextStyle(fontSize: 12.0.sp),\n\n                              border: OutlineInputBorder(\n                                borderSide: const BorderSide(\n                                  color: AppColors.lightgreyColor,\n                                ),\n                                borderRadius: BorderRadius.all(\n                                  Radius.circular(100.0.r),\n                                ),\n                              ),\n                              enabledBorder: OutlineInputBorder(\n                                borderSide: const BorderSide(\n                                  color: AppColors.lightgreyColor,\n                                ),\n                                borderRadius: BorderRadius.all(\n                                  Radius.circular(100.0.r),\n                                ),\n                              ),\n                              focusedBorder: OutlineInputBorder(\n                                borderSide: const BorderSide(\n                                  color: AppColors.lightgreyColor,\n                                ),\n                                borderRadius: BorderRadius.all(\n                                  Radius.circular(100.0.r),\n                                ),\n                              ),\n                              disabledBorder: OutlineInputBorder(\n                                borderSide: const BorderSide(\n                                  color: AppColors.lightgreyColor,\n                                ),\n                                borderRadius: BorderRadius.all(\n                                  Radius.circular(100.0.r),\n                                ),\n                              ),\n                            ),\n                          ),\n                        ),\n                        SizedBox(width: 10.0.w),\n                        InkWell(\n                          onTap: () async {\n                            if (_messageController.text.isNotEmpty) {\n                              setState(() {\n                                context\n                                    .read(_messages.notifier)\n                                    .state\n                                    .insert(\n                                      0,\n                                      ChatScreenModel(\n                                        senderID: _myUserID,\n                                        message: _messageController.text,\n                                        userName: _myUserName,\n                                        userImage: _myUserImage,\n                                      ),\n                                    );\n                              });\n                              // _messageController.clear();\n\n                              await context\n                                  .read(ChatProviders.chatProvidersApis)\n                                  .sendMessage(\n                                    context: context,\n                                    threadId: context\n                                        .read(ChatProviders.inboxProvidersApis)\n                                        .threadID!,\n                                    message: _messageController.text,\n                                  )\n                                  .then((value) {\n                                    _messageController.clear();\n                                  });\n                            }\n                          },\n                          child: Container(\n                            alignment: Alignment.center,\n                            padding: EdgeInsets.all(10.0.h),\n                            decoration: const BoxDecoration(\n                              color: AppColors.darkGreenColor,\n                              shape: BoxShape.circle,\n                            ),\n                            child: CommonComponents.imageAssetWithCache(\n                              context: context,\n                              image: AppImages.sendMessageIncon,\n                              height: 35.0.h,\n                              width: 35.0.w,\n                              fit: BoxFit.contain,\n                            ),\n                          ),\n                        ),\n                      ],\n                    ),\n                  ],\n                ),\n              ),\n            ),\n    );\n  }\n}\n", "baseTimestamp": 1755085627444, "deltas": [{"timestamp": 1755094241644, "changes": [{"type": "INSERT", "lineNumber": 165, "content": "                                                  topRight: Radius.circular("}, {"type": "INSERT", "lineNumber": 166, "content": "                                                    24.0.r,"}, {"type": "INSERT", "lineNumber": 167, "content": "                                                  ),"}, {"type": "INSERT", "lineNumber": 173, "content": "                                                  bottomLeft: Radius.circular("}, {"type": "INSERT", "lineNumber": 174, "content": "                                                    24.0.r,"}, {"type": "INSERT", "lineNumber": 175, "content": "                                                  ),"}, {"type": "INSERT", "lineNumber": 209, "content": "                                                : Alignment.centerLeft,"}, {"type": "DELETE", "lineNumber": 204, "oldContent": "                                            child: Text("}, {"type": "DELETE", "lineNumber": 212, "oldContent": "                                              textDirection: TextDirection.ltr,"}, {"type": "INSERT", "lineNumber": 219, "content": "                                              textDirection: TextDirection.ltr,"}, {"type": "DELETE", "lineNumber": 215, "oldContent": "                              ],"}, {"type": "INSERT", "lineNumber": 221, "content": "                                          ),"}, {"type": "DELETE", "lineNumber": 218, "oldContent": "                            ),"}, {"type": "DELETE", "lineNumber": 220, "oldContent": "                          ),"}, {"type": "INSERT", "lineNumber": 227, "content": "                            ),"}, {"type": "INSERT", "lineNumber": 228, "content": "                          ),"}]}, {"timestamp": 1755094251014, "changes": [{"type": "DELETE", "lineNumber": 165, "oldContent": "                                                  topRight: Radius.circular("}, {"type": "DELETE", "lineNumber": 166, "oldContent": "                                                )"}, {"type": "INSERT", "lineNumber": 165, "content": "                                                  : Radius.circular("}, {"type": "MODIFY", "lineNumber": 168, "content": "                                                )", "oldContent": "                                              : BorderRadius.only("}, {"type": "INSERT", "lineNumber": 169, "content": "                                              : BorderRadius.only("}, {"type": "INSERT", "lineNumber": 173, "content": "                                                  bottomLeft: Radius.circular("}, {"type": "INSERT", "lineNumber": 174, "content": "                                                    24.0.r,"}, {"type": "INSERT", "lineNumber": 175, "content": "                                                  ),"}, {"type": "DELETE", "lineNumber": 176, "oldContent": "                                                  bottomLeft: Radius.circular("}, {"type": "DELETE", "lineNumber": 178, "oldContent": "                                                    24.0.r,"}, {"type": "DELETE", "lineNumber": 180, "oldContent": "                                                  ),"}, {"type": "INSERT", "lineNumber": 209, "content": "                                                : Alignment.centerLeft,"}, {"type": "DELETE", "lineNumber": 214, "oldContent": "                                                : Alignment.centerLeft,"}, {"type": "INSERT", "lineNumber": 219, "content": "                                              textDirection: TextDirection.ltr,"}, {"type": "INSERT", "lineNumber": 221, "content": "                                          ),"}, {"type": "DELETE", "lineNumber": 222, "oldContent": "                                              textDirection: TextDirection.ltr,"}, {"type": "DELETE", "lineNumber": 224, "oldContent": "                                          ),"}, {"type": "INSERT", "lineNumber": 227, "content": "                            ),"}, {"type": "INSERT", "lineNumber": 228, "content": "                          ),"}, {"type": "DELETE", "lineNumber": 231, "oldContent": "                            ),"}, {"type": "DELETE", "lineNumber": 233, "oldContent": "                          ),"}]}, {"timestamp": 1755094256569, "changes": [{"type": "MODIFY", "lineNumber": 165, "content": "                                                  t: Radius.circular(", "oldContent": "                                                  : Radius.circular("}, {"type": "INSERT", "lineNumber": 167, "content": "                                                  ),"}, {"type": "DELETE", "lineNumber": 169, "oldContent": "                                                  ),"}, {"type": "DELETE", "lineNumber": 174, "oldContent": "                                                ),"}, {"type": "MODIFY", "lineNumber": 176, "content": "                                                ),", "oldContent": "                                        ),"}, {"type": "INSERT", "lineNumber": 177, "content": "                                        ),"}, {"type": "MODIFY", "lineNumber": 222, "content": "                                        ),", "oldContent": "                                        ),"}, {"type": "MODIFY", "lineNumber": 229, "content": "                        ),", "oldContent": "                        ),"}]}, {"timestamp": 1755094259730, "changes": [{"type": "MODIFY", "lineNumber": 165, "content": "                                                  bottomRight: Radius.circular(", "oldContent": "                                                  t: Radius.circular("}, {"type": "INSERT", "lineNumber": 175, "content": "                                                  ),"}, {"type": "DELETE", "lineNumber": 177, "oldContent": "                                                  ),"}, {"type": "INSERT", "lineNumber": 221, "content": "                                          ),"}, {"type": "DELETE", "lineNumber": 222, "oldContent": "                                        ),"}, {"type": "INSERT", "lineNumber": 228, "content": "                          ),"}, {"type": "DELETE", "lineNumber": 229, "oldContent": "                        ),"}]}, {"timestamp": 1755094265939, "changes": [{"type": "MODIFY", "lineNumber": 165, "content": "                                                  bottomLeft: Radius.circular(", "oldContent": "                                                  bottomRight: Radius.circular("}]}, {"timestamp": 1755094275964, "changes": [{"type": "MODIFY", "lineNumber": 173, "content": "                                                  t: Radius.circular(", "oldContent": "                                                  bottomLeft: Radius.circular("}]}, {"timestamp": 1755094278055, "changes": [{"type": "MODIFY", "lineNumber": 173, "content": "                                                  topRight: Radius.circular(", "oldContent": "                                                  t: Radius.circular("}]}, {"timestamp": 1755094291461, "changes": [{"type": "INSERT", "lineNumber": 176, "content": "                                                  bottomLeft: Radius.circular("}, {"type": "INSERT", "lineNumber": 177, "content": "                                                    24.0.r,"}, {"type": "INSERT", "lineNumber": 178, "content": "                                                  ),"}, {"type": "INSERT", "lineNumber": 179, "content": "                                                  bottomRight: Radius.circular("}, {"type": "INSERT", "lineNumber": 180, "content": "                                                    24.0.r,"}, {"type": "INSERT", "lineNumber": 181, "content": "                                                  ),"}]}, {"timestamp": 1755094295451, "changes": [{"type": "DELETE", "lineNumber": 177, "oldContent": "                                                ),"}, {"type": "MODIFY", "lineNumber": 179, "content": "                                                  t: Radius.circular(", "oldContent": "                                        ),"}, {"type": "INSERT", "lineNumber": 180, "content": "                                                    24.0.r,"}, {"type": "INSERT", "lineNumber": 181, "content": "                                                  ),"}, {"type": "INSERT", "lineNumber": 182, "content": "                                                ),"}, {"type": "INSERT", "lineNumber": 183, "content": "                                        ),"}, {"type": "DELETE", "lineNumber": 182, "oldContent": "                                                  bottomRight: Radius.circular("}, {"type": "DELETE", "lineNumber": 184, "oldContent": "                                                    24.0.r,"}, {"type": "DELETE", "lineNumber": 186, "oldContent": "                                                  ),"}]}, {"timestamp": 1755094297644, "changes": [{"type": "MODIFY", "lineNumber": 178, "content": "                                                  ),", "oldContent": "                                                  t: Radius.circular("}, {"type": "INSERT", "lineNumber": 179, "content": "                                                  topLeft: Radius.circular("}, {"type": "DELETE", "lineNumber": 181, "oldContent": "                                                  ),"}, {"type": "DELETE", "lineNumber": 182, "oldContent": "                                        child: Text("}, {"type": "INSERT", "lineNumber": 184, "content": "                                        child: Text("}]}, {"timestamp": 1755094309729, "changes": [{"type": "DELETE", "lineNumber": 170, "oldContent": "                                                  bottomRight: Radius.circular("}, {"type": "DELETE", "lineNumber": 171, "oldContent": "                                                    24.0.r,"}, {"type": "DELETE", "lineNumber": 172, "oldContent": "                                                  ),"}, {"type": "DELETE", "lineNumber": 173, "oldContent": "                                                  topRight: Radius.circular("}, {"type": "DELETE", "lineNumber": 174, "oldContent": "                                                    24.0.r,"}, {"type": "DELETE", "lineNumber": 175, "oldContent": "                                                  ),"}, {"type": "DELETE", "lineNumber": 176, "oldContent": "                                                  bottomLeft: Radius.circular("}, {"type": "DELETE", "lineNumber": 177, "oldContent": "                                                    24.0.r,"}, {"type": "DELETE", "lineNumber": 178, "oldContent": "                                                  ),"}, {"type": "DELETE", "lineNumber": 179, "oldContent": "                                                  topLeft: Radius.circular("}, {"type": "DELETE", "lineNumber": 180, "oldContent": "                                                    24.0.r,"}, {"type": "DELETE", "lineNumber": 181, "oldContent": "                                                  ),"}, {"type": "INSERT", "lineNumber": 170, "content": "                                                  bottomRight: <PERSON><PERSON>."}, {"type": "DELETE", "lineNumber": 183, "oldContent": "                                        child: Text("}, {"type": "INSERT", "lineNumber": 173, "content": "                                        child: Text("}]}, {"timestamp": 1755094315457, "changes": [{"type": "DELETE", "lineNumber": 170, "oldContent": "                                                  bottomRight: <PERSON><PERSON>."}, {"type": "DELETE", "lineNumber": 171, "oldContent": "                                        child: Text("}, {"type": "INSERT", "lineNumber": 170, "content": "                                                  bottomRight: Radius.circular(24.r)"}, {"type": "INSERT", "lineNumber": 173, "content": "                                        child: Text("}]}, {"timestamp": 1755094320766, "changes": [{"type": "DELETE", "lineNumber": 169, "oldContent": "                                              : BorderRadius.only("}, {"type": "DELETE", "lineNumber": 170, "oldContent": "                                                  bottomRight: Radius.circular(24.r)"}, {"type": "DELETE", "lineNumber": 171, "oldContent": "                                                ),"}, {"type": "DELETE", "lineNumber": 172, "oldContent": "                                        child: Text("}, {"type": "INSERT", "lineNumber": 169, "content": "                                              : BorderRadius.all()"}, {"type": "INSERT", "lineNumber": 171, "content": "                                        child: Text("}]}, {"timestamp": 1755094325188, "changes": [{"type": "DELETE", "lineNumber": 169, "oldContent": "                                              : BorderRadius.all()"}, {"type": "DELETE", "lineNumber": 170, "oldContent": "                                        child: Text("}, {"type": "INSERT", "lineNumber": 169, "content": "                                              :"}, {"type": "INSERT", "lineNumber": 170, "content": "                                              BorderRadius.all()"}, {"type": "INSERT", "lineNumber": 172, "content": "                                        child: Text("}]}, {"timestamp": 1755094332666, "changes": [{"type": "DELETE", "lineNumber": 157, "oldContent": "                                              watch"}, {"type": "DELETE", "lineNumber": 158, "oldContent": "                                                      .watch(_messages)[index]"}, {"type": "DELETE", "lineNumber": 159, "oldContent": "                                                      .senderID =="}, {"type": "DELETE", "lineNumber": 160, "oldContent": "                                                  _myUserID"}, {"type": "DELETE", "lineNumber": 161, "oldContent": "                                              ? BorderRadius.only("}, {"type": "DELETE", "lineNumber": 162, "oldContent": "                                                  topLeft: Radius.circular("}, {"type": "DELETE", "lineNumber": 163, "oldContent": "                                                    24.0.r,"}, {"type": "DELETE", "lineNumber": 164, "oldContent": "                                                  ),"}, {"type": "DELETE", "lineNumber": 165, "oldContent": "                                                  bottomLeft: Radius.circular("}, {"type": "DELETE", "lineNumber": 166, "oldContent": "                                                    24.0.r,"}, {"type": "DELETE", "lineNumber": 167, "oldContent": "                                                  ),"}, {"type": "DELETE", "lineNumber": 168, "oldContent": "                                                )"}, {"type": "DELETE", "lineNumber": 169, "oldContent": "                                              :"}, {"type": "DELETE", "lineNumber": 170, "oldContent": "                                              BorderRadius.all()"}, {"type": "INSERT", "lineNumber": 157, "content": "                                              // watch"}, {"type": "INSERT", "lineNumber": 158, "content": "                                              //         .watch(_messages)[index]"}, {"type": "INSERT", "lineNumber": 159, "content": "                                              //         .senderID =="}, {"type": "INSERT", "lineNumber": 160, "content": "                                              //     _myUserID"}, {"type": "INSERT", "lineNumber": 161, "content": "                                              // ? BorderRadius.only("}, {"type": "INSERT", "lineNumber": 162, "content": "                                              //     topLeft: Radius.circular("}, {"type": "INSERT", "lineNumber": 163, "content": "                                              //       24.0.r,"}, {"type": "INSERT", "lineNumber": 164, "content": "                                              //     ),"}, {"type": "INSERT", "lineNumber": 165, "content": "                                              //     bottomLeft: Radius.circular("}, {"type": "INSERT", "lineNumber": 166, "content": "                                              //       24.0.r,"}, {"type": "INSERT", "lineNumber": 167, "content": "                                              //     ),"}, {"type": "INSERT", "lineNumber": 168, "content": "                                              //   )"}, {"type": "INSERT", "lineNumber": 169, "content": "                                              // :"}, {"type": "INSERT", "lineNumber": 170, "content": "                                              BorderRadius.all("}, {"type": "INSERT", "lineNumber": 171, "content": "                                                Radius.circular(24.0.r),"}, {"type": "INSERT", "lineNumber": 172, "content": "                                              ),"}]}, {"timestamp": 1755094336305, "changes": [{"type": "DELETE", "lineNumber": 171, "oldContent": "                                                Radius.circular(24.0.r),"}, {"type": "DELETE", "lineNumber": 172, "oldContent": "                                        ),"}, {"type": "INSERT", "lineNumber": 171, "content": "                                                Radius.circular(20.0.r),"}, {"type": "INSERT", "lineNumber": 173, "content": "                                        ),"}]}, {"timestamp": 1755094341322, "changes": [{"type": "DELETE", "lineNumber": 171, "oldContent": "                                                Radius.circular(20.0.r),"}, {"type": "DELETE", "lineNumber": 172, "oldContent": "                                        ),"}, {"type": "INSERT", "lineNumber": 171, "content": "                                                Radius.circular(15.0.r),"}, {"type": "INSERT", "lineNumber": 173, "content": "                                        ),"}, {"type": "DELETE", "lineNumber": 191, "oldContent": "                                    ],"}, {"type": "DELETE", "lineNumber": 192, "oldContent": "                                ),"}, {"type": "DELETE", "lineNumber": 193, "oldContent": "                            ),"}, {"type": "DELETE", "lineNumber": 194, "oldContent": "                        ),"}, {"type": "DELETE", "lineNumber": 195, "oldContent": "                    ),"}, {"type": "DELETE", "lineNumber": 196, "oldContent": "                      children: ["}, {"type": "DELETE", "lineNumber": 197, "oldContent": "                          child: <PERSON><PERSON><PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 198, "oldContent": "                            style: TextStyle("}, {"type": "DELETE", "lineNumber": 199, "oldContent": "                              color: AppColors.blackColor,"}, {"type": "DELETE", "lineNumber": 200, "oldContent": "                            ),"}, {"type": "DELETE", "lineNumber": 201, "oldContent": "                              hintText: \"Type Message Here\","}, {"type": "DELETE", "lineNumber": 202, "oldContent": ""}, {"type": "DELETE", "lineNumber": 203, "oldContent": "                                borderSide: const BorderSide("}, {"type": "DELETE", "lineNumber": 204, "oldContent": "                                ),"}, {"type": "INSERT", "lineNumber": 191, "content": "                                      // Add timestamp display"}, {"type": "INSERT", "lineNumber": 192, "content": "                                      if (watch"}, {"type": "INSERT", "lineNumber": 193, "content": "                                          .watch(_messages)[index]"}, {"type": "INSERT", "lineNumber": 194, "content": "                                          .formattedTimestamp"}, {"type": "INSERT", "lineNumber": 195, "content": "                                          .isNotEmpty)"}, {"type": "INSERT", "lineNumber": 196, "content": "                                        Padding("}, {"type": "INSERT", "lineNumber": 197, "content": "                                          padding: EdgeInsets.only(top: 4.0.h),"}, {"type": "INSERT", "lineNumber": 198, "content": "                                          child: <PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 199, "content": "                                            alignment:"}, {"type": "INSERT", "lineNumber": 200, "content": "                                                watch"}, {"type": "INSERT", "lineNumber": 201, "content": "                                                        .watch(_messages)[index]"}, {"type": "INSERT", "lineNumber": 202, "content": "                                                        .senderID =="}, {"type": "INSERT", "lineNumber": 203, "content": "                                                    _myUserID"}, {"type": "INSERT", "lineNumber": 204, "content": "                                                ? Alignment.centerRight"}, {"type": "INSERT", "lineNumber": 246, "content": "                                borderRadius: BorderRadius.all("}, {"type": "INSERT", "lineNumber": 247, "content": "                                  Radius.circular(100.0.r),"}, {"type": "INSERT", "lineNumber": 248, "content": "                                ),"}, {"type": "INSERT", "lineNumber": 250, "content": "                              enabledBorder: OutlineInputBorder("}, {"type": "INSERT", "lineNumber": 251, "content": "                                borderSide: const BorderSide("}, {"type": "INSERT", "lineNumber": 252, "content": "                                  color: AppColors.lightgreyColor,"}, {"type": "INSERT", "lineNumber": 253, "content": "                                ),"}, {"type": "INSERT", "lineNumber": 254, "content": "                                borderRadius: BorderRadius.all("}, {"type": "INSERT", "lineNumber": 255, "content": "                                  Radius.circular(100.0.r),"}, {"type": "INSERT", "lineNumber": 256, "content": "                                ),"}, {"type": "INSERT", "lineNumber": 257, "content": "                              ),"}, {"type": "INSERT", "lineNumber": 258, "content": "                              focusedBorder: OutlineInputBorder("}, {"type": "INSERT", "lineNumber": 259, "content": "                                borderSide: const BorderSide("}, {"type": "INSERT", "lineNumber": 260, "content": "                                  color: AppColors.lightgreyColor,"}, {"type": "INSERT", "lineNumber": 261, "content": "                                ),"}, {"type": "INSERT", "lineNumber": 262, "content": "                                borderRadius: BorderRadius.all("}, {"type": "INSERT", "lineNumber": 263, "content": "                                  Radius.circular(100.0.r),"}, {"type": "INSERT", "lineNumber": 264, "content": "                                ),"}, {"type": "INSERT", "lineNumber": 265, "content": "                              ),"}, {"type": "INSERT", "lineNumber": 266, "content": "                              disabledBorder: OutlineInputBorder("}, {"type": "INSERT", "lineNumber": 267, "content": "                                borderSide: const BorderSide("}, {"type": "INSERT", "lineNumber": 268, "content": "                                  color: AppColors.lightgreyColor,"}, {"type": "INSERT", "lineNumber": 269, "content": "                                ),"}, {"type": "INSERT", "lineNumber": 270, "content": "                                borderRadius: BorderRadius.all("}, {"type": "INSERT", "lineNumber": 271, "content": "                                  Radius.circular(100.0.r),"}, {"type": "INSERT", "lineNumber": 272, "content": "                                ),"}, {"type": "INSERT", "lineNumber": 273, "content": "                              ),"}]}, {"timestamp": 1755100875948, "changes": [{"type": "MODIFY", "lineNumber": 173, "content": "                                        ),", "oldContent": "                                        ),"}, {"type": "INSERT", "lineNumber": 230, "content": "                        Expanded("}, {"type": "INSERT", "lineNumber": 231, "content": "                          child: <PERSON><PERSON><PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 232, "content": "                            controller: _messageC<PERSON><PERSON>er,"}, {"type": "INSERT", "lineNumber": 233, "content": "                            style: TextStyle("}, {"type": "INSERT", "lineNumber": 234, "content": "                              fontSize: 14.0.sp,"}, {"type": "INSERT", "lineNumber": 235, "content": "                              color: AppColors.blackColor,"}, {"type": "INSERT", "lineNumber": 236, "content": "                              fontWeight: FontWeight.bold,"}, {"type": "INSERT", "lineNumber": 237, "content": "                            ),"}, {"type": "INSERT", "lineNumber": 238, "content": "                            decoration: InputDecoration("}, {"type": "INSERT", "lineNumber": 239, "content": "                              hintText: \"Type Message Here\","}, {"type": "INSERT", "lineNumber": 240, "content": "                              hintStyle: TextStyle(fontSize: 12.0.sp),"}, {"type": "INSERT", "lineNumber": 241, "content": ""}, {"type": "INSERT", "lineNumber": 242, "content": "                              border: OutlineInputBorder("}, {"type": "INSERT", "lineNumber": 243, "content": "                                borderSide: const BorderSide("}, {"type": "INSERT", "lineNumber": 244, "content": "                                  color: AppColors.lightgreyColor,"}, {"type": "INSERT", "lineNumber": 245, "content": "                                ),"}, {"type": "INSERT", "lineNumber": 246, "content": "                                borderRadius: BorderRadius.all("}, {"type": "INSERT", "lineNumber": 247, "content": "                                  Radius.circular(100.0.r),"}, {"type": "INSERT", "lineNumber": 248, "content": "                                ),"}, {"type": "INSERT", "lineNumber": 249, "content": "                              ),"}, {"type": "INSERT", "lineNumber": 252, "content": "                                  color: AppColors.lightgreyColor,"}, {"type": "INSERT", "lineNumber": 255, "content": "                                  Radius.circular(100.0.r),"}, {"type": "INSERT", "lineNumber": 258, "content": "                              focusedBorder: OutlineInputBorder("}, {"type": "INSERT", "lineNumber": 261, "content": "                                ),"}, {"type": "INSERT", "lineNumber": 264, "content": "                                ),"}, {"type": "INSERT", "lineNumber": 267, "content": "                                borderSide: const BorderSide("}, {"type": "INSERT", "lineNumber": 270, "content": "                                borderRadius: BorderRadius.all("}, {"type": "DELETE", "lineNumber": 246, "oldContent": "                                borderRadius: BorderRadius.all("}, {"type": "INSERT", "lineNumber": 273, "content": "                              ),"}, {"type": "DELETE", "lineNumber": 248, "oldContent": "                                  Radius.circular(100.0.r),"}, {"type": "DELETE", "lineNumber": 250, "oldContent": "                                ),"}, {"type": "DELETE", "lineNumber": 253, "oldContent": "                              enabledBorder: OutlineInputBorder("}, {"type": "DELETE", "lineNumber": 255, "oldContent": "                                borderSide: const BorderSide("}, {"type": "DELETE", "lineNumber": 257, "oldContent": "                                  color: AppColors.lightgreyColor,"}, {"type": "DELETE", "lineNumber": 259, "oldContent": "                                ),"}, {"type": "DELETE", "lineNumber": 261, "oldContent": "                                borderRadius: BorderRadius.all("}, {"type": "DELETE", "lineNumber": 263, "oldContent": "                                  Radius.circular(100.0.r),"}, {"type": "DELETE", "lineNumber": 265, "oldContent": "                                ),"}, {"type": "DELETE", "lineNumber": 267, "oldContent": "                              ),"}, {"type": "DELETE", "lineNumber": 269, "oldContent": "                              focusedBorder: OutlineInputBorder("}, {"type": "DELETE", "lineNumber": 271, "oldContent": "                                borderSide: const BorderSide("}, {"type": "DELETE", "lineNumber": 273, "oldContent": "                                  color: AppColors.lightgreyColor,"}, {"type": "DELETE", "lineNumber": 275, "oldContent": "                                ),"}, {"type": "DELETE", "lineNumber": 277, "oldContent": "                                borderRadius: BorderRadius.all("}, {"type": "DELETE", "lineNumber": 279, "oldContent": "                                  Radius.circular(100.0.r),"}, {"type": "DELETE", "lineNumber": 281, "oldContent": "                                ),"}, {"type": "INSERT", "lineNumber": 292, "content": "                                        timestamp:"}, {"type": "INSERT", "lineNumber": 293, "content": "                                            DateTime.now(), // Add current timestamp for immediate display"}, {"type": "DELETE", "lineNumber": 283, "oldContent": "                              ),"}, {"type": "DELETE", "lineNumber": 285, "oldContent": "                              disabledBorder: OutlineInputBorder("}, {"type": "DELETE", "lineNumber": 287, "oldContent": "                                borderSide: const BorderSide("}, {"type": "DELETE", "lineNumber": 289, "oldContent": "                                  color: AppColors.lightgreyColor,"}, {"type": "DELETE", "lineNumber": 291, "oldContent": "                                ),"}, {"type": "DELETE", "lineNumber": 293, "oldContent": "                                borderRadius: BorderRadius.all("}, {"type": "DELETE", "lineNumber": 295, "oldContent": "                                  Radius.circular(100.0.r),"}, {"type": "DELETE", "lineNumber": 297, "oldContent": "                                ),"}, {"type": "DELETE", "lineNumber": 299, "oldContent": "                              ),"}]}, {"timestamp": 1755100929710, "changes": [{"type": "MODIFY", "lineNumber": 62, "content": "", "oldContent": "          context.read(_messages.notifier).state = getNewMessage;"}, {"type": "INSERT", "lineNumber": 63, "content": "          // Merge messages intelligently to avoid duplicates and flickering"}, {"type": "INSERT", "lineNumber": 64, "content": "          final currentMessages = context.read(_messages);"}, {"type": "INSERT", "lineNumber": 65, "content": "          final mergedMessages = <ChatScreenModel>[];"}, {"type": "INSERT", "lineNumber": 66, "content": ""}, {"type": "INSERT", "lineNumber": 67, "content": "          // Add all new messages from server"}, {"type": "INSERT", "lineNumber": 68, "content": "          for (final newMsg in getNewMessage) {"}, {"type": "INSERT", "lineNumber": 69, "content": "            mergedMessages.add(newMsg);"}, {"type": "INSERT", "lineNumber": 70, "content": "          }"}, {"type": "INSERT", "lineNumber": 71, "content": ""}, {"type": "INSERT", "lineNumber": 72, "content": "          // Add any local messages that don't exist on server yet (no messageId)"}, {"type": "INSERT", "lineNumber": 73, "content": "          for (final localMsg in currentMessages) {"}, {"type": "INSERT", "lineNumber": 74, "content": "            if (localMsg.messageId == null) {"}, {"type": "INSERT", "lineNumber": 75, "content": "              // Check if this local message might be a duplicate of a server message"}, {"type": "INSERT", "lineNumber": 76, "content": "              final isDuplicate = mergedMessages.any("}, {"type": "INSERT", "lineNumber": 77, "content": "                (serverMsg) =>"}, {"type": "INSERT", "lineNumber": 78, "content": "                    serverMsg.senderID == localMsg.senderID &&"}, {"type": "INSERT", "lineNumber": 79, "content": "                    serverMsg.message == localMsg.message &&"}, {"type": "INSERT", "lineNumber": 80, "content": "                    serverMsg.timestamp != null &&"}, {"type": "INSERT", "lineNumber": 81, "content": "                    localMsg.timestamp != null &&"}, {"type": "INSERT", "lineNumber": 82, "content": "                    (serverMsg.timestamp!"}, {"type": "INSERT", "lineNumber": 83, "content": "                            .difference(localMsg.timestamp!)"}, {"type": "INSERT", "lineNumber": 84, "content": "                            .abs()"}, {"type": "INSERT", "lineNumber": 85, "content": "                            .inMinutes <"}, {"type": "INSERT", "lineNumber": 86, "content": "                        1),"}, {"type": "INSERT", "lineNumber": 87, "content": "              );"}, {"type": "INSERT", "lineNumber": 88, "content": ""}, {"type": "INSERT", "lineNumber": 89, "content": "              if (!isDuplicate) {"}, {"type": "INSERT", "lineNumber": 90, "content": "                mergedMessages.insert(0, localMsg);"}, {"type": "INSERT", "lineNumber": 91, "content": "              }"}, {"type": "INSERT", "lineNumber": 92, "content": "            }"}, {"type": "INSERT", "lineNumber": 93, "content": "          }"}, {"type": "INSERT", "lineNumber": 94, "content": ""}, {"type": "INSERT", "lineNumber": 95, "content": "          context.read(_messages.notifier).state = mergedMessages;"}, {"type": "INSERT", "lineNumber": 205, "content": "                                              ),"}, {"type": "DELETE", "lineNumber": 173, "oldContent": "                                        ),"}, {"type": "DELETE", "lineNumber": 208, "oldContent": "                              color: AppColors.blackColor,"}, {"type": "DELETE", "lineNumber": 209, "oldContent": "                              fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 210, "oldContent": "                            decoration: InputDecoration("}, {"type": "DELETE", "lineNumber": 211, "oldContent": "                              hintStyle: TextStyle(fontSize: 12.0.sp),"}, {"type": "DELETE", "lineNumber": 212, "oldContent": "                              border: OutlineInputBorder("}, {"type": "DELETE", "lineNumber": 213, "oldContent": "                                borderSide: const BorderSide("}, {"type": "DELETE", "lineNumber": 214, "oldContent": "                                ),"}, {"type": "INSERT", "lineNumber": 241, "content": "                                                  .watch(_messages)[index]"}, {"type": "INSERT", "lineNumber": 242, "content": "                                                  .formattedTimestamp,"}, {"type": "INSERT", "lineNumber": 243, "content": "                                              style: TextStyle("}, {"type": "INSERT", "lineNumber": 244, "content": "                                                fontSize: 10.0.sp,"}, {"type": "INSERT", "lineNumber": 245, "content": "                                                color: Colors.grey[600],"}, {"type": "INSERT", "lineNumber": 246, "content": "                                                fontWeight: FontWeight.w400,"}, {"type": "INSERT", "lineNumber": 247, "content": "                                              ),"}, {"type": "DELETE", "lineNumber": 216, "oldContent": "                                ),"}, {"type": "INSERT", "lineNumber": 249, "content": "                                            ),"}, {"type": "DELETE", "lineNumber": 231, "oldContent": "                              focusedBorder: OutlineInputBorder("}, {"type": "DELETE", "lineNumber": 233, "oldContent": "                                borderSide: const BorderSide("}, {"type": "DELETE", "lineNumber": 235, "oldContent": "                                  color: AppColors.lightgreyColor,"}, {"type": "DELETE", "lineNumber": 237, "oldContent": "                                ),"}, {"type": "DELETE", "lineNumber": 239, "oldContent": "                                borderRadius: BorderRadius.all("}, {"type": "DELETE", "lineNumber": 241, "oldContent": "                                  Radius.circular(100.0.r),"}, {"type": "DELETE", "lineNumber": 243, "oldContent": "                                ),"}, {"type": "DELETE", "lineNumber": 245, "oldContent": "                              ),"}, {"type": "DELETE", "lineNumber": 247, "oldContent": "                              disabledBorder: OutlineInputBorder("}, {"type": "DELETE", "lineNumber": 249, "oldContent": "                                borderSide: const BorderSide("}, {"type": "DELETE", "lineNumber": 251, "oldContent": "                                  color: AppColors.lightgreyColor,"}, {"type": "DELETE", "lineNumber": 253, "oldContent": "                                ),"}, {"type": "INSERT", "lineNumber": 276, "content": "                                borderSide: const BorderSide("}, {"type": "INSERT", "lineNumber": 277, "content": "                                  color: AppColors.lightgreyColor,"}, {"type": "INSERT", "lineNumber": 278, "content": "                                ),"}, {"type": "INSERT", "lineNumber": 280, "content": "                                  Radius.circular(100.0.r),"}, {"type": "INSERT", "lineNumber": 281, "content": "                                ),"}, {"type": "INSERT", "lineNumber": 282, "content": "                              ),"}, {"type": "INSERT", "lineNumber": 283, "content": "                              enabledBorder: OutlineInputBorder("}, {"type": "INSERT", "lineNumber": 285, "content": "                                  color: AppColors.lightgreyColor,"}, {"type": "INSERT", "lineNumber": 286, "content": "                                ),"}, {"type": "INSERT", "lineNumber": 287, "content": "                                borderRadius: BorderRadius.all("}, {"type": "INSERT", "lineNumber": 289, "content": "                                ),"}, {"type": "INSERT", "lineNumber": 290, "content": "                              ),"}, {"type": "INSERT", "lineNumber": 291, "content": "                              focusedBorder: OutlineInputBorder("}, {"type": "INSERT", "lineNumber": 292, "content": "                                borderSide: const BorderSide("}, {"type": "INSERT", "lineNumber": 295, "content": "                                borderRadius: BorderRadius.all("}, {"type": "INSERT", "lineNumber": 296, "content": "                                  Radius.circular(100.0.r),"}, {"type": "INSERT", "lineNumber": 299, "content": "                              disabledBorder: OutlineInputBorder("}, {"type": "INSERT", "lineNumber": 300, "content": "                                borderSide: const BorderSide("}, {"type": "INSERT", "lineNumber": 301, "content": "                                  color: AppColors.lightgreyColor,"}, {"type": "INSERT", "lineNumber": 302, "content": "                                ),"}, {"type": "DELETE", "lineNumber": 264, "oldContent": "                            ),"}, {"type": "INSERT", "lineNumber": 307, "content": "                            ),"}, {"type": "DELETE", "lineNumber": 269, "oldContent": "                                  color: AppColors.lightgreyColor,"}, {"type": "DELETE", "lineNumber": 272, "oldContent": "                                  Radius.circular(100.0.r),"}, {"type": "DELETE", "lineNumber": 274, "oldContent": "                              focusedBorder: OutlineInputBorder("}, {"type": "DELETE", "lineNumber": 277, "oldContent": "                                ),"}, {"type": "DELETE", "lineNumber": 279, "oldContent": "                                ),"}, {"type": "DELETE", "lineNumber": 282, "oldContent": "                                borderSide: const BorderSide("}, {"type": "DELETE", "lineNumber": 284, "oldContent": "                                borderRadius: BorderRadius.all("}, {"type": "DELETE", "lineNumber": 287, "oldContent": "                              ),"}, {"type": "INSERT", "lineNumber": 325, "content": "                                        timestamp:"}, {"type": "INSERT", "lineNumber": 326, "content": "                                            DateTime.now(), // Add current timestamp for immediate display"}, {"type": "DELETE", "lineNumber": 297, "oldContent": "                                        timestamp:"}, {"type": "DELETE", "lineNumber": 299, "oldContent": "                                            DateTime.now(), // Add current timestamp for immediate display"}]}, {"timestamp": 1755101533038, "changes": [{"type": "DELETE", "lineNumber": 64, "oldContent": "        }"}, {"type": "DELETE", "lineNumber": 66, "oldContent": "      });"}, {"type": "DELETE", "lineNumber": 68, "oldContent": "    });"}, {"type": "DELETE", "lineNumber": 70, "oldContent": "    super.initState();"}, {"type": "DELETE", "lineNumber": 72, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 74, "oldContent": ""}, {"type": "DELETE", "lineNumber": 76, "oldContent": "  @override"}, {"type": "DELETE", "lineNumber": 78, "oldContent": "  void dispose() {"}, {"type": "DELETE", "lineNumber": 80, "oldContent": "    _messageController.dispose();"}, {"type": "DELETE", "lineNumber": 82, "oldContent": "    _timer?.cancel();"}, {"type": "DELETE", "lineNumber": 84, "oldContent": "    _isDisposed = true;"}, {"type": "INSERT", "lineNumber": 75, "content": "              // Check if this local message is a duplicate of any server message"}, {"type": "INSERT", "lineNumber": 76, "content": "              final isDuplicate = mergedMessages.any("}, {"type": "INSERT", "lineNumber": 77, "content": "                (serverMsg) =>"}, {"type": "INSERT", "lineNumber": 78, "content": "                    serverMsg.senderID == localMsg.senderID &&"}, {"type": "INSERT", "lineNumber": 79, "content": "                    serverMsg.message?.trim() == localMsg.message?.trim(),"}, {"type": "INSERT", "lineNumber": 80, "content": "              );"}, {"type": "DELETE", "lineNumber": 87, "oldContent": "              // Check if this local message might be a duplicate of a server message"}, {"type": "INSERT", "lineNumber": 82, "content": "              // Only add local message if it's not a duplicate"}, {"type": "INSERT", "lineNumber": 83, "content": "              if (!isDuplicate) {"}, {"type": "INSERT", "lineNumber": 84, "content": "                mergedMessages.insert(0, localMsg);"}, {"type": "INSERT", "lineNumber": 85, "content": "              }"}, {"type": "INSERT", "lineNumber": 86, "content": "            }"}, {"type": "INSERT", "lineNumber": 87, "content": "          }"}, {"type": "INSERT", "lineNumber": 88, "content": ""}, {"type": "INSERT", "lineNumber": 89, "content": "          context.read(_messages.notifier).state = mergedMessages;"}, {"type": "INSERT", "lineNumber": 90, "content": "        }"}, {"type": "INSERT", "lineNumber": 91, "content": "      });"}, {"type": "INSERT", "lineNumber": 92, "content": "    });"}, {"type": "INSERT", "lineNumber": 93, "content": "    super.initState();"}, {"type": "INSERT", "lineNumber": 94, "content": "  }"}, {"type": "INSERT", "lineNumber": 95, "content": ""}, {"type": "INSERT", "lineNumber": 96, "content": "  @override"}, {"type": "INSERT", "lineNumber": 97, "content": "  void dispose() {"}, {"type": "INSERT", "lineNumber": 98, "content": "    _messageController.dispose();"}, {"type": "INSERT", "lineNumber": 99, "content": "    _timer?.cancel();"}, {"type": "INSERT", "lineNumber": 100, "content": "    _isDisposed = true;"}, {"type": "INSERT", "lineNumber": 101, "content": ""}, {"type": "DELETE", "lineNumber": 89, "oldContent": "              final isDuplicate = mergedMessages.any("}, {"type": "DELETE", "lineNumber": 91, "oldContent": "                (serverMsg) =>"}, {"type": "DELETE", "lineNumber": 93, "oldContent": "                    serverMsg.senderID == localMsg.senderID &&"}, {"type": "DELETE", "lineNumber": 95, "oldContent": "                    serverMsg.message == localMsg.message &&"}, {"type": "DELETE", "lineNumber": 97, "oldContent": "                    serverMsg.timestamp != null &&"}, {"type": "DELETE", "lineNumber": 99, "oldContent": "                    localMsg.timestamp != null &&"}, {"type": "DELETE", "lineNumber": 101, "oldContent": "                    (serverMsg.timestamp!"}, {"type": "DELETE", "lineNumber": 103, "oldContent": "                            .difference(localMsg.timestamp!)"}, {"type": "DELETE", "lineNumber": 105, "oldContent": "                            .abs()"}, {"type": "DELETE", "lineNumber": 107, "oldContent": "                            .inMinutes <"}, {"type": "DELETE", "lineNumber": 109, "oldContent": "                        1),"}, {"type": "DELETE", "lineNumber": 111, "oldContent": "              );"}, {"type": "DELETE", "lineNumber": 113, "oldContent": ""}, {"type": "DELETE", "lineNumber": 115, "oldContent": "              if (!isDuplicate) {"}, {"type": "DELETE", "lineNumber": 117, "oldContent": "                mergedMessages.insert(0, localMsg);"}, {"type": "DELETE", "lineNumber": 119, "oldContent": "              }"}, {"type": "DELETE", "lineNumber": 121, "oldContent": "            }"}, {"type": "DELETE", "lineNumber": 123, "oldContent": "          }"}, {"type": "DELETE", "lineNumber": 125, "oldContent": ""}, {"type": "DELETE", "lineNumber": 127, "oldContent": "          context.read(_messages.notifier).state = mergedMessages;"}, {"type": "INSERT", "lineNumber": 199, "content": "                                              ),"}, {"type": "DELETE", "lineNumber": 237, "oldContent": "                                              ),"}, {"type": "DELETE", "lineNumber": 239, "oldContent": "                            style: TextStyle("}, {"type": "DELETE", "lineNumber": 240, "oldContent": "                              color: AppColors.blackColor,"}, {"type": "INSERT", "lineNumber": 233, "content": "                                            child: Text("}, {"type": "INSERT", "lineNumber": 234, "content": "                                              watch"}, {"type": "INSERT", "lineNumber": 235, "content": "                                                  .watch(_messages)[index]"}, {"type": "INSERT", "lineNumber": 236, "content": "                                                  .formattedTimestamp,"}, {"type": "INSERT", "lineNumber": 237, "content": "                                              style: TextStyle("}, {"type": "INSERT", "lineNumber": 238, "content": "                                                fontSize: 10.0.sp,"}, {"type": "INSERT", "lineNumber": 239, "content": "                                                color: Colors.grey[600],"}, {"type": "INSERT", "lineNumber": 240, "content": "                                                fontWeight: FontWeight.w400,"}, {"type": "INSERT", "lineNumber": 241, "content": "                                              ),"}, {"type": "INSERT", "lineNumber": 243, "content": "                                            ),"}, {"type": "DELETE", "lineNumber": 250, "oldContent": "                              enabledBorder: OutlineInputBorder("}, {"type": "DELETE", "lineNumber": 251, "oldContent": "                                  color: AppColors.lightgreyColor,"}, {"type": "DELETE", "lineNumber": 252, "oldContent": "                                ),"}, {"type": "DELETE", "lineNumber": 253, "oldContent": "                                  Radius.circular(100.0.r),"}, {"type": "DELETE", "lineNumber": 254, "oldContent": "                                ),"}, {"type": "INSERT", "lineNumber": 252, "content": "                        ),"}, {"type": "INSERT", "lineNumber": 253, "content": "                      ),"}, {"type": "INSERT", "lineNumber": 254, "content": "                    ),"}, {"type": "INSERT", "lineNumber": 255, "content": "                    Row("}, {"type": "INSERT", "lineNumber": 256, "content": "                      children: ["}, {"type": "DELETE", "lineNumber": 261, "oldContent": "                                                  .watch(_messages)[index]"}, {"type": "DELETE", "lineNumber": 262, "oldContent": "                                                  .formattedTimestamp,"}, {"type": "DELETE", "lineNumber": 264, "oldContent": "                                              style: TextStyle("}, {"type": "DELETE", "lineNumber": 265, "oldContent": "                                                fontSize: 10.0.sp,"}, {"type": "DELETE", "lineNumber": 267, "oldContent": "                                                color: Colors.grey[600],"}, {"type": "DELETE", "lineNumber": 268, "oldContent": "                                                fontWeight: FontWeight.w400,"}, {"type": "DELETE", "lineNumber": 270, "oldContent": "                                              ),"}, {"type": "DELETE", "lineNumber": 272, "oldContent": "                                            ),"}, {"type": "INSERT", "lineNumber": 270, "content": "                                borderSide: const BorderSide("}, {"type": "INSERT", "lineNumber": 271, "content": "                                  color: AppColors.lightgreyColor,"}, {"type": "INSERT", "lineNumber": 272, "content": "                                ),"}, {"type": "INSERT", "lineNumber": 274, "content": "                                  Radius.circular(100.0.r),"}, {"type": "INSERT", "lineNumber": 275, "content": "                                ),"}, {"type": "INSERT", "lineNumber": 276, "content": "                              ),"}, {"type": "INSERT", "lineNumber": 277, "content": "                              enabledBorder: OutlineInputBorder("}, {"type": "INSERT", "lineNumber": 279, "content": "                                  color: AppColors.lightgreyColor,"}, {"type": "INSERT", "lineNumber": 280, "content": "                                ),"}, {"type": "INSERT", "lineNumber": 281, "content": "                                borderRadius: BorderRadius.all("}, {"type": "INSERT", "lineNumber": 283, "content": "                                ),"}, {"type": "INSERT", "lineNumber": 284, "content": "                              ),"}, {"type": "INSERT", "lineNumber": 285, "content": "                              focusedBorder: OutlineInputBorder("}, {"type": "INSERT", "lineNumber": 286, "content": "                                borderSide: const BorderSide("}, {"type": "INSERT", "lineNumber": 289, "content": "                                borderRadius: BorderRadius.all("}, {"type": "INSERT", "lineNumber": 290, "content": "                                  Radius.circular(100.0.r),"}, {"type": "INSERT", "lineNumber": 293, "content": "                              disabledBorder: OutlineInputBorder("}, {"type": "INSERT", "lineNumber": 294, "content": "                                borderSide: const BorderSide("}, {"type": "INSERT", "lineNumber": 295, "content": "                                  color: AppColors.lightgreyColor,"}, {"type": "INSERT", "lineNumber": 296, "content": "                                ),"}, {"type": "INSERT", "lineNumber": 301, "content": "                            ),"}, {"type": "DELETE", "lineNumber": 293, "oldContent": "                                borderSide: const BorderSide("}, {"type": "DELETE", "lineNumber": 295, "oldContent": "                                  color: AppColors.lightgreyColor,"}, {"type": "DELETE", "lineNumber": 296, "oldContent": "                                ),"}, {"type": "DELETE", "lineNumber": 298, "oldContent": "                                  Radius.circular(100.0.r),"}, {"type": "DELETE", "lineNumber": 300, "oldContent": "                                ),"}, {"type": "DELETE", "lineNumber": 302, "oldContent": "                              ),"}, {"type": "DELETE", "lineNumber": 303, "oldContent": "                              enabledBorder: OutlineInputBorder("}, {"type": "DELETE", "lineNumber": 305, "oldContent": "                                  color: AppColors.lightgreyColor,"}, {"type": "DELETE", "lineNumber": 307, "oldContent": "                                ),"}, {"type": "DELETE", "lineNumber": 309, "oldContent": "                                borderRadius: BorderRadius.all("}, {"type": "DELETE", "lineNumber": 311, "oldContent": "                                ),"}, {"type": "DELETE", "lineNumber": 313, "oldContent": "                              ),"}, {"type": "DELETE", "lineNumber": 315, "oldContent": "                              focusedBorder: OutlineInputBorder("}, {"type": "DELETE", "lineNumber": 317, "oldContent": "                                borderSide: const BorderSide("}, {"type": "INSERT", "lineNumber": 319, "content": "                                        timestamp:"}, {"type": "INSERT", "lineNumber": 320, "content": "                                            DateTime.now(), // Add current timestamp for immediate display"}, {"type": "DELETE", "lineNumber": 321, "oldContent": "                                borderRadius: BorderRadius.all("}, {"type": "DELETE", "lineNumber": 323, "oldContent": "                                  Radius.circular(100.0.r),"}, {"type": "DELETE", "lineNumber": 326, "oldContent": "                              disabledBorder: OutlineInputBorder("}, {"type": "DELETE", "lineNumber": 327, "oldContent": "                                borderSide: const BorderSide("}, {"type": "DELETE", "lineNumber": 329, "oldContent": "                                  color: AppColors.lightgreyColor,"}, {"type": "DELETE", "lineNumber": 331, "oldContent": "                                ),"}, {"type": "DELETE", "lineNumber": 337, "oldContent": "                            ),"}, {"type": "DELETE", "lineNumber": 356, "oldContent": "                                        timestamp:"}, {"type": "DELETE", "lineNumber": 358, "oldContent": "                                            DateTime.now(), // Add current timestamp for immediate display"}]}]}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/providers/chat_providers_apis/chat_providers_apis.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/providers/chat_providers_apis/chat_providers_apis.dart", "baseContent": "import 'dart:convert';\nimport 'dart:developer';\n\nimport 'package:afa_app/app_config/api_keys.dart';\nimport 'package:afa_app/app_config/api_providers/chat_providers.dart';\nimport 'package:afa_app/app_config/api_providers/profile_providers.dart';\nimport 'package:afa_app/app_config/api_requests.dart';\nimport 'package:afa_app/app_config/common_components.dart';\nimport 'package:afa_app/models/chat_models/chat_screen_model.dart';\nimport 'package:flutter/material.dart';\nimport 'package:riverpod_context/riverpod_context.dart';\n\nclass ChatProvidersApis extends ChangeNotifier {\n  Future<List<ChatScreenModel>> getAllChat({\n    required BuildContext context,\n  }) async {\n    final List<ChatScreenModel> chatList = [];\n\n    final String myToken = await CommonComponents.getSavedData(\n      ApiKeys.userToken,\n    );\n\n    \n    log('TOKEN')\n    if (!context.mounted) return [];\n\n    final int threadID = context\n        .read(ChatProviders.inboxProvidersApis)\n        .threadID!;\n    // print(threadID);\n    if (!context.mounted) return [];\n    final Map<String, dynamic>? dataList = await ApiRequests.postApiRequest(\n      context: context,\n      baseUrl: ApiKeys.baseUrl,\n      apiUrl: \"wp-json/better-messages/v1/thread/$threadID\",\n      headers: {\"Authorization\": \"Bearer $myToken\"},\n      body: {},\n      showLoadingWidget: false,\n    );\n\n    if (dataList != null) {\n      for (final data in dataList['messages']) {\n        if (!context.mounted) return [];\n        await context\n            .read(ProfileProviders.profileSettingsProvidersApis)\n            .getUserData(\n              context: context,\n              getMyProfileData: false,\n              userID: data['sender_id'],\n            )\n            .then((value) {\n              chatList.add(\n                ChatScreenModel.fromJson(\n                  data,\n                  name: value.userName,\n                  image: value.userImage,\n                ),\n              );\n            });\n      }\n    } else {\n      debugPrint(\"ERROR WITH getAllChat FUNCTION\");\n    }\n\n    return chatList;\n  }\n\n  Future<void> sendMessage({\n    required BuildContext context,\n    required int threadId,\n    required String message,\n  }) async {\n    final String myToken = await CommonComponents.getSavedData(\n      ApiKeys.userToken,\n    );\n    if (!context.mounted) return;\n\n    final Map<String, dynamic>? data = await ApiRequests.postApiRequest(\n      context: context,\n      baseUrl: ApiKeys.baseUrl,\n      apiUrl: \"wp-json/better-messages/v1/thread/$threadId/send\",\n      headers: {\n        \"Authorization\": \"Bearer $myToken\",\n        \"Content-Type\": \"application/json\",\n      },\n      body: json.encode({\"message\": message}),\n      showLoadingWidget: false,\n    );\n\n    if (data != null) {\n      debugPrint(\"Sent Message Successfully\");\n    } else {\n      debugPrint(\"ERROR WITH sendMessage FUNCTION\");\n    }\n  }\n}\n", "baseTimestamp": 1755085661701, "deltas": [{"timestamp": 1755089216986, "changes": [{"type": "MODIFY", "lineNumber": 46, "content": "      final users = dataList['users'] as List?;", "oldContent": "      final users = dataList['users'];"}]}, {"timestamp": 1755089221938, "changes": [{"type": "MODIFY", "lineNumber": 46, "content": "      final users =( dataList['users'] as List?) ?? [];", "oldContent": "      final users = dataList['users'] as List?;"}]}, {"timestamp": 1755089232637, "changes": [{"type": "MODIFY", "lineNumber": 50, "content": "        final user = users.firstWhereOrNull()", "oldContent": "        final user = "}]}, {"timestamp": 1755089241435, "changes": [{"type": "MODIFY", "lineNumber": 47, "content": "", "oldContent": "      "}]}, {"timestamp": 1755089249337, "changes": [{"type": "MODIFY", "lineNumber": 50, "content": "        final user = users.firstWhereOrNull", "oldContent": "        final user = users.firstWhereOrNull()"}]}, {"timestamp": 1755089253924, "changes": [{"type": "INSERT", "lineNumber": 9, "content": "import 'package:collection/collection.dart';"}, {"type": "MODIFY", "lineNumber": 25, "content": "    log('TOKEN: $myToken');", "oldContent": "    log('TOKEN')"}, {"type": "MODIFY", "lineNumber": 51, "content": "        final user = users.firstWhereOrNull()", "oldContent": "        final user = users.firstWhereOrNull"}]}, {"timestamp": 1755089258643, "changes": [{"type": "DELETE", "lineNumber": 25, "oldContent": "    log('TOKEN')"}, {"type": "INSERT", "lineNumber": 26, "content": "    if (!context.mounted) return [];"}, {"type": "MODIFY", "lineNumber": 46, "content": "    if (dataList != null) {", "oldContent": "      for (final data in dataList['messages']) {"}, {"type": "DELETE", "lineNumber": 49, "oldContent": "        await context"}, {"type": "DELETE", "lineNumber": 50, "oldContent": "            .read(ProfileProviders.profileSettingsProvidersApis)"}, {"type": "DELETE", "lineNumber": 51, "oldContent": "        final user = users.firstWhereOrNull"}, {"type": "INSERT", "lineNumber": 49, "content": "      for (final data in dataList['messages']) {"}, {"type": "INSERT", "lineNumber": 50, "content": "        if (!context.mounted) return [];"}, {"type": "INSERT", "lineNumber": 52, "content": "        chatList.add("}, {"type": "INSERT", "lineNumber": 74, "content": "    return chatList;"}, {"type": "INSERT", "lineNumber": 75, "content": "  }"}]}, {"timestamp": 1755089260747, "changes": [{"type": "MODIFY", "lineNumber": 26, "content": "    if (!context.mounted) return [];", "oldContent": "    if (!context.mounted) return [];"}, {"type": "INSERT", "lineNumber": 51, "content": "        final user = users.firstWhereOrNull((user) => user['id'] == data['sender_id']);"}, {"type": "DELETE", "lineNumber": 52, "oldContent": "        final user = users.firstWhereOrNull()"}, {"type": "MODIFY", "lineNumber": 76, "content": "", "oldContent": ""}]}, {"timestamp": 1755089263426, "changes": [{"type": "INSERT", "lineNumber": 25, "content": "    log('TOKEN: $myToken');"}, {"type": "DELETE", "lineNumber": 26, "oldContent": "    if (!context.mounted) return [];"}, {"type": "MODIFY", "lineNumber": 47, "content": "      final users = (dataList['users'] as List?) ?? [];", "oldContent": "      final users =( dataList['users'] as List?) ?? [];"}, {"type": "MODIFY", "lineNumber": 51, "content": "        final user = users.firstWhereOrNull(", "oldContent": "        final user = users.firstWhereOrNull((user) => user['id'] == data['sender_id']);"}, {"type": "INSERT", "lineNumber": 52, "content": "          (user) => user['id'] == data['sender_id'],"}, {"type": "INSERT", "lineNumber": 53, "content": "        );"}, {"type": "INSERT", "lineNumber": 77, "content": "  }"}, {"type": "DELETE", "lineNumber": 76, "oldContent": ""}]}, {"timestamp": 1755089269445, "changes": [{"type": "DELETE", "lineNumber": 27, "oldContent": "    if (!context.mounted) return [];"}, {"type": "MODIFY", "lineNumber": 33, "content": "    log('BeforeGettting_Data');", "oldContent": "    log('BeforeGettting_Data');"}, {"type": "INSERT", "lineNumber": 41, "content": "    );"}, {"type": "INSERT", "lineNumber": 44, "content": "      'FULL_URL : ${ApiKeys.baseUrl}wp-json/better-messages/v1/thread/$threadID',"}, {"type": "INSERT", "lineNumber": 45, "content": "    );"}, {"type": "DELETE", "lineNumber": 45, "oldContent": "      for (final data in dataList['messages']) {"}, {"type": "DELETE", "lineNumber": 46, "oldContent": "    if (dataList != null) {"}, {"type": "DELETE", "lineNumber": 52, "oldContent": "          (user) => user['id'] == data['sender_id'],"}, {"type": "DELETE", "lineNumber": 53, "oldContent": "        chatList.add("}, {"type": "INSERT", "lineNumber": 52, "content": "          (user) => user['id'].toString() == data['sender_id'],"}, {"type": "INSERT", "lineNumber": 54, "content": "        chatList.add("}, {"type": "MODIFY", "lineNumber": 78, "content": "", "oldContent": ""}]}, {"timestamp": 1755089276183, "changes": [{"type": "INSERT", "lineNumber": 20, "content": ""}, {"type": "INSERT", "lineNumber": 21, "content": "    final String myToken = await CommonComponents.getSavedData("}, {"type": "DELETE", "lineNumber": 23, "oldContent": "    "}, {"type": "DELETE", "lineNumber": 24, "oldContent": "    log('TOKEN')"}, {"type": "INSERT", "lineNumber": 27, "content": ""}, {"type": "INSERT", "lineNumber": 28, "content": "    final int threadID = context"}, {"type": "DELETE", "lineNumber": 31, "oldContent": "    final Map<String, dynamic>? dataList = await ApiRequests.postApiRequest("}, {"type": "INSERT", "lineNumber": 34, "content": "    final Map<String, dynamic>? dataList = await ApiRequests.postApiRequest("}, {"type": "DELETE", "lineNumber": 40, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 42, "oldContent": "    if (dataList != null) {"}, {"type": "MODIFY", "lineNumber": 46, "content": "    if (dataList != null) {", "oldContent": "      for (final data in dataList['messages']) {"}, {"type": "DELETE", "lineNumber": 52, "oldContent": "          (user) => user['id'].toString() == data['sender_id'],"}, {"type": "DELETE", "lineNumber": 53, "oldContent": "        chatList.add("}, {"type": "INSERT", "lineNumber": 52, "content": "          (user) => user['id'].toString() == data['sender_id'].toString(),"}, {"type": "INSERT", "lineNumber": 54, "content": "        chatList.add("}, {"type": "MODIFY", "lineNumber": 77, "content": "  }", "oldContent": "    required BuildContext context,"}, {"type": "INSERT", "lineNumber": 79, "content": "  Future<void> sendMessage({"}, {"type": "INSERT", "lineNumber": 80, "content": "    required BuildContext context,"}]}, {"timestamp": 1755089286274, "changes": [{"type": "INSERT", "lineNumber": 17, "content": "    log('FIRST_OF_Function');"}, {"type": "INSERT", "lineNumber": 18, "content": ""}, {"type": "DELETE", "lineNumber": 19, "oldContent": ""}, {"type": "DELETE", "lineNumber": 20, "oldContent": ""}, {"type": "MODIFY", "lineNumber": 22, "content": "      ApiKeys.userToken,", "oldContent": "      ApiKeys.userToken,"}, {"type": "MODIFY", "lineNumber": 29, "content": "        .read(ChatProviders.inboxProvidersApis)", "oldContent": "        .read(ChatProviders.inboxProvidersApis)"}, {"type": "MODIFY", "lineNumber": 35, "content": "      context: context,", "oldContent": "      context: context,"}, {"type": "MODIFY", "lineNumber": 45, "content": "    );", "oldContent": "      for (final data in dataList['messages']) {"}, {"type": "MODIFY", "lineNumber": 54, "content": "        chatList.add(", "oldContent": "        chatList.add("}, {"type": "MODIFY", "lineNumber": 57, "content": "            name: user[''],", "oldContent": "            name: value.userName,"}, {"type": "MODIFY", "lineNumber": 81, "content": "    required int threadId,", "oldContent": "    required int threadId,"}]}, {"timestamp": 1755089292651, "changes": [{"type": "INSERT", "lineNumber": 18, "content": ""}, {"type": "DELETE", "lineNumber": 20, "oldContent": ""}, {"type": "INSERT", "lineNumber": 21, "content": "    final String myToken = await CommonComponents.getSavedData("}, {"type": "DELETE", "lineNumber": 22, "oldContent": "      ApiKeys.userToken,"}, {"type": "INSERT", "lineNumber": 28, "content": "    final int threadID = context"}, {"type": "DELETE", "lineNumber": 29, "oldContent": "        .read(ChatProviders.inboxProvidersApis)"}, {"type": "INSERT", "lineNumber": 34, "content": "    final Map<String, dynamic>? dataList = await ApiRequests.postApiRequest("}, {"type": "DELETE", "lineNumber": 35, "oldContent": "      context: context,"}, {"type": "MODIFY", "lineNumber": 43, "content": "    log(", "oldContent": "      for (final data in dataList['messages']) {"}, {"type": "INSERT", "lineNumber": 53, "content": "        );"}, {"type": "DELETE", "lineNumber": 54, "oldContent": "        chatList.add("}, {"type": "DELETE", "lineNumber": 57, "oldContent": "            name: user[''],"}, {"type": "MODIFY", "lineNumber": 57, "content": "            name: user['name'],", "oldContent": "            image: value.userImage,"}, {"type": "INSERT", "lineNumber": 58, "content": "            image: "}, {"type": "INSERT", "lineNumber": 80, "content": "    required BuildContext context,"}, {"type": "DELETE", "lineNumber": 81, "oldContent": "    required int threadId,"}]}, {"timestamp": 1755089294890, "changes": [{"type": "INSERT", "lineNumber": 35, "content": "      context: context,"}, {"type": "DELETE", "lineNumber": 42, "oldContent": "    if (dataList != null) {"}, {"type": "MODIFY", "lineNumber": 58, "content": "            image: user['avatar'],", "oldContent": "            image: value.userImage,"}, {"type": "INSERT", "lineNumber": 81, "content": "    required int threadId,"}]}, {"timestamp": 1755089418307, "changes": [{"type": "DELETE", "lineNumber": 54, "oldContent": "        chatList.add("}, {"type": "DELETE", "lineNumber": 55, "oldContent": "          ChatScreenModel.fromJson("}, {"type": "DELETE", "lineNumber": 56, "oldContent": "            data,"}, {"type": "DELETE", "lineNumber": 57, "oldContent": "            name: user['name'],"}, {"type": "DELETE", "lineNumber": 58, "oldContent": "            image: user['avatar'],"}, {"type": "DELETE", "lineNumber": 59, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 60, "oldContent": "        );"}, {"type": "DELETE", "lineNumber": 61, "oldContent": "        // await context"}, {"type": "DELETE", "lineNumber": 62, "oldContent": "        //     .read(ProfileProviders.profileSettingsProvidersApis)"}, {"type": "DELETE", "lineNumber": 63, "oldContent": "        //     .getUserData("}, {"type": "DELETE", "lineNumber": 64, "oldContent": "        //       context: context,"}, {"type": "DELETE", "lineNumber": 65, "oldContent": "        //       getMyProfileData: false,"}, {"type": "DELETE", "lineNumber": 66, "oldContent": "        //       userID: data['sender_id'],"}, {"type": "DELETE", "lineNumber": 67, "oldContent": "        //     )"}, {"type": "DELETE", "lineNumber": 68, "oldContent": "        //     .then((value) {"}, {"type": "DELETE", "lineNumber": 69, "oldContent": "        //"}, {"type": "DELETE", "lineNumber": 70, "oldContent": "        //     });"}, {"type": "INSERT", "lineNumber": 54, "content": "        // chatList.add("}, {"type": "INSERT", "lineNumber": 55, "content": "        //   ChatScreenModel.fromJson("}, {"type": "INSERT", "lineNumber": 56, "content": "        //     data,"}, {"type": "INSERT", "lineNumber": 57, "content": "        //     name: user['name'],"}, {"type": "INSERT", "lineNumber": 58, "content": "        //     image: user['avatar'],"}, {"type": "INSERT", "lineNumber": 59, "content": "        //   ),"}, {"type": "INSERT", "lineNumber": 60, "content": "        // );"}, {"type": "INSERT", "lineNumber": 61, "content": "        await context"}, {"type": "INSERT", "lineNumber": 62, "content": "            .read(ProfileProviders.profileSettingsProvidersApis)"}, {"type": "INSERT", "lineNumber": 63, "content": "            .getUserData("}, {"type": "INSERT", "lineNumber": 64, "content": "              context: context,"}, {"type": "INSERT", "lineNumber": 65, "content": "              getMyProfileData: false,"}, {"type": "INSERT", "lineNumber": 66, "content": "              userID: data['sender_id'],"}, {"type": "INSERT", "lineNumber": 67, "content": "            )"}, {"type": "INSERT", "lineNumber": 68, "content": "            .then((value) {"}, {"type": "INSERT", "lineNumber": 69, "content": "              "}, {"type": "INSERT", "lineNumber": 70, "content": "            });"}, {"type": "DELETE", "lineNumber": 82, "oldContent": "    return chatList;"}, {"type": "DELETE", "lineNumber": 83, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 84, "oldContent": ""}, {"type": "DELETE", "lineNumber": 85, "oldContent": "  Future<void> sendMessage({"}, {"type": "DELETE", "lineNumber": 86, "oldContent": "    required BuildContext context,"}, {"type": "DELETE", "lineNumber": 87, "oldContent": "    required int threadId,"}]}, {"timestamp": 1755089422702, "changes": [{"type": "INSERT", "lineNumber": 82, "content": "    required String message,"}, {"type": "INSERT", "lineNumber": 83, "content": "  }) async {"}, {"type": "INSERT", "lineNumber": 84, "content": "    final String myToken = await CommonComponents.getSavedData("}, {"type": "INSERT", "lineNumber": 85, "content": "      ApiKeys.userToken,"}, {"type": "INSERT", "lineNumber": 86, "content": "    );"}, {"type": "INSERT", "lineNumber": 87, "content": "    if (!context.mounted) return;"}, {"type": "INSERT", "lineNumber": 88, "content": ""}]}, {"timestamp": 1755089433418, "changes": [{"type": "DELETE", "lineNumber": 68, "oldContent": "            .then((value) {"}, {"type": "DELETE", "lineNumber": 69, "oldContent": "              "}, {"type": "DELETE", "lineNumber": 70, "oldContent": "            });"}, {"type": "DELETE", "lineNumber": 71, "oldContent": "    return chatList;"}, {"type": "DELETE", "lineNumber": 72, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 73, "oldContent": "  Future<void> sendMessage({"}, {"type": "INSERT", "lineNumber": 68, "content": "            .then((value) {});"}, {"type": "INSERT", "lineNumber": 69, "content": "      }"}, {"type": "INSERT", "lineNumber": 70, "content": "    } else {"}, {"type": "INSERT", "lineNumber": 71, "content": "      debugPrint(\"ERROR WITH getAllChat FUNCTION\");"}, {"type": "DELETE", "lineNumber": 83, "oldContent": "    final Map<String, dynamic>? data = await ApiRequests.postApiRequest("}, {"type": "DELETE", "lineNumber": 85, "oldContent": "      context: context,"}, {"type": "DELETE", "lineNumber": 87, "oldContent": "      baseUrl: ApiKeys.baseUrl,"}, {"type": "DELETE", "lineNumber": 89, "oldContent": "      apiUrl: \"wp-json/better-messages/v1/thread/$threadId/send\","}, {"type": "DELETE", "lineNumber": 91, "oldContent": "      headers: {"}, {"type": "DELETE", "lineNumber": 93, "oldContent": "        \"Authorization\": \"Bearer $myToken\","}, {"type": "INSERT", "lineNumber": 87, "content": "    final Map<String, dynamic>? data = await ApiRequests.postApiRequest("}, {"type": "INSERT", "lineNumber": 88, "content": "      context: context,"}, {"type": "INSERT", "lineNumber": 89, "content": "      baseUrl: ApiKeys.baseUrl,"}, {"type": "INSERT", "lineNumber": 90, "content": "      apiUrl: \"wp-json/better-messages/v1/thread/$threadId/send\","}, {"type": "INSERT", "lineNumber": 91, "content": "      headers: {"}, {"type": "INSERT", "lineNumber": 92, "content": "        \"Authorization\": \"Bearer $myToken\","}]}, {"timestamp": 1755089438534, "changes": [{"type": "INSERT", "lineNumber": 83, "content": "      ApiKeys.userToken,"}, {"type": "INSERT", "lineNumber": 84, "content": "    );"}, {"type": "INSERT", "lineNumber": 85, "content": "    if (!context.mounted) return;"}, {"type": "INSERT", "lineNumber": 86, "content": ""}, {"type": "DELETE", "lineNumber": 85, "oldContent": "      ApiKeys.userToken,"}, {"type": "DELETE", "lineNumber": 88, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 91, "oldContent": "    if (!context.mounted) return;"}, {"type": "DELETE", "lineNumber": 92, "oldContent": ""}]}, {"timestamp": 1755089445911, "changes": [{"type": "MODIFY", "lineNumber": 68, "content": "            .then((value) {", "oldContent": "            .then((value) {});"}, {"type": "INSERT", "lineNumber": 69, "content": "              chatList.add("}, {"type": "INSERT", "lineNumber": 70, "content": "                ChatScreenModel.fromJson("}, {"type": "INSERT", "lineNumber": 71, "content": "                  data,"}, {"type": "INSERT", "lineNumber": 72, "content": "                  name: value.userName!,"}, {"type": "INSERT", "lineNumber": 73, "content": "                  image: value.userImage!,"}, {"type": "INSERT", "lineNumber": 74, "content": "                ),"}, {"type": "INSERT", "lineNumber": 75, "content": "              );"}, {"type": "INSERT", "lineNumber": 76, "content": "            });"}, {"type": "DELETE", "lineNumber": 72, "oldContent": "    required String message,"}, {"type": "INSERT", "lineNumber": 80, "content": "    }"}, {"type": "DELETE", "lineNumber": 84, "oldContent": "    final Map<String, dynamic>? data = await ApiRequests.postApiRequest("}, {"type": "DELETE", "lineNumber": 86, "oldContent": "      context: context,"}, {"type": "INSERT", "lineNumber": 95, "content": "    final Map<String, dynamic>? data = await ApiRequests.postApiRequest("}, {"type": "INSERT", "lineNumber": 96, "content": "      context: context,"}, {"type": "INSERT", "lineNumber": 101, "content": "        \"Content-Type\": \"application/json\","}]}, {"timestamp": 1755089519814, "changes": [{"type": "DELETE", "lineNumber": 54, "oldContent": "        // chatList.add("}, {"type": "DELETE", "lineNumber": 55, "oldContent": "        //   ChatScreenModel.fromJson("}, {"type": "DELETE", "lineNumber": 56, "oldContent": "        //     data,"}, {"type": "DELETE", "lineNumber": 57, "oldContent": "        //     name: user['name'],"}, {"type": "DELETE", "lineNumber": 58, "oldContent": "        //     image: user['avatar'],"}, {"type": "DELETE", "lineNumber": 59, "oldContent": "        //   ),"}, {"type": "DELETE", "lineNumber": 60, "oldContent": "        // );"}, {"type": "DELETE", "lineNumber": 61, "oldContent": "        await context"}, {"type": "DELETE", "lineNumber": 62, "oldContent": "            .read(ProfileProviders.profileSettingsProvidersApis)"}, {"type": "DELETE", "lineNumber": 63, "oldContent": "            .getUserData("}, {"type": "DELETE", "lineNumber": 64, "oldContent": "              context: context,"}, {"type": "DELETE", "lineNumber": 65, "oldContent": "              getMyProfileData: false,"}, {"type": "DELETE", "lineNumber": 66, "oldContent": "              userID: data['sender_id'],"}, {"type": "DELETE", "lineNumber": 67, "oldContent": "            )"}, {"type": "DELETE", "lineNumber": 68, "oldContent": "            .then((value) {"}, {"type": "DELETE", "lineNumber": 69, "oldContent": "              chatList.add("}, {"type": "INSERT", "lineNumber": 54, "content": "        chatList.add("}, {"type": "INSERT", "lineNumber": 55, "content": "          ChatScreenModel.fromJson("}, {"type": "INSERT", "lineNumber": 56, "content": "            data,"}, {"type": "INSERT", "lineNumber": 57, "content": "            name: user['name'],"}, {"type": "INSERT", "lineNumber": 58, "content": "            image: user['avatar'],"}, {"type": "INSERT", "lineNumber": 59, "content": "          ),"}, {"type": "INSERT", "lineNumber": 60, "content": "        );"}, {"type": "INSERT", "lineNumber": 61, "content": "        // await context"}, {"type": "INSERT", "lineNumber": 62, "content": "        //     .read(ProfileProviders.profileSettingsProvidersApis)"}, {"type": "INSERT", "lineNumber": 63, "content": "        //     .getUserData("}, {"type": "INSERT", "lineNumber": 64, "content": "        //       context: context,"}, {"type": "INSERT", "lineNumber": 65, "content": "        //       getMyProfileData: false,"}, {"type": "INSERT", "lineNumber": 66, "content": "        //       userID: data['sender_id'],"}, {"type": "INSERT", "lineNumber": 67, "content": "        //     )"}, {"type": "INSERT", "lineNumber": 68, "content": "        //     .then((value) {"}, {"type": "INSERT", "lineNumber": 69, "content": "        //"}, {"type": "INSERT", "lineNumber": 70, "content": "        //     });"}, {"type": "DELETE", "lineNumber": 71, "oldContent": "                ChatScreenModel.fromJson("}, {"type": "DELETE", "lineNumber": 73, "oldContent": "                  data,"}, {"type": "DELETE", "lineNumber": 75, "oldContent": "                  name: value.userName!,"}, {"type": "DELETE", "lineNumber": 76, "oldContent": "                  image: value.userImage!,"}, {"type": "DELETE", "lineNumber": 77, "oldContent": "  }) async {"}, {"type": "DELETE", "lineNumber": 78, "oldContent": "                ),"}, {"type": "INSERT", "lineNumber": 74, "content": "    }"}, {"type": "INSERT", "lineNumber": 75, "content": ""}, {"type": "DELETE", "lineNumber": 80, "oldContent": "              );"}, {"type": "DELETE", "lineNumber": 82, "oldContent": "            });"}, {"type": "DELETE", "lineNumber": 87, "oldContent": "    }"}, {"type": "INSERT", "lineNumber": 89, "content": "    final Map<String, dynamic>? data = await ApiRequests.postApiRequest("}, {"type": "INSERT", "lineNumber": 90, "content": "      context: context,"}, {"type": "INSERT", "lineNumber": 95, "content": "        \"Content-Type\": \"application/json\","}, {"type": "INSERT", "lineNumber": 96, "content": "      },"}, {"type": "DELETE", "lineNumber": 101, "oldContent": "    final Map<String, dynamic>? data = await ApiRequests.postApiRequest("}, {"type": "DELETE", "lineNumber": 103, "oldContent": "      context: context,"}, {"type": "DELETE", "lineNumber": 109, "oldContent": "        \"Content-Type\": \"application/json\","}]}]}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/app_config/api_requests.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/app_config/api_requests.dart", "baseContent": "import 'dart:async';\nimport 'dart:convert';\nimport 'dart:io';\nimport 'package:afa_app/app_config/common_components.dart';\nimport 'package:flutter/material.dart';\nimport 'package:http/http.dart' as http;\n\nclass ApiRequests {\n  static Future<dynamic> getApiRequest({\n    required BuildContext context,\n    required String baseUrl,\n    required String apiUrl,\n    required Map<String, String> headers,\n  }) async {\n    try {\n      if (await CommonComponents.checkConnectivity()) {\n        final String url = \"$baseUrl$apiUrl\";\n\n        final http.Response response = await http.get(\n          Uri.parse(url),\n          headers: {}..addAll(headers),\n        );\n\n        if (response.statusCode == 200) {\n          final succesDecodedData = jsonDecode(response.body);\n          return succesDecodedData;\n        } else if (response.statusCode == 500) {\n          if (!context.mounted) return;\n          await CommonComponents.showCustomizedAlert(\n            context: context,\n            title: \"Server Error\",\n            subTitle: \"internal_server_error\",\n          );\n          final failedDecodedData = jsonDecode(response.body);\n          return failedDecodedData;\n        } else {\n          debugPrint(\"ERROR WITH $apiUrl STATUSCODE !=200\");\n          final failedDecodedData = jsonDecode(response.body);\n          return failedDecodedData;\n        }\n      } else {\n        if (context.mounted) {\n          await CommonComponents.notConnectionAlert(context);\n        }\n      }\n    } on TimeoutException catch (error) {\n      if (context.mounted) {\n        debugPrint(\"Time Out Exception is::=>$error with api=>$apiUrl\");\n        if (!context.mounted) return;\n        await CommonComponents.timeOutExceptionAlert(context);\n      }\n    } on SocketException catch (error) {\n      debugPrint(\"Socket Exception is::=>$error with api=>$apiUrl\");\n      if (!context.mounted) return;\n      await CommonComponents.socketExceptionAlert(context);\n    } catch (error) {\n      debugPrint(\"General Exception is::=>$error with api=>$apiUrl\");\n    }\n  }\n\n  static Future<dynamic> postApiRequest({\n    required BuildContext context,\n    required String baseUrl,\n    required String apiUrl,\n    required Map<String, String> headers,\n    required dynamic body,\n    bool showLoadingWidget = true,\n  }) async {\n    try {\n      if (await CommonComponents.checkConnectivity()) {\n      // if (await CommonComponents.checkConnectivity()) {\n        if (!context.mounted) return;\n        if (showLoadingWidget) CommonComponents.loading(context);\n\n        final String url = \"$baseUrl$apiUrl\";\n\n        final http.Response response = await http.post(\n          Uri.parse(url),\n          headers: {}..addAll(headers),\n          body: body,\n        );\n\n        if (response.statusCode == 200 || response.statusCode == 201) {\n          if (!context.mounted) return;\n          if (showLoadingWidget) Navigator.pop(context);\n          final successDecodedData = jsonDecode(response.body);\n          return successDecodedData;\n        } else if (response.statusCode == 500) {\n          if (!context.mounted) return;\n          await CommonComponents.showCustomizedAlert(\n            context: context,\n            title: \"Server Error\",\n            subTitle: \"internal_server_error\",\n          );\n          final failedDecodedData = jsonDecode(response.body);\n          return failedDecodedData;\n        } else {\n          debugPrint(\n            \"POST METHOD=> status Code !=200 or 201 with api=>$apiUrl\",\n          );\n          if (!context.mounted) return;\n          if (showLoadingWidget) Navigator.pop(context);\n          final failedDecodedData = jsonDecode(response.body);\n          return failedDecodedData;\n        }\n      } else {\n        if (!context.mounted) return;\n        await CommonComponents.notConnectionAlert(context);\n      }\n    } on TimeoutException catch (error) {\n      debugPrint(\"Time Out Exception is::=>$error with api=>$apiUrl\");\n\n      if (!context.mounted) return;\n      Navigator.pop(context);\n      await CommonComponents.timeOutExceptionAlert(context);\n    } on SocketException catch (error) {\n      debugPrint(\"Socket Exception is::=>$error with api=>$apiUrl\");\n      if (!context.mounted) return;\n      Navigator.pop(context);\n      await CommonComponents.socketExceptionAlert(context);\n    } catch (error) {\n      if (!context.mounted) return;\n      Navigator.pop(context);\n      debugPrint(\"General Exception is::=>$error with api=>$apiUrl\");\n    }\n  }\n\n  static Future<dynamic> putApiRequest({\n    required BuildContext context,\n    required String baseUrl,\n    required String apiUrl,\n    required Map<String, String> headers,\n    required dynamic body,\n    bool showLoadingWidget = true,\n  }) async {\n    try {\n      if (await CommonComponents.checkConnectivity()) {\n        if (!context.mounted) return;\n        if (showLoadingWidget) CommonComponents.loading(context);\n\n        final String url = \"$baseUrl$apiUrl\";\n        final http.Response response = await http.put(\n          Uri.parse(url),\n          headers: {}..addAll(headers),\n          body: body,\n        );\n\n        if (response.statusCode == 200 || response.statusCode == 201) {\n          if (!context.mounted) return;\n          if (showLoadingWidget) Navigator.pop(context);\n          final succeededDecodedData = jsonDecode(response.body);\n\n          return succeededDecodedData;\n        } else if (response.statusCode == 500) {\n          if (!context.mounted) return;\n          await CommonComponents.showCustomizedAlert(\n            context: context,\n            title: \"Server Error\",\n            subTitle: \"internal_server_error\",\n          );\n          final failedDecodedData = jsonDecode(response.body);\n          return failedDecodedData;\n        } else {\n          debugPrint(\"PUT METHOD status Code !=200 or 201 with api=>$apiUrl\");\n          if (!context.mounted) return;\n          if (showLoadingWidget) Navigator.pop(context);\n          final failedDecodedData = jsonDecode(response.body);\n          return failedDecodedData;\n        }\n      } else {\n        if (!context.mounted) return;\n        await CommonComponents.notConnectionAlert(context);\n      }\n    } on TimeoutException catch (error) {\n      debugPrint(\"Time Out Exception is::=>$error with api=>$apiUrl\");\n\n      if (!context.mounted) return;\n      Navigator.pop(context);\n      await CommonComponents.timeOutExceptionAlert(context);\n    } on SocketException catch (error) {\n      debugPrint(\"Socket Exception is::=>$error with api=>$apiUrl\");\n      if (!context.mounted) return;\n      Navigator.pop(context);\n      await CommonComponents.socketExceptionAlert(context);\n    } catch (error) {\n      if (!context.mounted) return;\n      Navigator.pop(context);\n      debugPrint(\"General Exception is::=>$error with api=>$apiUrl\");\n    }\n  }\n\n  static Future<dynamic> deleteApiRequest({\n    required BuildContext context,\n    required String baseUrl,\n    required String apiUrl,\n    required Map<String, String> headers,\n    bool showLoadingWidget = true,\n  }) async {\n    try {\n      if (await CommonComponents.checkConnectivity()) {\n        if (!context.mounted) return;\n        if (showLoadingWidget) CommonComponents.loading(context);\n\n        final String url = \"$baseUrl$apiUrl\";\n        final http.Response response = await http.delete(\n          Uri.parse(url),\n          headers: {}..addAll(headers),\n        );\n\n        if (response.statusCode == 200 || response.statusCode == 201) {\n          if (!context.mounted) return;\n          if (showLoadingWidget) Navigator.pop(context);\n          final suceededDecodedData = jsonDecode(response.body);\n          return suceededDecodedData;\n        } else if (response.statusCode == 500) {\n          if (!context.mounted) return;\n          await CommonComponents.showCustomizedAlert(\n            context: context,\n            title: \"Server Error\",\n            subTitle: \"internal_server_error\",\n          );\n          final failedDecodedData = jsonDecode(response.body);\n          return failedDecodedData;\n        } else {\n          debugPrint(\n            \"DELETE METHOD status Code !=200 or 201 with api=>$apiUrl\",\n          );\n          if (!context.mounted) return;\n          if (showLoadingWidget) Navigator.pop(context);\n          final failedDecodedData = jsonDecode(response.body);\n          return failedDecodedData;\n        }\n      } else {\n        if (!context.mounted) return;\n        await CommonComponents.notConnectionAlert(context);\n      }\n    } on TimeoutException catch (error) {\n      debugPrint(\"Time Out Exception is::=>$error with api=>$apiUrl\");\n\n      if (!context.mounted) return;\n      Navigator.pop(context);\n      await CommonComponents.timeOutExceptionAlert(context);\n    } on SocketException catch (error) {\n      debugPrint(\"Socket Exception is::=>$error with api=>$apiUrl\");\n      if (!context.mounted) return;\n      Navigator.pop(context);\n      await CommonComponents.socketExceptionAlert(context);\n    } catch (error) {\n      if (!context.mounted) return;\n      Navigator.pop(context);\n      debugPrint(\"General Exception is::=>$error with api=>$apiUrl\");\n    }\n  }\n}\n", "baseTimestamp": 1755086058784, "deltas": [{"timestamp": 1755086063914, "changes": [{"type": "DELETE", "lineNumber": 69, "oldContent": "      if (await CommonComponents.checkConnectivity()) {"}, {"type": "DELETE", "lineNumber": 70, "oldContent": "      // if (await CommonComponents.checkConnectivity()) {"}, {"type": "INSERT", "lineNumber": 69, "content": "      if (true) {"}, {"type": "INSERT", "lineNumber": 70, "content": "        // if (await CommonComponents.checkConnectivity()) {"}]}, {"timestamp": 1755086082904, "changes": [{"type": "DELETE", "lineNumber": 69, "oldContent": "      if (true) {"}, {"type": "MODIFY", "lineNumber": 69, "content": "      if (await CommonComponents.checkConnectivity()) {", "oldContent": "        // if (await CommonComponents.checkConnectivity()) {"}]}]}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/app_config/common_components.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/app_config/common_components.dart", "baseContent": "import 'dart:io';\n\nimport 'package:afa_app/app_config/app_colors.dart';\nimport 'package:afa_app/app_config/app_images.dart';\nimport 'package:cached_network_image/cached_network_image.dart';\nimport 'package:connectivity_plus/connectivity_plus.dart';\nimport 'package:flutter/material.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:image_picker/image_picker.dart';\nimport 'package:shared_preferences/shared_preferences.dart';\nimport 'package:url_launcher/url_launcher.dart';\n\nclass CommonComponents {\n  static String imageNotFound =\n      \"https://media.istockphoto.com/id/1055079680/vector/black-linear-photo-camera-like-no-image-available.jpg?s=612x612&w=0&k=20&c=P1DebpeMIAtXj_ZbVsKVvg-duuL0v9DlrOZUvPG6UJk=\";\n\n  static Widget imageAssetWithCache({\n    required BuildContext context,\n    required String image,\n    required double height,\n    required double width,\n    required BoxFit fit,\n  }) {\n    final double pixelRatio = View.of(context).devicePixelRatio;\n    return Image.asset(\n      image,\n      height: height,\n      width: width,\n      cacheHeight: (height * pixelRatio).round(),\n      cacheWidth: (width * pixelRatio).round(),\n      fit: fit,\n    );\n  }\n\n  static Widget imageWithNetworkCache({\n    required BuildContext context,\n    required String image,\n    required double height,\n    required double width,\n    required BoxFit fit,\n  }) {\n    return CachedNetworkImage(\n      imageUrl: image,\n      height: height,\n      width: width,\n      fit: fit,\n      memCacheHeight: (height * View.of(context).devicePixelRatio).round(),\n      memCacheWidth: (width * View.of(context).devicePixelRatio).round(),\n    );\n  }\n\n  static Widget imageFromFile({\n    required BuildContext context,\n    required File image,\n    required double height,\n    required double width,\n    required BoxFit fit,\n  }) {\n    return Image.file(\n      image,\n      height: height,\n      width: width,\n      cacheHeight: (height * View.of(context).devicePixelRatio).round(),\n      cacheWidth: (width * View.of(context).devicePixelRatio).round(),\n      fit: fit,\n    );\n  }\n\n  static Widget comonTitleScreen({\n    required BuildContext context,\n    required String title,\n  }) {\n    return Row(\n      children: [\n        ElevatedButton(\n          onPressed: () {\n            Navigator.pop(context);\n          },\n\n          style: ElevatedButton.styleFrom(\n            elevation: 0.0,\n            shape: const CircleBorder(),\n            foregroundColor: AppColors.blackColor,\n            backgroundColor: AppColors.greyColor.withAlpha((0.2 * 255).toInt()),\n            minimumSize: Size(40.0.w, 40.0.h),\n          ),\n          child: Icon(Icons.arrow_back, size: 24.0.h),\n        ),\n        Text(\n          title,\n          style: TextStyle(fontSize: 20.0.sp, fontWeight: FontWeight.bold),\n        ),\n      ],\n    );\n  }\n\n  static Future<bool> checkConnectivity() async {\n    return true;\n    final List<ConnectivityResult> result = await Connectivity()\n        .checkConnectivity();\n\n    if (result[0] == ConnectivityResult.none) {\n      return false;\n    } else if (result[0] == ConnectivityResult.wifi ||\n        result[0] == ConnectivityResult.mobile) {\n      return true;\n    } else {\n      return false;\n    }\n  }\n\n  static Future<void> loading(BuildContext context) async {\n    await showDialog(\n      barrierDismissible: false,\n      context: context,\n      builder: (context) => Center(\n        child: SizedBox(\n          height: 50.0.h,\n          width: 50.0.w,\n          child: CircularProgressIndicator(\n            valueColor: const AlwaysStoppedAnimation(\n              AppColors.midLevelGreenColor,\n            ),\n            strokeWidth: 5.0.w,\n          ),\n        ),\n      ),\n    );\n  }\n\n  static Widget loadingDataFromServer() => const Center(\n    child: CircularProgressIndicator(\n      valueColor: AlwaysStoppedAnimation<Color>(AppColors.midLevelGreenColor),\n    ),\n  );\n\n  static Future showCustomizedAlert({\n    required BuildContext context,\n    required String title,\n    required String subTitle,\n  }) async {\n    return await showGeneralDialog(\n      context: context,\n      pageBuilder: (context, animation1, animation2) => Container(),\n      transitionDuration: const Duration(milliseconds: 400),\n      transitionBuilder: (context, animation1, animation2, child) =>\n          ScaleTransition(\n            scale: animation1,\n            child: AlertDialog(\n              shape: RoundedRectangleBorder(\n                borderRadius: BorderRadius.circular(20.0),\n              ),\n              title: Column(\n                mainAxisSize: MainAxisSize.min,\n                children: [\n                  CommonComponents.imageAssetWithCache(\n                    context: context,\n                    image: AppImages.logoTestImage,\n                    height: 50.0.h,\n                    width: 50.0.w,\n                    fit: BoxFit.contain,\n                  ),\n                  SizedBox(height: 5.0.h),\n                  Text(\n                    title,\n                    style: TextStyle(\n                      color: AppColors.midLevelGreenColor,\n                      fontSize: 16.0.sp,\n                      fontWeight: FontWeight.bold,\n                    ),\n                  ),\n                ],\n              ),\n              content: Text(\n                subTitle,\n                style: TextStyle(\n                  fontSize: 16.0.sp,\n                  fontWeight: FontWeight.bold,\n                  color: AppColors.blackColor,\n                ),\n                textAlign: TextAlign.center,\n              ),\n              actions: [\n                TextButton(\n                  style: TextButton.styleFrom(foregroundColor: Colors.green),\n                  onPressed: () {\n                    Navigator.pop(context);\n                  },\n                  child: Text(\"OK\", style: TextStyle(fontSize: 18.0.sp)),\n                ),\n              ],\n            ),\n          ),\n    );\n  }\n\n  static Widget noDataFoundWidget() {\n    return Text(\n      'No data found',\n      style: TextStyle(\n        fontSize: 18.0.sp,\n        fontWeight: FontWeight.bold,\n        color: AppColors.midLevelGreenColor,\n      ),\n    );\n  }\n\n  static Future notConnectionAlert(BuildContext context) async {\n    await showCustomizedAlert(\n      context: context,\n      title: \"No Connection To Network\",\n      subTitle: \"Please Connect To Network To Wifi Or Mobile Data\",\n    );\n  }\n\n  static Future timeOutExceptionAlert(BuildContext context) async {\n    await showCustomizedAlert(\n      context: context,\n      title: \"Server Busy\",\n      subTitle: \"Network Busy please Try Again Later\",\n    );\n  }\n\n  static Future socketExceptionAlert(BuildContext context) async {\n    await CommonComponents.showCustomizedAlert(\n      context: context,\n      title: \"Connection Error\",\n      subTitle: \"Please Make sure your Database Server is connected\",\n    );\n  }\n\n  static Future<bool> saveData({\n    required String key,\n    required dynamic value,\n  }) async {\n    final SharedPreferences prefs = await SharedPreferences.getInstance();\n    if (value is String) {\n      return await prefs.setString(key, value);\n    } else if (value is bool) {\n      return await prefs.setBool(key, value);\n    } else {\n      return await prefs.setInt(key, value);\n    }\n  }\n\n  static Future<dynamic> getSavedData(String key) async {\n    final SharedPreferences prefs = await SharedPreferences.getInstance();\n    return prefs.get(key);\n  }\n\n  static Future<void> deleteSavedData(String key) async {\n    final SharedPreferences prefs = await SharedPreferences.getInstance();\n    prefs.remove(key);\n  }\n\n  static void showCustomizedSnackBar({\n    required BuildContext context,\n    required String title,\n  }) {\n    ScaffoldMessenger.of(context).showSnackBar(\n      SnackBar(\n        content: Text(\n          title,\n          style: TextStyle(\n            color: Colors.white,\n            fontSize: 15.0.sp,\n            fontWeight: FontWeight.bold,\n          ),\n          textAlign: TextAlign.center,\n        ),\n        duration: const Duration(seconds: 2),\n        behavior: SnackBarBehavior.floating,\n        backgroundColor: AppColors.midLevelGreenColor,\n      ),\n    );\n  }\n\n  static Future<void> launchOnBrowser({\n    required BuildContext context,\n    required String url,\n  }) async {\n    if (await canLaunchUrl(Uri.parse(url))) {\n      await launchUrl(Uri.parse(url));\n    } else {\n      if (!context.mounted) return;\n      showCustomizedSnackBar(context: context, title: \"Invalid URL\");\n    }\n  }\n\n  static Future<File?> pickImage({\n    required BuildContext context,\n    required ImageSource source,\n  }) async {\n    File? image;\n\n    final pickImage = await ImagePicker().pickImage(\n      source: source,\n      imageQuality: 50,\n    );\n\n    if (pickImage != null) {\n      image = File(pickImage.path);\n      if (context.mounted) {\n        showCustomizedSnackBar(\n          context: context,\n          title: \"Image Uploded Successfully\",\n        );\n\n        return image;\n      } else {\n        return null;\n      }\n    } else {\n      if (context.mounted) {\n        showCustomizedSnackBar(\n          context: context,\n          title: \"Unable To Upload Image\",\n        );\n        return null;\n      } else {\n        return null;\n      }\n    }\n  }\n}\n", "baseTimestamp": 1755086087336, "deltas": [{"timestamp": 1755086094435, "changes": [{"type": "MODIFY", "lineNumber": 97, "content": "    return true;//TODO-fix", "oldContent": "    return true;"}]}, {"timestamp": 1755086096459, "changes": [{"type": "MODIFY", "lineNumber": 97, "content": "    return true; //TODO-fix", "oldContent": "    return true;//TODO-fix"}]}, {"timestamp": 1755094538415, "changes": [{"type": "DELETE", "lineNumber": 97, "oldContent": "    return true; //TODO-fix"}]}]}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/providers/home_categories_screens_providers_apis/question_and_answers_providers_apis.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/providers/home_categories_screens_providers_apis/question_and_answers_providers_apis.dart", "baseContent": "import 'dart:math';\n\nimport 'package:afa_app/app_config/api_keys.dart';\nimport 'package:afa_app/app_config/api_requests.dart';\nimport 'package:afa_app/app_config/common_components.dart';\nimport 'package:afa_app/models/home_screens_models/sessions_q&a_models/question_model.dart';\nimport 'package:afa_app/models/home_screens_models/sessions_q&a_models/session_questions_model.dart';\nimport 'package:afa_app/models/home_screens_models/sessions_q&a_models/session_time_periods_model.dart';\nimport 'package:flutter/material.dart';\n\nclass QuestionAndAnswersProvidersApis extends ChangeNotifier {\n  SessionQAndADaysModel? getQandAByTimePeriod;\n  Key? questionsKey;\n\n  void selectTimePeriod(SessionQAndADaysModel? daysModel) {\n    getQandAByTimePeriod = daysModel;\n    notifyListeners();\n  }\n\n  void rebuildQandAWidget() {\n    questionsKey = ValueKey(Random().nextInt(10000));\n    notifyListeners();\n  }\n\n  Future<List<SessionQAndADaysModel>> getQAndADaysList({\n    required BuildContext context,\n  }) async {\n    final List<SessionQAndADaysModel> daysList = [];\n\n    final Map<String, dynamic>? dataList = await ApiRequests.getApiRequest(\n      context: context,\n      baseUrl: ApiKeys.baseUrl,\n      apiUrl: \"wp-json/buddypress-ext/v1/agenda/filters-list\",\n      headers: {},\n    );\n\n    if (dataList != null) {\n      for (final data in dataList['time_periods']) {\n        daysList.add(SessionQAndADaysModel.fromJson(data));\n      }\n    } else {\n      debugPrint(\"ERROR WITH getAgendaFiltersList FUNCTION\");\n    }\n    daysList.removeAt(0);\n    return daysList;\n  }\n\n  Future<List<SessionQuestionsModel>> getSessionQuestions({\n    required BuildContext context,\n    required bool setInitialTimePeriod,\n  }) async {\n    final List<SessionQuestionsModel> sessionList = [];\n    if (setInitialTimePeriod) {\n      await getQAndADaysList(context: context).then((value) {\n        selectTimePeriod(value[0]);\n      });\n    }\n\n    if (!context.mounted) return [];\n\n    final List<dynamic>? dataList = await ApiRequests.getApiRequest(\n      context: context,\n      baseUrl: ApiKeys.baseUrl,\n      apiUrl:\n          \"wp-json/buddypress-ext/v1/agenda/filter?time_period=${getQandAByTimePeriod!.dayName}\",\n      headers: {},\n    );\n\n    if (dataList != null) {\n      for (final data in dataList) {\n        if (data['is_comment_open']) {\n          sessionList.add(SessionQuestionsModel.fromJson(data));\n        }\n      }\n    } else {\n      debugPrint(\"ERROR WITH getQuestions FUNCTION\");\n    }\n\n    return sessionList;\n  }\n\n  Future<List<QuestionsModel>> getQuestions({\n    required BuildContext context,\n  }) async {\n    final List<QuestionsModel> questionList = [];\n\n    final List<dynamic>? dataList = await ApiRequests.getApiRequest(\n      context: context,\n      baseUrl: ApiKeys.baseUrl,\n      apiUrl:\n          \"wp-json/buddypress-ext/v1/agenda/filter?time_period=${getQandAByTimePeriod!.dayName}\",\n      headers: {},\n    );\n    \n    log\n\n    if (dataList != null) {\n      for (final data in dataList) {\n        for (final question in data['questions']) {\n          questionList.add(QuestionsModel.fromJson(question));\n        }\n      }\n    } else {\n      debugPrint(\"ERROR WITH getQuestions FUNCTION\");\n    }\n\n    return questionList;\n  }\n\n  Future<void> createQuestion({\n    required BuildContext context,\n    required String question,\n    required int sessionID,\n  }) async {\n    final String myToken = await CommonComponents.getSavedData(\n      ApiKeys.userToken,\n    );\n\n    if (!context.mounted) return;\n\n    final Map<String, dynamic>? data = await ApiRequests.postApiRequest(\n      context: context,\n      baseUrl: ApiKeys.baseUrl,\n      apiUrl: \"wp-json/buddypress-ext/v1/agenda/$sessionID/questions\",\n      headers: {\"Authorization\": \"Bearer $myToken\"},\n      body: {\"content\": question},\n    );\n    if (data != null) {\n      if (!context.mounted) return;\n      CommonComponents.showCustomizedSnackBar(\n        context: context,\n        title: \"Your Question Added Successfully\",\n      );\n      Navigator.pop(context);\n    } else {\n      debugPrint(\"ERROR WITH createQuestion FUNCTION\");\n    }\n  }\n}\n", "baseTimestamp": 1755086246120, "deltas": [{"timestamp": 1755086260683, "changes": [{"type": "MODIFY", "lineNumber": 94, "content": "    print('FULL_URLLL $')", "oldContent": "    log"}]}, {"timestamp": 1755086262820, "changes": [{"type": "MODIFY", "lineNumber": 94, "content": "    print('FULL_URLLL ${ApiKeys.baseUrl}wp-json/buddypress-ext/v1/agenda/filter?time_period=${getQandAByTimePeriod!.dayName}');", "oldContent": "    print('FULL_URLLL $')"}]}, {"timestamp": 1755086265594, "changes": [{"type": "DELETE", "lineNumber": 93, "oldContent": "    "}, {"type": "MODIFY", "lineNumber": 94, "content": "    print(", "oldContent": "    print('FULL_URLLL ${ApiKeys.baseUrl}wp-json/buddypress-ext/v1/agenda/filter?time_period=${getQandAByTimePeriod!.dayName}');"}, {"type": "INSERT", "lineNumber": 95, "content": "      'FULL_URLLL ${ApiKeys.baseUrl}wp-json/buddypress-ext/v1/agenda/filter?time_period=${getQandAByTimePeriod!.dayName}',"}, {"type": "INSERT", "lineNumber": 96, "content": "    );"}, {"type": "INSERT", "lineNumber": 97, "content": ""}]}, {"timestamp": 1755086272412, "changes": [{"type": "INSERT", "lineNumber": 93, "content": ""}, {"type": "DELETE", "lineNumber": 95, "oldContent": ""}, {"type": "MODIFY", "lineNumber": 98, "content": "    if (dataList != null) {", "oldContent": "    if (dataList != null) {"}]}, {"timestamp": 1755086311562, "changes": [{"type": "DELETE", "lineNumber": 94, "oldContent": "    print("}, {"type": "DELETE", "lineNumber": 95, "oldContent": "      'FULL_URLLL ${ApiKeys.baseUrl}wp-json/buddypress-ext/v1/agenda/filter?time_period=${getQandAByTimePeriod!.dayName}',"}, {"type": "DELETE", "lineNumber": 96, "oldContent": "    );"}, {"type": "INSERT", "lineNumber": 94, "content": " "}, {"type": "DELETE", "lineNumber": 98, "oldContent": "    if (dataList != null) {"}]}, {"timestamp": 1755086316938, "changes": [{"type": "DELETE", "lineNumber": 94, "oldContent": " "}]}, {"timestamp": 1755086327029, "changes": [{"type": "INSERT", "lineNumber": 67, "content": "    "}, {"type": "INSERT", "lineNumber": 68, "content": "    printL"}]}, {"timestamp": 1755086337116, "changes": [{"type": "INSERT", "lineNumber": 68, "content": "    print('FULL_URRRRL ')"}, {"type": "DELETE", "lineNumber": 69, "oldContent": "    printL"}]}, {"timestamp": 1755086339145, "changes": [{"type": "MODIFY", "lineNumber": 68, "content": "    print('FULL_URRRRL ${ApiKeys.baseUrl}wp-json/buddypress-ext/v1/agenda/filter?time_period=${getQandAByTimePeriod!.dayName}');", "oldContent": "    print('FULL_URRRRL ')"}]}, {"timestamp": 1755086342063, "changes": [{"type": "DELETE", "lineNumber": 67, "oldContent": "    "}, {"type": "MODIFY", "lineNumber": 68, "content": "    print(", "oldContent": "    print('FULL_URRRRL ${ApiKeys.baseUrl}wp-json/buddypress-ext/v1/agenda/filter?time_period=${getQandAByTimePeriod!.dayName}');"}, {"type": "INSERT", "lineNumber": 69, "content": "      'FULL_URRRRL ${ApiKeys.baseUrl}wp-json/buddypress-ext/v1/agenda/filter?time_period=${getQandAByTimePeriod!.dayName}',"}, {"type": "INSERT", "lineNumber": 70, "content": "    );"}, {"type": "INSERT", "lineNumber": 71, "content": ""}]}]}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/providers/athuntication_providers/reset_password_providers_apis.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/providers/athuntication_providers/reset_password_providers_apis.dart", "baseContent": "import 'dart:convert';\nimport 'dart:developer';\n\nimport 'package:afa_app/app_config/api_keys.dart';\nimport 'package:afa_app/app_config/api_requests.dart';\nimport 'package:afa_app/app_config/common_components.dart';\nimport 'package:afa_app/models/athuntication_models/reset_password_model.dart';\nimport 'package:flutter/material.dart';\n\nclass ResetPasswordProvidersApis extends ChangeNotifier {\n  Future<void> resetPassword({\n    required BuildContext context,\n    required String email,\n  }) async {\n    final ResetPasswordModel model = ResetPasswordModel(email: email);\n\n    final Map<String, dynamic>? data = await ApiRequests.postApiRequest(\n      context: context,\n      baseUrl: ApiKeys.baseUrl,\n      apiUrl: \"wp-json/buddypress-ext/v1/auth/password/reset\",\n      headers: {\"Content-Type\": \"application/json\"},\n      body: jsonEncode(model.toJson()),\n    );\n    \n    log('')\n\n    if (data != null) {\n      if (!context.mounted) return;\n      CommonComponents.showCustomizedSnackBar(\n        context: context,\n        title: data['message'],\n      );\n\n      Navigator.pop(context);\n    } else {\n      debugPrint(\"ERROR WITH resetPassword FUNCTION\");\n    }\n  }\n}\n", "baseTimestamp": 1755086493232, "deltas": [{"timestamp": 1755086496311, "changes": [{"type": "MODIFY", "lineNumber": 24, "content": "    log('data');", "oldContent": "    log('')"}]}, {"timestamp": 1755086501799, "changes": [{"type": "DELETE", "lineNumber": 23, "oldContent": "    "}, {"type": "MODIFY", "lineNumber": 24, "content": "    log('dataAAA ${data}');", "oldContent": "    log('data');"}, {"type": "INSERT", "lineNumber": 25, "content": ""}]}]}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/widgets/home_screens_widgets.dart/home_screen_widgets.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/widgets/home_screens_widgets.dart/home_screen_widgets.dart", "baseContent": "import 'package:afa_app/app_config/app_colors.dart';\nimport 'package:afa_app/app_config/app_images.dart';\nimport 'package:afa_app/app_config/common_components.dart';\nimport 'package:afa_app/app_config/routes.dart';\nimport 'package:flutter/material.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\n\nclass HomeScreenWidgets {\n  static Widget headerScreenWidget({\n    required BuildContext context,\n    required List<Map<String, dynamic>> eventTimer,\n  }) {\n    return Container(\n      height: 254.0.h,\n      width: 398.0.w,\n      padding: EdgeInsets.all(12.0.h),\n      decoration: BoxDecoration(\n        color: AppColors.darkGreenColor,\n        borderRadius: BorderRadius.all(Radius.circular(12.0.r)),\n      ),\n      child: Column(\n        crossAxisAlignment: CrossAxisAlignment.center,\n\n        // mainAxisAlignment: MainAxisAlignment.center,\n        children: [\n          CommonComponents.imageAssetWithCache(\n            context: context,\n            image: AppImages.logoImage,\n            height: 56.0.h,\n            width: 198.0.w,\n            fit: BoxFit.contain,\n          ),\n          SizedBox(height: 15.0.h),\n          Text(\n            \"37th AFA Int’l Technical Conference & Exhibition\",\n            style: TextStyle(\n              fontSize: 18.0.sp,\n              fontWeight: FontWeight.bold,\n              color: Colors.white,\n            ),\n            textAlign: TextAlign.center,\n          ),\n          SizedBox(height: 25.0.h),\n          Row(\n            mainAxisAlignment: MainAxisAlignment.spaceAround,\n            children: eventTimer\n                .map(\n                  (elements) => Row(\n                    children: [\n                      Column(\n                        crossAxisAlignment: CrossAxisAlignment.center,\n\n                        children: [\n                          Text(\n                            elements['time'].toString(),\n                            style: TextStyle(\n                              fontSize: 22.0.sp,\n                              fontWeight: FontWeight.bold,\n                              color: Colors.white,\n                            ),\n                          ),\n                          SizedBox(height: 10.0.h),\n                          Text(\n                            elements['title'],\n                            style: TextStyle(\n                              fontSize: 12.0.sp,\n                              fontWeight: FontWeight.bold,\n                              color: Colors.white,\n                            ),\n                          ),\n                        ],\n                      ),\n                      elements['title'] != \"Seconds\"\n                          ? Container(\n                              margin: EdgeInsets.only(left: 20.0.w),\n                              height: 52.0.h,\n                              width: 2.0.w,\n                              color: Colors.white,\n                            )\n                          : const SizedBox(),\n                    ],\n                  ),\n                )\n                .toList(),\n          ),\n        ],\n      ),\n    );\n  }\n\n  // static List<Map<String, dynamic>> eventTimer = [\n  //   {\"time\": \"-570\", \"title\": \"Days\"},\n  //   {\"time\": \"-09\", \"title\": \"Hours\"},\n  //   {\"time\": \"50\", \"title\": \"Minute\"},\n  //   {\"time\": \"09\", \"title\": \"Seconds\"},\n  // ];\n\n  static List<Map<String, dynamic>> homFiledsList = [\n    {\n      \"title\": \"Agenda\",\n      \"image\": AppImages.agendaIcon,\n      \"path\": PATHS.agendaScreen,\n    },\n    {\n      \"title\": \"Attendance\",\n      \"image\": AppImages.attendanceIcon,\n      \"path\": PATHS.attendanceScreen,\n    },\n    {\n      \"title\": \"Speakers\",\n      \"image\": AppImages.speakersIcon,\n      \"path\": PATHS.speakersScreen,\n    },\n    {\n      \"title\": \"Exibitors\",\n      \"image\": AppImages.exibitorsIcon,\n      \"path\": PATHS.exibitorsScreen,\n    },\n    {\n      \"title\": \"Sponsors\",\n      \"image\": AppImages.sponsorsIcon,\n      \"path\": PATHS.sponsorsScreen,\n    },\n    {\n      \"title\": \"Venue\",\n      \"image\": AppImages.venueIcon,\n      \"path\": PATHS.accomodationScreen,\n    },\n    {\n      \"title\": \"Session Q&A\",\n      \"image\": AppImages.sessionIcon,\n      \"path\": PATHS.sessionQuestionsScreen,\n    },\n    {\n      \"title\": \"Resources\",\n      \"image\": AppImages.resourcsesIcon,\n      \"path\": PATHS.resourcesScreen,\n    },\n  ];\n}\n", "baseTimestamp": 1755088173794, "deltas": [{"timestamp": 1755088177966, "changes": [{"type": "MODIFY", "lineNumber": 114, "content": "      \"title\": \"Exhibitors\",", "oldContent": "      \"title\": \"Exibitors\","}]}]}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/screens/home_screens/home_categories_screens/speakers_screens/request_meeting_screen_speaker.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/screens/home_screens/home_categories_screens/speakers_screens/request_meeting_screen_speaker.dart", "baseContent": "import 'package:afa_app/app_config/api_providers/profile_providers.dart';\nimport 'package:afa_app/app_config/app_colors.dart';\nimport 'package:afa_app/widgets/home_screens_widgets.dart/speakers_screens_widgets/request_meeting_speaker_widgets.dart';\nimport 'package:flutter/material.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:riverpod_context/riverpod_context.dart';\n\nclass RequestMeetingScreenSpeaker extends StatefulWidget {\n  const RequestMeetingScreenSpeaker({super.key});\n\n  @override\n  State<RequestMeetingScreenSpeaker> createState() =>\n      _RequestMeetingScreenSpeakerState();\n}\n\nclass _RequestMeetingScreenSpeakerState\n    extends State<RequestMeetingScreenSpeaker> {\n  final TextEditingController _titleController = TextEditingController();\n  final TextEditingController _descriptionController = TextEditingController();\n  final TextEditingController _linkOrLocationController =\n      TextEditingController();\n  final TextEditingController _dateController = TextEditingController();\n  final TextEditingController _timeController = TextEditingController();\n  final _formKey = GlobalKey<FormState>();\n  @override\n  void dispose() {\n    _titleController.dispose();\n    _descriptionController.dispose();\n    _linkOrLocationController.dispose();\n    _dateController.dispose();\n    _timeController.dispose();\n    super.dispose();\n  }\n\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      body: SafeArea(\n        child: SingleChildScrollView(\n          physics: const BouncingScrollPhysics(),\n          padding: EdgeInsets.all(15.0.h),\n          child: Form(\n            key: _formKey,\n            child: Column(\n              spacing: 25.0.h,\n              crossAxisAlignment: CrossAxisAlignment.start,\n              children: [\n                Row(\n                  children: [\n                    \n                    Text(\n                      \"Request Meeting\",\n                      style: TextStyle(\n                        fontSize: 20.0.sp,\n                        fontWeight: FontWeight.bold,\n                        color: AppColors.blackColor,\n                      ),\n                    ),\n                  ],\n                ),\n\n                RequestMeetingSpeakerWidgets.requestMeettingFields(\n                  context: context,\n                  title: \"Title\",\n                  controller: _titleController,\n                  validate: \"Please Enter Meeting Title\",\n                ),\n\n                RequestMeetingSpeakerWidgets.requestMeettingFields(\n                  context: context,\n                  title: \"Description\",\n                  controller: _descriptionController,\n                  validate: \"Please Enter Meeting Description\",\n                  maxlines: 5,\n                ),\n\n                RequestMeetingSpeakerWidgets.requestMeettingFields(\n                  context: context,\n                  title: \"Link or Location\",\n                  controller: _linkOrLocationController,\n                  validate: \"Please Enter Meeting Link or Location\",\n                ),\n\n                RequestMeetingSpeakerWidgets.requestMeettingFields(\n                  context: context,\n                  title: \"Date\",\n                  controller: _dateController,\n                  validate: \"Please Enter Meeting Date\",\n                  showDateIcon: true,\n                  readOnly: true,\n                ),\n\n                RequestMeetingSpeakerWidgets.requestMeettingFields(\n                  context: context,\n                  title: \"Time\",\n                  controller: _timeController,\n                  validate: \"Please Enter Meeting Time\",\n                  showTimeIcon: true,\n                  readOnly: true,\n                ),\n\n                ElevatedButton(\n                  onPressed: () async {\n                    if (_formKey.currentState!.validate()) {\n                      await context\n                          .read(ProfileProviders.commonApis)\n                          .sendRequestMeeting(\n                            context: context,\n                            title: _titleController.text,\n                            descriptipn: _descriptionController.text,\n                            location: _linkOrLocationController.text,\n                            date: _dateController.text,\n                            time: _timeController.text,\n                          );\n                    }\n                  },\n\n                  style: ElevatedButton.styleFrom(\n                    foregroundColor: Colors.white,\n                    backgroundColor: AppColors.midLevelGreenColor,\n                    textStyle: TextStyle(\n                      fontSize: 16.0.sp,\n                      fontWeight: FontWeight.bold,\n                    ),\n                    minimumSize: Size(398.0.w, 46.0.h),\n                  ),\n                  child: const Text(\"Send\"),\n                ),\n              ],\n            ),\n          ),\n        ),\n      ),\n    );\n  }\n}\n", "baseTimestamp": 1755088380053, "deltas": [{"timestamp": 1755088384644, "changes": [{"type": "MODIFY", "lineNumber": 49, "content": "                    const <PERSON><PERSON><PERSON><PERSON>(),", "oldContent": "                    "}]}, {"timestamp": 1755088388806, "changes": [{"type": "INSERT", "lineNumber": 50, "content": "                    S"}]}, {"timestamp": 1755088392917, "changes": [{"type": "MODIFY", "lineNumber": 50, "content": "                    SizedBox(width: 10.0.w),", "oldContent": "                    S"}]}]}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/screens/home_screens/home_categories_screens/attendance_screens/request_meeting_screen_attendance.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/screens/home_screens/home_categories_screens/attendance_screens/request_meeting_screen_attendance.dart", "baseContent": "import 'package:afa_app/app_config/api_providers/profile_providers.dart';\nimport 'package:afa_app/app_config/app_colors.dart';\nimport 'package:afa_app/models/home_screens_models/attendance_screens_models/attendance_screen_model.dart';\nimport 'package:afa_app/widgets/home_screens_widgets.dart/attendance_screens_widgets/request_meeting_attendance_widgets.dart';\nimport 'package:flutter/material.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:intl/intl.dart';\nimport 'package:riverpod_context/riverpod_context.dart';\n\nclass RequestMeetingScreenAttendance extends StatefulWidget {\n  const RequestMeetingScreenAttendance({super.key, this.attendanceDetails});\n  final AttendanceScreenModel? attendanceDetails;\n\n  @override\n  State<RequestMeetingScreenAttendance> createState() =>\n      _RequestMeetingScreenAttendanceState();\n}\n\nclass _RequestMeetingScreenAttendanceState\n    extends State<RequestMeetingScreenAttendance> {\n  final TextEditingController _titleController = TextEditingController();\n  final TextEditingController _descriptionController = TextEditingController();\n  final TextEditingController _linkOrLocationController =\n      TextEditingController();\n  final TextEditingController _dateController = TextEditingController();\n  final TextEditingController _timeController = TextEditingController();\n  final _formKey = GlobalKey<FormState>();\n  @override\n  void dispose() {\n    _titleController.dispose();\n    _descriptionController.dispose();\n    _linkOrLocationController.dispose();\n    _dateController.dispose();\n    _timeController.dispose();\n    super.dispose();\n  }\n\n  @override\n  Widget build(BuildContext context) {\n    // final RequestMeetingScreenAttendance args =\n    //     ModalRoute.of(context)!.settings.arguments\n    //         as RequestMeetingScreenAttendance;\n\n    return Scaffold(\n      body: SafeArea(\n        child: SingleChildScrollView(\n          physics: const BouncingScrollPhysics(),\n          padding: EdgeInsets.all(15.0.h),\n          child: Form(\n            key: _formKey,\n            child: Column(\n              spacing: 25.0.h,\n              crossAxisAlignment: CrossAxisAlignment.start,\n              children: [\n                Row(\n                  children: [\n                    const BackButton(),\n                    SizedBox(width: 10.0.w),\n                    Text(\n                      \"Request Meeting\",\n                      style: TextStyle(\n                        fontSize: 20.0.sp,\n                        fontWeight: FontWeight.bold,\n                        color: AppColors.blackColor,\n                      ),\n                    ),\n                  ],\n                ),\n\n                RequestMeetingWidgetsAttendance.requestMeettingFields(\n                  context: context,\n                  title: \"Title\",\n                  controller: _titleController,\n                  validate: \"Please Enter Meeting Title\",\n                ),\n\n                RequestMeetingWidgetsAttendance.requestMeettingFields(\n                  context: context,\n                  title: \"Description\",\n                  controller: _descriptionController,\n                  validate: \"Please Enter Meeting Description\",\n                  maxlines: 5,\n                ),\n\n                RequestMeetingWidgetsAttendance.requestMeettingFields(\n                  context: context,\n                  title: \"Link or Location\",\n                  controller: _linkOrLocationController,\n                  validate: \"Please Enter Meeting Link or Location\",\n                ),\n\n                RequestMeetingWidgetsAttendance.requestMeettingFields(\n                  context: context,\n                  title: \"Date\",\n                  controller: _dateController,\n                  validate: \"Please Enter Meeting Date\",\n                  showDateIcon: true,\n                  readOnly: true,\n                  onPress: () async {\n                    await showDatePicker(\n                      context: context,\n                      firstDate: DateTime.now(),\n                      lastDate: DateTime(2050),\n                    ).then((value) {\n                      if (value != null) {\n                        _dateController.text = DateFormat(\n                          'yyyy-MM-dd',\n                        ).format(value);\n                      }\n                    });\n                  },\n                ),\n\n                RequestMeetingWidgetsAttendance.requestMeettingFields(\n                  context: context,\n                  title: \"Time\",\n                  controller: _timeController,\n                  validate: \"Please Enter Meeting Time\",\n                  showTimeIcon: true,\n                  readOnly: true,\n                  onPress: () async {\n                    await showTimePicker(\n                      context: context,\n                      initialTime: TimeOfDay.now(),\n                    ).then((value) {\n                      if (value != null) {\n                        if (!context.mounted) return;\n                        _timeController.text = value.format(context);\n                      }\n                    });\n                  },\n                ),\n\n                ElevatedButton(\n                  onPressed: () async {\n                    if (_formKey.currentState!.validate()) {\n                      await context\n                          .read(ProfileProviders.commonApis)\n                          .sendRequestMeeting(\n                            context: context,\n                            title: _titleController.text,\n                            descriptipn: _descriptionController.text,\n                            location: _linkOrLocationController.text,\n                            date: _dateController.text,\n                            time: _timeController.text,\n                          );\n                    }\n                  },\n\n                  style: ElevatedButton.styleFrom(\n                    foregroundColor: Colors.white,\n                    backgroundColor: AppColors.midLevelGreenColor,\n                    textStyle: TextStyle(\n                      fontSize: 16.0.sp,\n                      fontWeight: FontWeight.bold,\n                    ),\n                    minimumSize: Size(398.0.w, 46.0.h),\n                  ),\n                  child: const Text(\"Send\"),\n                ),\n              ],\n            ),\n          ),\n        ),\n      ),\n    );\n  }\n}\n", "baseTimestamp": 1755088411784}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/widgets/athuntication_screens_widgets/login_screen_widgets.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/widgets/athuntication_screens_widgets/login_screen_widgets.dart", "baseContent": "import 'package:afa_app/app_config/app_colors.dart';\nimport 'package:flutter/material.dart';\nimport 'package:flutter_riverpod/flutter_riverpod.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:riverpod_context/riverpod_context.dart';\n\nclass LoginScreenWidgets {\n  static Widget loginScreenFileds({\n    required TextEditingController controller,\n    required TextInputType type,\n    required TextInputAction action,\n    required String validate,\n    required String hint,\n    bool showPasswordIcon = false,\n  }) {\n    final StateProvider<bool> showPassword = StateProvider((ref) => true);\n    return Consumer(\n      builder: (context, watch, child) => TextFormField(\n        controller: controller,\n        autovalidateMode: AutovalidateMode.onUserInteraction,\ninitialValue:,\n        validator: (value) => value!.isEmpty ? validate : null,\n        keyboardType: type,\n        obscureText: showPasswordIcon ? watch.watch(showPassword) : false,\n        style: TextStyle(fontSize: 12.0.sp),\n        decoration: InputDecoration(\n          hintText: hint,\n          hintStyle: TextStyle(fontSize: 12.0.sp),\n          errorStyle: TextStyle(fontSize: 14.0.sp),\n          focusedBorder: OutlineInputBorder(\n            borderRadius: BorderRadius.all(Radius.circular(8.0.r)),\n            borderSide: const BorderSide(color: AppColors.greyColor),\n          ),\n          border: OutlineInputBorder(\n            borderRadius: BorderRadius.all(Radius.circular(8.0.r)),\n            borderSide: const BorderSide(color: AppColors.greyColor),\n          ),\n          errorBorder: OutlineInputBorder(\n            borderRadius: BorderRadius.all(Radius.circular(8.0.r)),\n            borderSide: const BorderSide(color: AppColors.greyColor),\n          ),\n          enabledBorder: OutlineInputBorder(\n            borderRadius: BorderRadius.all(Radius.circular(8.0.r)),\n            borderSide: const BorderSide(color: AppColors.greyColor),\n          ),\n          disabledBorder: OutlineInputBorder(\n            borderRadius: BorderRadius.all(Radius.circular(8.0.r)),\n            borderSide: const BorderSide(color: AppColors.greyColor),\n          ),\n          suffixIcon: Visibility(\n            visible: showPasswordIcon,\n            child: InkWell(\n              onTap: () {\n                context.read(showPassword.notifier).state = !context\n                    .read(showPassword.notifier)\n                    .state;\n              },\n              child: watch.watch(showPassword)\n                  ? const Icon(Icons.visibility_off)\n                  : const Icon(Icons.visibility),\n            ),\n          ),\n          suffixIconColor: AppColors.midLevelGreenColor,\n        ),\n      ),\n    );\n  }\n}\n", "baseTimestamp": 1755088761951, "deltas": [{"timestamp": 1755088787210, "changes": [{"type": "DELETE", "lineNumber": 20, "oldContent": "initialValue:,"}]}]}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/screens/athuntication_screens/login_screen.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/screens/athuntication_screens/login_screen.dart", "baseContent": "import 'package:afa_app/app_config/api_keys.dart';\nimport 'package:afa_app/app_config/api_providers/athuntication_providers.dart';\nimport 'package:afa_app/app_config/app_colors.dart';\nimport 'package:afa_app/app_config/app_images.dart';\nimport 'package:afa_app/app_config/common_components.dart';\nimport 'package:afa_app/app_config/routes.dart';\nimport 'package:afa_app/widgets/athuntication_screens_widgets/login_screen_widgets.dart';\nimport 'package:firebase_messaging/firebase_messaging.dart';\nimport 'package:flutter/material.dart';\nimport 'package:flutter_riverpod/flutter_riverpod.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:riverpod_context/riverpod_context.dart';\n\nclass LoginScreen extends StatefulWidget {\n  const LoginScreen({super.key});\n\n  @override\n  State<LoginScreen> createState() => LoginScreenState();\n}\n\nclass LoginScreenState extends State<LoginScreen> {\n  final TextEditingController _emailController = TextEditingController();\n  final TextEditingController _passwordController = TextEditingController();\n  final _formkey = GlobalKey<FormState>();\n\n  @override\n  void initState() {\n    Future.delayed(Duration.zero, () async {\n      if (await CommonComponents.getSavedData(ApiKeys.userToken) != null) {\n        if (!mounted) return;\n\n        Navigator.pushNamedAndRemoveUntil(\n          context,\n          PATHS.mainScreen,\n          (route) => false,\n        );\n      }\n    });\n    super.initState();\n  }\n\n  @override\n  void dispose() {\n    _emailController.dispose();\n    _passwordController.dispose();\n    super.dispose();\n  }\n\n  @override\n  Widget build(BuildContext context) {\n    // final StateProvider<bool> checkRememberMe = StateProvider((ref) => false);\n    return Scaffold(\n      body: SafeArea(\n        child: SingleChildScrollView(\n          physics: const BouncingScrollPhysics(),\n          padding: EdgeInsets.all(20.0.h),\n          child: Form(\n            key: _formkey,\n            child: Column(\n              crossAxisAlignment: CrossAxisAlignment.start,\n\n              children: [\n                Center(\n                  child: CommonComponents.imageAssetWithCache(\n                    context: context,\n                    image: AppImages.loginlogoImage,\n                    height: 93.0.h,\n                    width: 247.0.w,\n                    fit: BoxFit.contain,\n                  ),\n                ),\n                SizedBox(height: 15.0.h),\n                Text(\n                  \"Welcome Back\",\n                  style: TextStyle(\n                    fontSize: 22.0.sp,\n                    color: AppColors.blackColor,\n                    fontWeight: FontWeight.bold,\n                  ),\n                ),\n\n                Text(\n                  \"Get Ready to Dive Back into AFA Conference\",\n                  style: TextStyle(\n                    fontSize: 18.0.sp,\n                    color: AppColors.greyColor,\n                    fontWeight: FontWeight.bold,\n                  ),\n                ),\n                SizedBox(height: 20.0.h),\n                Text(\n                  \"Email\",\n                  style: TextStyle(\n                    fontSize: 14.0.sp,\n                    color: AppColors.textColor,\n                  ),\n                ),\n                SizedBox(height: 5.0.h),\n                LoginScreenWidgets.loginScreenFileds(\n                  controller: _emailController,\n                  type: TextInputType.emailAddress,\n                  action: TextInputAction.next,\n                  hint: \"Enter\",\n                  validate: \"Plaese Enter Your Email Address\",\n                ),\n                SizedBox(height: 10.0.h),\n                Text(\n                  \"Password\",\n                  style: TextStyle(\n                    fontSize: 14.0.sp,\n                    color: AppColors.textColor,\n                  ),\n                ),\n                SizedBox(height: 5.0.h),\n                LoginScreenWidgets.loginScreenFileds(\n                  controller: _passwordController,\n                  type: TextInputType.visiblePassword,\n                  action: TextInputAction.done,\n                  hint: \"Enter\",\n                  validate: \"Plaese Enter Your Password\",\n                  showPasswordIcon: true,\n                ),\n                SizedBox(height: 10.0.h),\n                Row(\n                  mainAxisAlignment: MainAxisAlignment.spaceBetween,\n                  children: [\n                    // Consumer(\n                    //   builder: (context, watch, child) => Row(\n                    //     children: [\n                    //       Checkbox(\n                    //         value: watch.watch(checkRememberMe),\n                    //         checkColor: Colors.white,\n                    //         activeColor: AppColors.midLevelGreenColor,\n                    //         onChanged: (value) {\n                    //           context.read(checkRememberMe.notifier).state =\n                    //               !context.read(checkRememberMe.notifier).state;\n                    //         },\n                    //       ),\n\n                    //       Text(\n                    //         \"Remember Me\",\n                    //         style: TextStyle(\n                    //           fontSize: 16.0.sp,\n                    //           color: AppColors.textColor,\n                    //           fontWeight: FontWeight.bold,\n                    //         ),\n                    //       ),\n                    //     ],\n                    //   ),\n                    // ),\n                    TextButton(\n                      onPressed: () {\n                        Navigator.pushNamed(context, PATHS.resetPasswordScreen);\n                      },\n                      style: TextButton.styleFrom(\n                        foregroundColor: AppColors.midLevelGreenColor,\n                        textStyle: TextStyle(\n                          fontSize: 16.0.sp,\n                          fontWeight: FontWeight.bold,\n                        ),\n                      ),\n                      child: const Text(\"Forgot Password?\"),\n                    ),\n                  ],\n                ),\n                SizedBox(height: 15.0.h),\n                Consumer(\n                  builder: (context, watch, child) => ElevatedButton(\n                    onPressed: () async {\n                      if (_formkey.currentState!.validate()) {\n                        FirebaseMessaging.instance.getToken().then((\n                          value,\n                        ) async {\n                          if (!context.mounted) return;\n                          await context\n                              .read(AthunticationProviders.loginProvidersApis)\n                              .userLogin(\n                                context: context,\n                                email: _emailController.text,\n                                password: _passwordController.text,\n                                fcmToken: value!,\n                                // userSelectedRememberMe: watch.watch(\n                                //   checkRememberMe,\n                                // ),\n                              );\n                        });\n                      }\n                    },\n                    style: ElevatedButton.styleFrom(\n                      foregroundColor: Colors.white,\n                      backgroundColor: AppColors.midLevelGreenColor,\n                      textStyle: TextStyle(\n                        fontSize: 16.0.sp,\n                        fontWeight: FontWeight.bold,\n                      ),\n                      minimumSize: Size(398.0.w, 46.0.h),\n                      shape: RoundedRectangleBorder(\n                        borderRadius: BorderRadius.all(Radius.circular(32.0.r)),\n                      ),\n                    ),\n                    child: const Text(\"Login\"),\n                  ),\n                ),\n              ],\n            ),\n          ),\n        ),\n      ),\n    );\n  }\n}\n", "baseTimestamp": 1755088781925, "deltas": [{"timestamp": 1755088798902, "changes": [{"type": "INSERT", "lineNumber": 8, "content": "import 'package:flutter/foundation.dart';"}, {"type": "MODIFY", "lineNumber": 22, "content": "  final TextEditingController _emailController = TextEditingController(text: kDebugMode ? );", "oldContent": "  final TextEditingController _emailController = TextEditingController();"}]}, {"timestamp": 1755088818806, "changes": [{"type": "DELETE", "lineNumber": 22, "oldContent": "  final TextEditingController _emailController = TextEditingController();"}, {"type": "MODIFY", "lineNumber": 22, "content": "  final TextEditingController _emailController = TextEditingController(text: kDebugMode ? \"admin\": '');", "oldContent": "  final TextEditingController _emailController = TextEditingController(text: kDebugMode ? );"}, {"type": "INSERT", "lineNumber": 23, "content": "  final TextEditingController _passwordController = TextEditingController(text: kDebugMode ? );"}]}, {"timestamp": 1755088828910, "changes": [{"type": "DELETE", "lineNumber": 22, "oldContent": "  final TextEditingController _emailController = TextEditingController(text: kDebugMode ? \"admin\": '');"}, {"type": "MODIFY", "lineNumber": 22, "content": "  final TextEditingController _emailController = TextEditingController(", "oldContent": "  final TextEditingController _emailController = TextEditingController(text: kDebugMode ? );"}, {"type": "INSERT", "lineNumber": 23, "content": "    text: kDebugMode ? \"admin\" : '',"}, {"type": "INSERT", "lineNumber": 24, "content": "  );"}, {"type": "INSERT", "lineNumber": 25, "content": "  final TextEditingController _passwordController = TextEditingController("}, {"type": "INSERT", "lineNumber": 26, "content": "    text: kDebugMode ? \"PenwPZc29AuHF%a%q7Xn&Bzm\" : '',"}, {"type": "INSERT", "lineNumber": 27, "content": "  );"}]}, {"timestamp": 1755088900896, "changes": [{"type": "MODIFY", "lineNumber": 23, "content": "    text: kDebugMode ? \"admin\" : '',", "oldContent": "  final TextEditingController _emailController = TextEditingController(text: kDebugMode ? );"}, {"type": "DELETE", "lineNumber": 25, "oldContent": "  final _formkey = GlobalKey<FormState>();"}, {"type": "DELETE", "lineNumber": 27, "oldContent": ""}, {"type": "DELETE", "lineNumber": 29, "oldContent": "  @override"}, {"type": "INSERT", "lineNumber": 28, "content": "  final _formkey = GlobalKey<FormState>();"}, {"type": "INSERT", "lineNumber": 29, "content": ""}, {"type": "INSERT", "lineNumber": 30, "content": "  @override"}, {"type": "INSERT", "lineNumber": 175, "content": "                        if (kDebugMode) {"}, {"type": "INSERT", "lineNumber": 176, "content": "                          await context"}, {"type": "INSERT", "lineNumber": 177, "content": "                              .read(AthunticationProviders.loginProvidersApis)"}, {"type": "INSERT", "lineNumber": 178, "content": "                              .userLogin("}, {"type": "INSERT", "lineNumber": 179, "content": "                                context: context,"}, {"type": "INSERT", "lineNumber": 180, "content": "                                email: _emailController.text,"}, {"type": "INSERT", "lineNumber": 181, "content": "                                password: _passwordController.text,"}, {"type": "INSERT", "lineNumber": 182, "content": "                                fcmToken: '',"}, {"type": "INSERT", "lineNumber": 183, "content": "                                // userSelectedRememberMe: watch.watch("}, {"type": "INSERT", "lineNumber": 184, "content": "                                //   checkRememberMe,"}, {"type": "INSERT", "lineNumber": 185, "content": "                                // ),"}, {"type": "INSERT", "lineNumber": 186, "content": "                              );"}, {"type": "INSERT", "lineNumber": 187, "content": "                          return;"}, {"type": "INSERT", "lineNumber": 188, "content": "                        }"}]}]}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/providers/chat_providers_apis/inbox_providers_apis.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/providers/chat_providers_apis/inbox_providers_apis.dart", "baseContent": "import 'dart:developer';\n\nimport 'package:afa_app/app_config/api_keys.dart';\nimport 'package:afa_app/app_config/api_providers/profile_providers.dart';\nimport 'package:afa_app/app_config/api_requests.dart';\nimport 'package:afa_app/app_config/common_components.dart';\nimport 'package:afa_app/models/chat_models/inbox_model.dart';\nimport 'package:flutter/material.dart';\nimport 'package:riverpod_context/riverpod_context.dart';\n\nclass InboxProvidersApis extends ChangeNotifier {\n  int? threadID;\n\n  void setThreadID(int id) {\n    threadID = id;\n    notifyListeners();\n  }\n\n  Future<List<InboxModel>> getChatInbox({required BuildContext context}) async {\n    final List<InboxModel> inboxList = [];\n    final String myToken = await CommonComponents.getSavedData(\n      ApiKeys.userToken,\n    );\n\n    final int myUserId = await CommonComponents.getSavedData(ApiKeys.userID);\n\n    if (!context.mounted) return [];\n    final Map<String, dynamic>? dataList = await ApiRequests.getApiRequest(\n      context: context,\n      baseUrl: ApiKeys.baseUrl,\n      apiUrl: \"wp-json/better-messages/v1/threads\",\n      headers: {\"Authorization\": \"Bearer $myToken\"},\n    );\n\n    log('FULL_URLL ${ApiKeys.baseUrl}wp-json/better-messages/v1/threads');\n    if (dataList != null) {\n      for (final data in dataList['threads']) {\n        if (data['participantsCount'] == 2) {\n          if (!context.mounted) return [];\n\n          for (final participants in data['participants']) {\n            if (participants != myUserId) {\n              await context\n                  .read(ProfileProviders.profileSettingsProvidersApis)\n                  .getUserData(\n                    context: context,\n                    getMyProfileData: false,\n                    userID: participants,\n                  )\n                  .then((value) {\n                    inboxList.add(\n                      InboxModel.fromJson(\n                        data,\n                        userName: value.userName!,\n                        userImage: value.userImage!,\n                      ),\n                    );\n                  });\n            }\n          }\n        }\n      }\n    } else {\n      debugPrint(\"ERROR WITH getChatInbox FUNCTION\");\n    }\n\n    return inboxList;\n  }\n}\n", "baseTimestamp": 1755089561325, "deltas": [{"timestamp": 1755092355733, "changes": [{"type": "INSERT", "lineNumber": 36, "content": "      // Collect all participant IDs first"}, {"type": "INSERT", "lineNumber": 37, "content": "      final List<int> participantIds = [];"}, {"type": "INSERT", "lineNumber": 38, "content": "      final List<Map<String, dynamic>> threadsData = [];"}, {"type": "INSERT", "lineNumber": 39, "content": ""}, {"type": "DELETE", "lineNumber": 38, "oldContent": "          if (!context.mounted) return [];"}, {"type": "DELETE", "lineNumber": 39, "oldContent": ""}, {"type": "DELETE", "lineNumber": 42, "oldContent": "              await context"}, {"type": "DELETE", "lineNumber": 43, "oldContent": "                  .read(ProfileProviders.profileSettingsProvidersApis)"}, {"type": "DELETE", "lineNumber": 44, "oldContent": "                  .getUserData("}, {"type": "DELETE", "lineNumber": 45, "oldContent": "                    context: context,"}, {"type": "DELETE", "lineNumber": 46, "oldContent": "                    getMyProfileData: false,"}, {"type": "DELETE", "lineNumber": 47, "oldContent": "                    userID: participants,"}, {"type": "DELETE", "lineNumber": 48, "oldContent": "                  )"}, {"type": "DELETE", "lineNumber": 49, "oldContent": "                  .then((value) {"}, {"type": "DELETE", "lineNumber": 50, "oldContent": "                    inboxList.add("}, {"type": "DELETE", "lineNumber": 51, "oldContent": "                      InboxModel.fromJson("}, {"type": "DELETE", "lineNumber": 52, "oldContent": "                        data,"}, {"type": "DELETE", "lineNumber": 53, "oldContent": "                        userName: value.userName!,"}, {"type": "DELETE", "lineNumber": 54, "oldContent": "                        userImage: value.userImage!,"}, {"type": "DELETE", "lineNumber": 55, "oldContent": "                      ),"}, {"type": "DELETE", "lineNumber": 56, "oldContent": "                    );"}, {"type": "DELETE", "lineNumber": 57, "oldContent": "                  });"}, {"type": "INSERT", "lineNumber": 44, "content": "              participantIds.add(participants);"}, {"type": "INSERT", "lineNumber": 45, "content": "              threadsData.add(data);"}, {"type": "INSERT", "lineNumber": 46, "content": "              break; // Only one other participant in 2-person chat"}, {"type": "INSERT", "lineNumber": 51, "content": ""}, {"type": "INSERT", "lineNumber": 52, "content": "      if (!context.mounted) return [];"}, {"type": "INSERT", "lineNumber": 53, "content": ""}, {"type": "INSERT", "lineNumber": 54, "content": "      // Make single bulk API call to get all user data"}, {"type": "INSERT", "lineNumber": 55, "content": "      if (participantIds.isNotEmpty) {"}, {"type": "INSERT", "lineNumber": 56, "content": "        final Map<int, dynamic> usersData = await context"}, {"type": "INSERT", "lineNumber": 57, "content": "            .read(ProfileProviders.profileSettingsProvidersApis)"}, {"type": "INSERT", "lineNumber": 58, "content": "            .getBulkUserData(context: context, userIds: participantIds);"}, {"type": "INSERT", "lineNumber": 59, "content": ""}, {"type": "INSERT", "lineNumber": 60, "content": "        // Create inbox items using the bulk user data"}, {"type": "INSERT", "lineNumber": 61, "content": "        for (int i = 0; i < threadsData.length; i++) {"}, {"type": "INSERT", "lineNumber": 62, "content": "          final data = threadsData[i];"}, {"type": "INSERT", "lineNumber": 63, "content": "          final participantId = participantIds[i];"}, {"type": "INSERT", "lineNumber": 64, "content": "          final userData = usersData[participantId];"}, {"type": "INSERT", "lineNumber": 65, "content": ""}, {"type": "INSERT", "lineNumber": 66, "content": "          if (userData != null) {"}, {"type": "INSERT", "lineNumber": 67, "content": "            inboxList.add("}, {"type": "INSERT", "lineNumber": 68, "content": "              InboxModel.fromJson("}, {"type": "INSERT", "lineNumber": 69, "content": "                data,"}, {"type": "INSERT", "lineNumber": 70, "content": "                userName: userData.userName ?? 'Unknown User',"}, {"type": "INSERT", "lineNumber": 71, "content": "                userImage: userData.userImage ?? '',"}, {"type": "INSERT", "lineNumber": 72, "content": "              ),"}, {"type": "INSERT", "lineNumber": 73, "content": "            );"}, {"type": "INSERT", "lineNumber": 74, "content": "          }"}, {"type": "INSERT", "lineNumber": 75, "content": "        }"}, {"type": "INSERT", "lineNumber": 76, "content": "      }"}]}]}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/providers/home_categories_screens_providers_apis/profile_screens_providers_apis/profile_settings_providers_apis.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/providers/home_categories_screens_providers_apis/profile_screens_providers_apis/profile_settings_providers_apis.dart", "baseContent": "import 'dart:async';\nimport 'dart:convert';\nimport 'dart:developer';\nimport 'dart:io';\nimport 'package:afa_app/app_config/api_keys.dart';\nimport 'package:afa_app/app_config/api_requests.dart';\nimport 'package:afa_app/app_config/common_components.dart';\nimport 'package:afa_app/models/profile_models/profile_user_data_model.dart';\nimport 'package:afa_app/models/profile_models/user_countries_model.dart';\nimport 'package:flutter/material.dart';\nimport 'package:http/http.dart' as http;\n\nclass ProfileSettingsProvidersApis extends ChangeNotifier {\n  // Key? profileKey;\n\n  // void rebuildProfile() {\n  //   profileKey = ValueKey(Random().nextInt(1000));\n  //   notifyListeners();\n  // }\n\n  UserCountriesModel? selectedCountry;\n\n  void setSelectedCountry(UserCountriesModel country) {\n    selectedCountry = country;\n    notifyListeners();\n  }\n\n  Future<ProfileUserDataModel> getUserData({\n    required BuildContext context,\n    required bool getMyProfileData,\n    int? userID,\n  }) async {\n    ProfileUserDataModel? userData;\n\n    final int myUserId = await CommonComponents.getSavedData(ApiKeys.userID);\n    if (!context.mounted) return userData!;\n    final Map<String, dynamic>? data = await ApiRequests.getApiRequest(\n      context: context,\n      baseUrl: ApiKeys.baseUrl,\n      apiUrl: getMyProfileData\n          ? \"wp-json/buddypress-ext/v1/profile/$myUserId\"\n          : \"wp-json/buddypress-ext/v1/profile/$userID\",\n      headers: {},\n    );\n\n    log('DATAA ${data}');\n\n    if (data != null) {\n      userData = ProfileUserDataModel.fromJson(data);\n    } else {\n      debugPrint(\"ERROR WITH userData FUNCTION\");\n    }\n\n    return userData!;\n  }\n\n  Future<List<UserCountriesModel>> getAllCountries({\n    required BuildContext context,\n  }) async {\n    final List<UserCountriesModel> countriesList = [];\n\n    final Map<String, dynamic>? dataList = await ApiRequests.getApiRequest(\n      context: context,\n      baseUrl: ApiKeys.baseUrl,\n      apiUrl: \"wp-json/buddypress-ext/v1/countries\",\n      headers: {},\n    );\n\n    if (dataList != null) {\n      for (final data in dataList['countries']) {\n        countriesList.add(UserCountriesModel.fromJson(data));\n      }\n    } else {\n      debugPrint(\"ERROR WITH getAllCountries FUNCTION\");\n    }\n    return countriesList;\n  }\n\n  Future userEditProfile({\n    required BuildContext context,\n    required File? userImage,\n    required String userName,\n    required String phoneNumber,\n    required String userEmail,\n    required String position,\n    required String bio,\n    required String country,\n  }) async {\n    try {\n      final int userID = await CommonComponents.getSavedData(ApiKeys.userID);\n      final String myToken = await CommonComponents.getSavedData(\n        ApiKeys.userToken,\n      );\n\n      if (await CommonComponents.checkConnectivity()) {\n        if (context.mounted) {\n          CommonComponents.loading(context);\n        } else {\n          return;\n        }\n        final String url =\n            \"${ApiKeys.baseUrl}wp-json/buddypress-ext/v1/profile/$userID/form\";\n\n        final http.MultipartRequest response = http.MultipartRequest(\n          \"POST\",\n          Uri.parse(url),\n        );\n\n        response.headers[\"Content-Type\"] = \"application/json\";\n        response.headers[\"Authorization\"] = \"Bearer $myToken\";\n\n        if (userImage != null) {\n          response.files.add(\n            await http.MultipartFile.fromPath('profile_image', userImage.path),\n          );\n        }\n\n        response.files.addAll([\n          http.MultipartFile.fromString('name', userName),\n          http.MultipartFile.fromString('email', userEmail),\n          http.MultipartFile.fromString('phone', phoneNumber),\n          http.MultipartFile.fromString('position', position),\n          http.MultipartFile.fromString('bio', bio),\n          // http.MultipartFile.fromString('country',country),\n        ]);\n\n        response.send().then((results) async {\n          await http.Response.fromStream(results).then((response) async {\n            if (response.statusCode == 200) {\n              final decoddedData = jsonDecode(response.body);\n              if (!context.mounted) return;\n              Navigator.pop(context);\n              await CommonComponents.saveData(\n                key: ApiKeys.userName,\n                value: decoddedData['user']['name'],\n              );\n\n              await CommonComponents.saveData(\n                key: ApiKeys.userImage,\n                value: decoddedData['user']['profile_image'],\n              );\n\n              if (context.mounted) {\n                CommonComponents.showCustomizedSnackBar(\n                  context: context,\n                  title: \"Your data has been Updated.\",\n                );\n              }\n            } else {\n              if (!context.mounted) return;\n              CommonComponents.showCustomizedSnackBar(\n                context: context,\n                title: \"Server Error Request\",\n              );\n              Navigator.pop(context);\n            }\n          });\n        });\n      } else {\n        if (context.mounted) {\n          Navigator.pop(context);\n          await CommonComponents.notConnectionAlert(context);\n        } else {\n          return;\n        }\n      }\n    } on TimeoutException catch (error) {\n      if (context.mounted) {\n        Navigator.pop(context);\n      }\n      debugPrint(\"Time Out Exception is::=>$error\");\n      if (context.mounted) {\n        await CommonComponents.timeOutExceptionAlert(context);\n      }\n    } on SocketException catch (error) {\n      if (context.mounted) {\n        Navigator.pop(context);\n      }\n      debugPrint(\"Socket Exception is::=>$error\");\n      if (context.mounted) {\n        await CommonComponents.socketExceptionAlert(context);\n      }\n    } catch (error) {\n      if (context.mounted) {\n        Navigator.pop(context);\n      }\n      debugPrint(\"General Exception is::=>$error\");\n    }\n  }\n}\n", "baseTimestamp": 1755090195363, "deltas": [{"timestamp": 1755090239723, "changes": [{"type": "INSERT", "lineNumber": 45, "content": "    log('FULL_URLL $')"}]}, {"timestamp": 1755090244771, "changes": [{"type": "MODIFY", "lineNumber": 45, "content": "    log('FULL_URLL $", "oldContent": "    log('FULL_URLL $')"}]}, {"timestamp": 1755090249915, "changes": [{"type": "MODIFY", "lineNumber": 45, "content": "    log(", "oldContent": "    log('FULL_URLL $"}, {"type": "INSERT", "lineNumber": 46, "content": "      'FULL_URLrrrL ${ApiKeys.baseUrl}wp-json/buddypress-ext/v1/profile/$myUserId',"}, {"type": "INSERT", "lineNumber": 47, "content": "    );"}]}, {"timestamp": 1755092313561, "changes": [{"type": "MODIFY", "lineNumber": 48, "content": "    log('DATAA ${data}');", "oldContent": "    log('DATAA ${data}');"}, {"type": "INSERT", "lineNumber": 59, "content": "  /// Get multiple users data in a single API call for better performance"}, {"type": "INSERT", "lineNumber": 60, "content": "  Future<Map<int, ProfileUserDataModel>> getBulkUserData({"}, {"type": "INSERT", "lineNumber": 61, "content": "    required BuildContext context,"}, {"type": "INSERT", "lineNumber": 62, "content": "    required List<int> userIds,"}, {"type": "INSERT", "lineNumber": 63, "content": "  }) async {"}, {"type": "INSERT", "lineNumber": 64, "content": "    final Map<int, ProfileUserDataModel> usersData = {};"}, {"type": "INSERT", "lineNumber": 65, "content": ""}, {"type": "INSERT", "lineNumber": 66, "content": "    if (userIds.isEmpty) return usersData;"}, {"type": "INSERT", "lineNumber": 67, "content": ""}, {"type": "INSERT", "lineNumber": 68, "content": "    if (!context.mounted) return usersData;"}, {"type": "INSERT", "lineNumber": 69, "content": ""}, {"type": "INSERT", "lineNumber": 70, "content": "    final Map<String, dynamic>? data = await ApiRequests.postApiRequest("}, {"type": "INSERT", "lineNumber": 71, "content": "      context: context,"}, {"type": "INSERT", "lineNumber": 72, "content": "      baseUrl: ApiKeys.baseUrl,"}, {"type": "INSERT", "lineNumber": 73, "content": "      apiUrl: \"wp-json/buddypress-ext/v1/attendancee/by-ids\","}, {"type": "INSERT", "lineNumber": 74, "content": "      headers: {\"Content-Type\": \"application/json\"},"}, {"type": "INSERT", "lineNumber": 75, "content": "      body: json.encode({\"ids\": userIds}),"}, {"type": "INSERT", "lineNumber": 76, "content": "      showLoadingWidget: false,"}, {"type": "INSERT", "lineNumber": 77, "content": "    );"}, {"type": "INSERT", "lineNumber": 78, "content": ""}, {"type": "INSERT", "lineNumber": 79, "content": "    log("}, {"type": "INSERT", "lineNumber": 80, "content": "      'BULK_USER_DATA_URL: ${ApiKeys.baseUrl}wp-json/buddypress-ext/v1/attendancee/by-ids',"}, {"type": "INSERT", "lineNumber": 81, "content": "    );"}, {"type": "INSERT", "lineNumber": 82, "content": "    log('BULK_USER_DATA_RESPONSE: $data');"}, {"type": "INSERT", "lineNumber": 83, "content": ""}, {"type": "INSERT", "lineNumber": 84, "content": "    if (data != null && data['items'] != null) {"}, {"type": "INSERT", "lineNumber": 85, "content": "      for (final item in data['items']) {"}, {"type": "INSERT", "lineNumber": 86, "content": "        final userData = ProfileUserDataModel("}, {"type": "INSERT", "lineNumber": 87, "content": "          userID: item['id'],"}, {"type": "INSERT", "lineNumber": 88, "content": "          userName: item['name'],"}, {"type": "INSERT", "lineNumber": 89, "content": "          userImage: item['avatar'],"}, {"type": "INSERT", "lineNumber": 90, "content": "          userEmail: item['email'],"}, {"type": "INSERT", "lineNumber": 91, "content": "          country: null, // Not provided in bulk API"}, {"type": "INSERT", "lineNumber": 92, "content": "          position: null, // Not provided in bulk API"}, {"type": "INSERT", "lineNumber": 93, "content": "          bio: null, // Not provided in bulk API"}, {"type": "INSERT", "lineNumber": 94, "content": "          phoneNumber: null, // Not provided in bulk API"}, {"type": "INSERT", "lineNumber": 95, "content": "        );"}, {"type": "INSERT", "lineNumber": 96, "content": "        usersData[item['id']] = userData;"}, {"type": "INSERT", "lineNumber": 97, "content": "      }"}, {"type": "INSERT", "lineNumber": 98, "content": "    } else {"}, {"type": "INSERT", "lineNumber": 99, "content": "      debugPrint(\"ERROR WITH getBulkUserData FUNCTION\");"}, {"type": "INSERT", "lineNumber": 100, "content": "    }"}, {"type": "INSERT", "lineNumber": 101, "content": ""}, {"type": "INSERT", "lineNumber": 102, "content": "    return usersData;"}, {"type": "INSERT", "lineNumber": 103, "content": "  }"}, {"type": "INSERT", "lineNumber": 104, "content": ""}]}]}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/models/chat_models/chat_screen_model.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/models/chat_models/chat_screen_model.dart", "baseContent": "class ChatScreenModel {\n  ChatScreenModel.fromJson(\n    Map<String, dynamic> jsonData, {\n    required String name,\n    required String image,this is messages response\n\n    {\n    \"threads\": [\n    {\n    \"thread_id\": 89,\n    \"isHidden\": 0,\n    \"isDeleted\": 0,\n    \"type\": \"thread\",\n    \"title\": \"test hi\",\n    \"subject\": \"test hi\",\n    \"image\": \"\",\n    \"lastTime\": 17550904511930,\n    \"participants\": [\n    50,\n    1\n    ],\n    \"participantsCount\": 2,\n    \"moderators\": [],\n    \"url\": \"\",\n    \"meta\": {\n    \"allowInvite\": false\n    },\n    \"isPinned\": 0,\n    \"isMuted\": false,\n    \"permissions\": {\n    \"isModerator\": true,\n    \"deleteAllowed\": true,\n    \"canDeleteOwnMessages\": false,\n    \"canDeleteAllMessages\": true,\n    \"canEditOwnMessages\": false,\n    \"canFavorite\": true,\n    \"canMuteThread\": true,\n    \"canEraseThread\": true,\n    \"canClearThread\": true,\n    \"canInvite\": true,\n    \"canLeave\": false,\n    \"canUpload\": true,\n    \"canVideoCall\": false,\n    \"canAudioCall\": false,\n    \"canMaximize\": true,\n    \"canPinMessages\": true,\n    \"canMinimize\": false,\n    \"canReply\": true,\n    \"canReplyMsg\": []\n    },\n    \"mentions\": [],\n    \"unread\": 0\n    }\n    ],\n    \"users\": [\n    {\n    \"id\": \"1\",\n    \"user_id\": 1,\n    \"name\": \"Mohamed Nashaat\",\n    \"avatar\": \"https://arabfertilizer.org/technical-conference/wp-content/uploads/sites/3/2025/07/scaled_1000180490-1-150x150.jpg\",\n    \"url\": \"https://arabfertilizer.org/technical-conference/members/admin/\",\n    \"verified\": 0,\n    \"lastActive\": \"2025-08-13 12:38:38\",\n    \"isFriend\": 0,\n    \"canVideo\": 0,\n    \"canAudio\": 0\n    },\n    {\n    \"id\": \"50\",\n    \"user_id\": 50,\n    \"name\": \"Arab Fertilizer Association\",\n    \"avatar\": \"https://arabfertilizer.org/technical-conference/wp-content/uploads/sites/3/2025/07/cropped-afa-fav-150x150.webp\",\n    \"url\": \"https://arabfertilizer.org/technical-conference/members/37th-technical-conference/\",\n    \"verified\": 0,\n    \"lastActive\": \"2025-07-22 15:01:55\",\n    \"isFriend\": 0,\n    \"canVideo\": 0,\n    \"canAudio\": 0\n    }\n    ],\n    \"messages\": [\n    {\n    \"thread_id\": 89,\n    \"sender_id\": 1,\n    \"message\": \"new\",\n    \"created_at\": 17550904511930,\n    \"updated_at\": 17550904511930,\n    \"temp_id\": \"\",\n    \"message_id\": 170,\n    \"meta\": {},\n    \"favorited\": 0\n    },\n    {\n    \"thread_id\": 89,\n    \"sender_id\": 1,\n    \"message\": \"test\",\n    \"created_at\": 17550904437899,\n    \"updated_at\": 17550904437899,\n    \"temp_id\": \"\",\n    \"message_id\": 169,\n    \"meta\": {},\n    \"favorited\": 0\n    },\n    {\n    \"thread_id\": 89,\n    \"sender_id\": 1,\n    \"message\": \"test\",\n    \"created_at\": 17545224434645,\n    \"updated_at\": 17545224434645,\n    \"temp_id\": \"\",\n    \"message_id\": 154,\n    \"meta\": {},\n    \"favorited\": 0\n    },\n    {\n    \"thread_id\": 89,\n    \"sender_id\": 1,\n    \"message\": \"test hii\",\n    \"created_at\": 17545219330203,\n    \"updated_at\": 17545219330203,\n    \"temp_id\": \"\",\n    \"message_id\": 153,\n    \"meta\": {},\n    \"favorited\": 0\n    },\n    {\n    \"thread_id\": 89,\n    \"sender_id\": 1,\n    \"message\": \"test hi\",\n    \"created_at\": 17545218915511,\n    \"updated_at\": 17545218915511,\n    \"temp_id\": \"\",\n    \"message_id\": 152,\n    \"meta\": {},\n    \"favorited\": 0\n    },\n    {\n    \"thread_id\": 89,\n    \"sender_id\": 1,\n    \"message\": \"tessst\",\n    \"created_at\": 17545217821696,\n    \"updated_at\": 17545217821696,\n    \"temp_id\": \"\",\n    \"message_id\": 151,\n    \"meta\": {},\n    \"favorited\": 0\n    },\n    {\n    \"thread_id\": 89,\n    \"sender_id\": 1,\n    \"message\": \"test ❤️😘\",\n    \"created_at\": 17545216724049,\n    \"updated_at\": 17545216724049,\n    \"temp_id\": \"\",\n    \"message_id\": 150,\n    \"meta\": {},\n    \"favorited\": 0\n    },\n    {\n    \"thread_id\": 89,\n    \"sender_id\": 1,\n    \"message\": \"test\",\n    \"created_at\": 17545215553107,\n    \"updated_at\": 17545215553107,\n    \"temp_id\": \"\",\n    \"message_id\": 149,\n    \"meta\": {},\n    \"favorited\": 0\n    },\n    {\n    \"thread_id\": 89,\n    \"sender_id\": 1,\n    \"message\": \"hyf\",\n    \"created_at\": 17545214766814,\n    \"updated_at\": 17545214766814,\n    \"temp_id\": \"\",\n    \"message_id\": 148,\n    \"meta\": {},\n    \"favorited\": 0\n    },\n    {\n    \"thread_id\": 89,\n    \"sender_id\": 1,\n    \"message\": \"hyf\",\n    \"created_at\": 17545214753444,\n    \"updated_at\": 17545214753444,\n    \"temp_id\": \"\",\n    \"message_id\": 147,\n    \"meta\": {},\n    \"favorited\": 0\n    },\n    {\n    \"thread_id\": 89,\n    \"sender_id\": 1,\n    \"message\": \"👋😊\",\n    \"created_at\": 17545213914665,\n    \"updated_at\": 17545213914665,\n    \"temp_id\": \"\",\n    \"message_id\": 146,\n    \"meta\": {},\n    \"favorited\": 0\n    },\n    {\n    \"thread_id\": 89,\n    \"sender_id\": 1,\n    \"message\": \"👋\",\n    \"created_at\": 17545213898322,\n    \"updated_at\": 17545213898322,\n    \"temp_id\": \"\",\n    \"message_id\": 145,\n    \"meta\": {},\n    \"favorited\": 0\n    },\n    {\n    \"thread_id\": 89,\n    \"sender_id\": 50,\n    \"message\": \"heyyyy\",\n    \"created_at\": 17545212782731,\n    \"updated_at\": 17545212782731,\n    \"temp_id\": \"\",\n    \"message_id\": 144,\n    \"meta\": {},\n    \"favorited\": 0\n    },\n    {\n    \"thread_id\": 89,\n    \"sender_id\": 1,\n    \"message\": \"heyy\",\n    \"created_at\": 17545212513561,\n    \"updated_at\": 17545212513561,\n    \"temp_id\": \"\",\n    \"message_id\": 143,\n    \"meta\": {},\n    \"favorited\": 0\n    },\n    {\n    \"thread_id\": 89,\n    \"sender_id\": 1,\n    \"message\": \"heyy\",\n    \"created_at\": 17545212494108,\n    \"updated_at\": 17545212494108,\n    \"temp_id\": \"\",\n    \"message_id\": 142,\n    \"meta\": {},\n    \"favorited\": 0\n    },\n    {\n    \"thread_id\": 89,\n    \"sender_id\": 50,\n    \"message\": \"Hey! This is a test message2.\",\n    \"created_at\": 17544950740106,\n    \"updated_at\": 17544950740106,\n    \"temp_id\": \"\",\n    \"message_id\": 133,\n    \"meta\": {},\n    \"favorited\": 0\n    },\n    {\n    \"thread_id\": 89,\n    \"sender_id\": 1,\n    \"message\": \"test hi\",\n    \"created_at\": 17544946458296,\n    \"updated_at\": 17544946458296,\n    \"temp_id\": \"\",\n    \"message_id\": 132,\n    \"meta\": {},\n    \"favorited\": 0\n    }\n    ],\n    \"serverTime\": 17550907507656\n    }\n\n    and this is it's model @/lib/models/chat_models/chat_screen_model.dart \n\n    need now to show the time of sent messages in @/lib/screens/home_screens/chat_screen.dart\n\n    it's getting in api like \"created_at\": 17550904511930,\n\n    but i need to show with good ui like 12 Aug. 12:30 PM\n\n    like that you handle\n\n    if (participants != myUserId) {\n    await context\n        .read(ProfileProviders.profileSettingsProvidersApis)\n        .getUserData(\n    context: context,\n    getMyProfileData: false,\n    userID: participants,\n    )\n        .then((value) {\n    inboxList.add(\n    InboxModel.fromJson(\n    data,\n    userName: value.userName!,\n    userImage: value.userImage!,\n    ),\n    );\n    });\n    } @/lib/providers/chat_providers_apis/inbox_providers_apis.dart\n\n  another thing this take so many time because it's sent like 12 api request \n\n  we make new api request that take multi ids and get the response\n\n  url is https://arabfertilizer.org/technical-conference//wp-json/buddypress-ext/v1/attendancee/by-ids\n\n  and body is ids and take ids like [1, 12, 7, 42]\n\n  {\n  \"ids\": [1, 12]\n  }\n\n  and it's response is \n\n  {\n  \"items\": [\n  {\n  \"id\": 1,\n  \"name\": \"Mohamed Nashaat\",\n  \"avatar\": \"https://arabfertilizer.org/technical-conference/wp-content/uploads/sites/3/2025/07/scaled_1000180490-1-scaled.jpg\",\n  \"email\": \"<EMAIL>\",\n  \"type\": \"deletages\"\n  },\n  {\n  \"id\": 12,\n  \"name\": \"mfcru\",\n  \"avatar\": \"https://secure.gravatar.com/avatar/aca95db55556e012885b43e8b897cd45cf19fab322f5a7aaabfe18503a2abf78?s=96&d=mm&r=g\",\n  \"email\": \"<EMAIL>\",\n  \"type\": \"\"\n  }\n  ],\n  \"not_found\": []\n  }\n\n  and inject the user name and avatar instead of this every time\n\n  await context\n      .read(ProfileProviders.profileSettingsProvidersApis)\n      .getUserData(\n  context: context,\n  getMyProfileData: false,\n  userID: participants,\n  )\n      .then((value) {\n  inboxList.add(\n  InboxModel.fromJson(\n  data,\n  userName: value.userName!,\n  userImage: value.userImage!,\n  ),\n  );\n  });\n  }) {\n    senderID = jsonData['sender_id'];\n    message = jsonData['message'];\n    userName = name;\n    userImage = image;\n  }\n\n  ChatScreenModel({this.message, this.userName, this.userImage, this.senderID});\n  int? senderID;\n  String? message, userName, userImage;\n}\n", "baseTimestamp": 1755091855024, "deltas": [{"timestamp": 1755091858612, "changes": [{"type": "DELETE", "lineNumber": 4, "oldContent": "    required String image,this is messages response"}, {"type": "DELETE", "lineNumber": 5, "oldContent": ""}, {"type": "DELETE", "lineNumber": 6, "oldContent": "    {"}, {"type": "DELETE", "lineNumber": 7, "oldContent": "    \"threads\": ["}, {"type": "DELETE", "lineNumber": 8, "oldContent": "    {"}, {"type": "DELETE", "lineNumber": 9, "oldContent": "    \"thread_id\": 89,"}, {"type": "DELETE", "lineNumber": 10, "oldContent": "    \"isHidden\": 0,"}, {"type": "DELETE", "lineNumber": 11, "oldContent": "    \"isDeleted\": 0,"}, {"type": "DELETE", "lineNumber": 12, "oldContent": "    \"type\": \"thread\","}, {"type": "DELETE", "lineNumber": 13, "oldContent": "    \"title\": \"test hi\","}, {"type": "DELETE", "lineNumber": 14, "oldContent": "    \"subject\": \"test hi\","}, {"type": "DELETE", "lineNumber": 15, "oldContent": "    \"image\": \"\","}, {"type": "DELETE", "lineNumber": 16, "oldContent": "    \"lastTime\": 17550904511930,"}, {"type": "DELETE", "lineNumber": 17, "oldContent": "    \"participants\": ["}, {"type": "DELETE", "lineNumber": 18, "oldContent": "    50,"}, {"type": "DELETE", "lineNumber": 19, "oldContent": "    1"}, {"type": "DELETE", "lineNumber": 20, "oldContent": "    ],"}, {"type": "DELETE", "lineNumber": 21, "oldContent": "    \"participantsCount\": 2,"}, {"type": "DELETE", "lineNumber": 22, "oldContent": "    \"moderators\": [],"}, {"type": "DELETE", "lineNumber": 23, "oldContent": "    \"url\": \"\","}, {"type": "DELETE", "lineNumber": 24, "oldContent": "    \"meta\": {"}, {"type": "DELETE", "lineNumber": 25, "oldContent": "    \"allowInvite\": false"}, {"type": "DELETE", "lineNumber": 26, "oldContent": "    },"}, {"type": "DELETE", "lineNumber": 27, "oldContent": "    \"isPinned\": 0,"}, {"type": "DELETE", "lineNumber": 28, "oldContent": "    \"isMuted\": false,"}, {"type": "DELETE", "lineNumber": 29, "oldContent": "    \"permissions\": {"}, {"type": "DELETE", "lineNumber": 30, "oldContent": "    \"isModerator\": true,"}, {"type": "DELETE", "lineNumber": 31, "oldContent": "    \"deleteAllowed\": true,"}, {"type": "DELETE", "lineNumber": 32, "oldContent": "    \"canDeleteOwnMessages\": false,"}, {"type": "DELETE", "lineNumber": 33, "oldContent": "    \"canDeleteAllMessages\": true,"}, {"type": "DELETE", "lineNumber": 34, "oldContent": "    \"canEditOwnMessages\": false,"}, {"type": "DELETE", "lineNumber": 35, "oldContent": "    \"canFavorite\": true,"}, {"type": "DELETE", "lineNumber": 36, "oldContent": "    \"canMuteThread\": true,"}, {"type": "DELETE", "lineNumber": 37, "oldContent": "    \"canEraseThread\": true,"}, {"type": "DELETE", "lineNumber": 38, "oldContent": "    \"canClearThread\": true,"}, {"type": "DELETE", "lineNumber": 39, "oldContent": "    \"canInvite\": true,"}, {"type": "DELETE", "lineNumber": 40, "oldContent": "    \"canLeave\": false,"}, {"type": "DELETE", "lineNumber": 41, "oldContent": "    \"canUpload\": true,"}, {"type": "DELETE", "lineNumber": 42, "oldContent": "    \"canVideoCall\": false,"}, {"type": "DELETE", "lineNumber": 43, "oldContent": "    \"canAudioCall\": false,"}, {"type": "DELETE", "lineNumber": 44, "oldContent": "    \"canMaximize\": true,"}, {"type": "DELETE", "lineNumber": 45, "oldContent": "    \"canPinMessages\": true,"}, {"type": "DELETE", "lineNumber": 46, "oldContent": "    \"canMinimize\": false,"}, {"type": "DELETE", "lineNumber": 47, "oldContent": "    \"canReply\": true,"}, {"type": "DELETE", "lineNumber": 48, "oldContent": "    \"canReplyMsg\": []"}, {"type": "DELETE", "lineNumber": 49, "oldContent": "    },"}, {"type": "DELETE", "lineNumber": 50, "oldContent": "    \"mentions\": [],"}, {"type": "DELETE", "lineNumber": 51, "oldContent": "    \"unread\": 0"}, {"type": "DELETE", "lineNumber": 52, "oldContent": "    }"}, {"type": "DELETE", "lineNumber": 53, "oldContent": "    ],"}, {"type": "DELETE", "lineNumber": 54, "oldContent": "    \"users\": ["}, {"type": "DELETE", "lineNumber": 55, "oldContent": "    {"}, {"type": "DELETE", "lineNumber": 56, "oldContent": "    \"id\": \"1\","}, {"type": "DELETE", "lineNumber": 57, "oldContent": "    \"user_id\": 1,"}, {"type": "DELETE", "lineNumber": 58, "oldContent": "    \"name\": \"<PERSON>\","}, {"type": "DELETE", "lineNumber": 59, "oldContent": "    \"avatar\": \"https://arabfertilizer.org/technical-conference/wp-content/uploads/sites/3/2025/07/scaled_1000180490-1-150x150.jpg\","}, {"type": "DELETE", "lineNumber": 60, "oldContent": "    \"url\": \"https://arabfertilizer.org/technical-conference/members/admin/\","}, {"type": "DELETE", "lineNumber": 61, "oldContent": "    \"verified\": 0,"}, {"type": "DELETE", "lineNumber": 62, "oldContent": "    \"lastActive\": \"2025-08-13 12:38:38\","}, {"type": "DELETE", "lineNumber": 63, "oldContent": "    \"isFriend\": 0,"}, {"type": "DELETE", "lineNumber": 64, "oldContent": "    \"canVideo\": 0,"}, {"type": "DELETE", "lineNumber": 65, "oldContent": "    \"canAudio\": 0"}, {"type": "DELETE", "lineNumber": 66, "oldContent": "    },"}, {"type": "DELETE", "lineNumber": 67, "oldContent": "    {"}, {"type": "DELETE", "lineNumber": 68, "oldContent": "    \"id\": \"50\","}, {"type": "DELETE", "lineNumber": 69, "oldContent": "    \"user_id\": 50,"}, {"type": "DELETE", "lineNumber": 70, "oldContent": "    \"name\": \"Arab Fertilizer Association\","}, {"type": "DELETE", "lineNumber": 71, "oldContent": "    \"avatar\": \"https://arabfertilizer.org/technical-conference/wp-content/uploads/sites/3/2025/07/cropped-afa-fav-150x150.webp\","}, {"type": "DELETE", "lineNumber": 72, "oldContent": "    \"url\": \"https://arabfertilizer.org/technical-conference/members/37th-technical-conference/\","}, {"type": "DELETE", "lineNumber": 73, "oldContent": "    \"verified\": 0,"}, {"type": "DELETE", "lineNumber": 74, "oldContent": "    \"lastActive\": \"2025-07-22 15:01:55\","}, {"type": "DELETE", "lineNumber": 75, "oldContent": "    \"isFriend\": 0,"}, {"type": "DELETE", "lineNumber": 76, "oldContent": "    \"canVideo\": 0,"}, {"type": "DELETE", "lineNumber": 77, "oldContent": "    \"canAudio\": 0"}, {"type": "DELETE", "lineNumber": 78, "oldContent": "    }"}, {"type": "DELETE", "lineNumber": 79, "oldContent": "    ],"}, {"type": "DELETE", "lineNumber": 80, "oldContent": "    \"messages\": ["}, {"type": "DELETE", "lineNumber": 81, "oldContent": "    {"}, {"type": "DELETE", "lineNumber": 82, "oldContent": "    \"thread_id\": 89,"}, {"type": "DELETE", "lineNumber": 83, "oldContent": "    \"sender_id\": 1,"}, {"type": "DELETE", "lineNumber": 84, "oldContent": "    \"message\": \"new\","}, {"type": "DELETE", "lineNumber": 85, "oldContent": "    \"created_at\": 17550904511930,"}, {"type": "DELETE", "lineNumber": 86, "oldContent": "    \"updated_at\": 17550904511930,"}, {"type": "DELETE", "lineNumber": 87, "oldContent": "    \"temp_id\": \"\","}, {"type": "DELETE", "lineNumber": 88, "oldContent": "    \"message_id\": 170,"}, {"type": "DELETE", "lineNumber": 89, "oldContent": "    \"meta\": {},"}, {"type": "DELETE", "lineNumber": 90, "oldContent": "    \"favorited\": 0"}, {"type": "DELETE", "lineNumber": 91, "oldContent": "    },"}, {"type": "DELETE", "lineNumber": 92, "oldContent": "    {"}, {"type": "DELETE", "lineNumber": 93, "oldContent": "    \"thread_id\": 89,"}, {"type": "DELETE", "lineNumber": 94, "oldContent": "    \"sender_id\": 1,"}, {"type": "DELETE", "lineNumber": 95, "oldContent": "    \"message\": \"test\","}, {"type": "DELETE", "lineNumber": 96, "oldContent": "    \"created_at\": 17550904437899,"}, {"type": "DELETE", "lineNumber": 97, "oldContent": "    \"updated_at\": 17550904437899,"}, {"type": "DELETE", "lineNumber": 98, "oldContent": "    \"temp_id\": \"\","}, {"type": "DELETE", "lineNumber": 99, "oldContent": "    \"message_id\": 169,"}, {"type": "DELETE", "lineNumber": 100, "oldContent": "    \"meta\": {},"}, {"type": "DELETE", "lineNumber": 101, "oldContent": "    \"favorited\": 0"}, {"type": "DELETE", "lineNumber": 102, "oldContent": "    },"}, {"type": "DELETE", "lineNumber": 103, "oldContent": "    {"}, {"type": "DELETE", "lineNumber": 104, "oldContent": "    \"thread_id\": 89,"}, {"type": "DELETE", "lineNumber": 105, "oldContent": "    \"sender_id\": 1,"}, {"type": "DELETE", "lineNumber": 106, "oldContent": "    \"message\": \"test\","}, {"type": "DELETE", "lineNumber": 107, "oldContent": "    \"created_at\": 17545224434645,"}, {"type": "DELETE", "lineNumber": 108, "oldContent": "    \"updated_at\": 17545224434645,"}, {"type": "DELETE", "lineNumber": 109, "oldContent": "    \"temp_id\": \"\","}, {"type": "DELETE", "lineNumber": 110, "oldContent": "    \"message_id\": 154,"}, {"type": "DELETE", "lineNumber": 111, "oldContent": "    \"meta\": {},"}, {"type": "DELETE", "lineNumber": 112, "oldContent": "    \"favorited\": 0"}, {"type": "DELETE", "lineNumber": 113, "oldContent": "    },"}, {"type": "DELETE", "lineNumber": 114, "oldContent": "    {"}, {"type": "DELETE", "lineNumber": 115, "oldContent": "    \"thread_id\": 89,"}, {"type": "DELETE", "lineNumber": 116, "oldContent": "    \"sender_id\": 1,"}, {"type": "DELETE", "lineNumber": 117, "oldContent": "    \"message\": \"test hii\","}, {"type": "DELETE", "lineNumber": 118, "oldContent": "    \"created_at\": 17545219330203,"}, {"type": "DELETE", "lineNumber": 119, "oldContent": "    \"updated_at\": 17545219330203,"}, {"type": "DELETE", "lineNumber": 120, "oldContent": "    \"temp_id\": \"\","}, {"type": "DELETE", "lineNumber": 121, "oldContent": "    \"message_id\": 153,"}, {"type": "DELETE", "lineNumber": 122, "oldContent": "    \"meta\": {},"}, {"type": "DELETE", "lineNumber": 123, "oldContent": "    \"favorited\": 0"}, {"type": "DELETE", "lineNumber": 124, "oldContent": "    },"}, {"type": "DELETE", "lineNumber": 125, "oldContent": "    {"}, {"type": "DELETE", "lineNumber": 126, "oldContent": "    \"thread_id\": 89,"}, {"type": "DELETE", "lineNumber": 127, "oldContent": "    \"sender_id\": 1,"}, {"type": "DELETE", "lineNumber": 128, "oldContent": "    \"message\": \"test hi\","}, {"type": "DELETE", "lineNumber": 129, "oldContent": "    \"created_at\": 17545218915511,"}, {"type": "DELETE", "lineNumber": 130, "oldContent": "    \"updated_at\": 17545218915511,"}, {"type": "DELETE", "lineNumber": 131, "oldContent": "    \"temp_id\": \"\","}, {"type": "DELETE", "lineNumber": 132, "oldContent": "    \"message_id\": 152,"}, {"type": "DELETE", "lineNumber": 133, "oldContent": "    \"meta\": {},"}, {"type": "DELETE", "lineNumber": 134, "oldContent": "    \"favorited\": 0"}, {"type": "DELETE", "lineNumber": 135, "oldContent": "    },"}, {"type": "DELETE", "lineNumber": 136, "oldContent": "    {"}, {"type": "DELETE", "lineNumber": 137, "oldContent": "    \"thread_id\": 89,"}, {"type": "DELETE", "lineNumber": 138, "oldContent": "    \"sender_id\": 1,"}, {"type": "DELETE", "lineNumber": 139, "oldContent": "    \"message\": \"tessst\","}, {"type": "DELETE", "lineNumber": 140, "oldContent": "    \"created_at\": 17545217821696,"}, {"type": "DELETE", "lineNumber": 141, "oldContent": "    \"updated_at\": 17545217821696,"}, {"type": "DELETE", "lineNumber": 142, "oldContent": "    \"temp_id\": \"\","}, {"type": "DELETE", "lineNumber": 143, "oldContent": "    \"message_id\": 151,"}, {"type": "DELETE", "lineNumber": 144, "oldContent": "    \"meta\": {},"}, {"type": "DELETE", "lineNumber": 145, "oldContent": "    \"favorited\": 0"}, {"type": "DELETE", "lineNumber": 146, "oldContent": "    },"}, {"type": "DELETE", "lineNumber": 147, "oldContent": "    {"}, {"type": "DELETE", "lineNumber": 148, "oldContent": "    \"thread_id\": 89,"}, {"type": "DELETE", "lineNumber": 149, "oldContent": "    \"sender_id\": 1,"}, {"type": "DELETE", "lineNumber": 150, "oldContent": "    \"message\": \"test ❤️😘\","}, {"type": "DELETE", "lineNumber": 151, "oldContent": "    \"created_at\": 17545216724049,"}, {"type": "DELETE", "lineNumber": 152, "oldContent": "    \"updated_at\": 17545216724049,"}, {"type": "DELETE", "lineNumber": 153, "oldContent": "    \"temp_id\": \"\","}, {"type": "DELETE", "lineNumber": 154, "oldContent": "    \"message_id\": 150,"}, {"type": "DELETE", "lineNumber": 155, "oldContent": "    \"meta\": {},"}, {"type": "DELETE", "lineNumber": 156, "oldContent": "    \"favorited\": 0"}, {"type": "DELETE", "lineNumber": 157, "oldContent": "    },"}, {"type": "DELETE", "lineNumber": 158, "oldContent": "    {"}, {"type": "DELETE", "lineNumber": 159, "oldContent": "    \"thread_id\": 89,"}, {"type": "DELETE", "lineNumber": 160, "oldContent": "    \"sender_id\": 1,"}, {"type": "DELETE", "lineNumber": 161, "oldContent": "    \"message\": \"test\","}, {"type": "DELETE", "lineNumber": 162, "oldContent": "    \"created_at\": 17545215553107,"}, {"type": "DELETE", "lineNumber": 163, "oldContent": "    \"updated_at\": 17545215553107,"}, {"type": "DELETE", "lineNumber": 164, "oldContent": "    \"temp_id\": \"\","}, {"type": "DELETE", "lineNumber": 165, "oldContent": "    \"message_id\": 149,"}, {"type": "DELETE", "lineNumber": 166, "oldContent": "    \"meta\": {},"}, {"type": "DELETE", "lineNumber": 167, "oldContent": "    \"favorited\": 0"}, {"type": "DELETE", "lineNumber": 168, "oldContent": "    },"}, {"type": "DELETE", "lineNumber": 169, "oldContent": "    {"}, {"type": "DELETE", "lineNumber": 170, "oldContent": "    \"thread_id\": 89,"}, {"type": "DELETE", "lineNumber": 171, "oldContent": "    \"sender_id\": 1,"}, {"type": "DELETE", "lineNumber": 172, "oldContent": "    \"message\": \"hyf\","}, {"type": "DELETE", "lineNumber": 173, "oldContent": "    \"created_at\": 17545214766814,"}, {"type": "DELETE", "lineNumber": 174, "oldContent": "    \"updated_at\": 17545214766814,"}, {"type": "DELETE", "lineNumber": 175, "oldContent": "    \"temp_id\": \"\","}, {"type": "DELETE", "lineNumber": 176, "oldContent": "    \"message_id\": 148,"}, {"type": "DELETE", "lineNumber": 177, "oldContent": "    \"meta\": {},"}, {"type": "DELETE", "lineNumber": 178, "oldContent": "    \"favorited\": 0"}, {"type": "DELETE", "lineNumber": 179, "oldContent": "    },"}, {"type": "DELETE", "lineNumber": 180, "oldContent": "    {"}, {"type": "DELETE", "lineNumber": 181, "oldContent": "    \"thread_id\": 89,"}, {"type": "DELETE", "lineNumber": 182, "oldContent": "    \"sender_id\": 1,"}, {"type": "DELETE", "lineNumber": 183, "oldContent": "    \"message\": \"hyf\","}, {"type": "DELETE", "lineNumber": 184, "oldContent": "    \"created_at\": 17545214753444,"}, {"type": "DELETE", "lineNumber": 185, "oldContent": "    \"updated_at\": 17545214753444,"}, {"type": "DELETE", "lineNumber": 186, "oldContent": "    \"temp_id\": \"\","}, {"type": "DELETE", "lineNumber": 187, "oldContent": "    \"message_id\": 147,"}, {"type": "DELETE", "lineNumber": 188, "oldContent": "    \"meta\": {},"}, {"type": "DELETE", "lineNumber": 189, "oldContent": "    \"favorited\": 0"}, {"type": "DELETE", "lineNumber": 190, "oldContent": "    },"}, {"type": "DELETE", "lineNumber": 191, "oldContent": "    {"}, {"type": "DELETE", "lineNumber": 192, "oldContent": "    \"thread_id\": 89,"}, {"type": "DELETE", "lineNumber": 193, "oldContent": "    \"sender_id\": 1,"}, {"type": "DELETE", "lineNumber": 194, "oldContent": "    \"message\": \"👋😊\","}, {"type": "DELETE", "lineNumber": 195, "oldContent": "    \"created_at\": 17545213914665,"}, {"type": "DELETE", "lineNumber": 196, "oldContent": "    \"updated_at\": 17545213914665,"}, {"type": "DELETE", "lineNumber": 197, "oldContent": "    \"temp_id\": \"\","}, {"type": "DELETE", "lineNumber": 198, "oldContent": "    \"message_id\": 146,"}, {"type": "DELETE", "lineNumber": 199, "oldContent": "    \"meta\": {},"}, {"type": "DELETE", "lineNumber": 200, "oldContent": "    \"favorited\": 0"}, {"type": "DELETE", "lineNumber": 201, "oldContent": "    },"}, {"type": "DELETE", "lineNumber": 202, "oldContent": "    {"}, {"type": "DELETE", "lineNumber": 203, "oldContent": "    \"thread_id\": 89,"}, {"type": "DELETE", "lineNumber": 204, "oldContent": "    \"sender_id\": 1,"}, {"type": "DELETE", "lineNumber": 205, "oldContent": "    \"message\": \"👋\","}, {"type": "DELETE", "lineNumber": 206, "oldContent": "    \"created_at\": 17545213898322,"}, {"type": "DELETE", "lineNumber": 207, "oldContent": "    \"updated_at\": 17545213898322,"}, {"type": "DELETE", "lineNumber": 208, "oldContent": "    \"temp_id\": \"\","}, {"type": "DELETE", "lineNumber": 209, "oldContent": "    \"message_id\": 145,"}, {"type": "DELETE", "lineNumber": 210, "oldContent": "    \"meta\": {},"}, {"type": "DELETE", "lineNumber": 211, "oldContent": "    \"favorited\": 0"}, {"type": "DELETE", "lineNumber": 212, "oldContent": "    },"}, {"type": "DELETE", "lineNumber": 213, "oldContent": "    {"}, {"type": "DELETE", "lineNumber": 214, "oldContent": "    \"thread_id\": 89,"}, {"type": "DELETE", "lineNumber": 215, "oldContent": "    \"sender_id\": 50,"}, {"type": "DELETE", "lineNumber": 216, "oldContent": "    \"message\": \"heyyyy\","}, {"type": "DELETE", "lineNumber": 217, "oldContent": "    \"created_at\": 17545212782731,"}, {"type": "DELETE", "lineNumber": 218, "oldContent": "    \"updated_at\": 17545212782731,"}, {"type": "DELETE", "lineNumber": 219, "oldContent": "    \"temp_id\": \"\","}, {"type": "DELETE", "lineNumber": 220, "oldContent": "    \"message_id\": 144,"}, {"type": "DELETE", "lineNumber": 221, "oldContent": "    \"meta\": {},"}, {"type": "DELETE", "lineNumber": 222, "oldContent": "    \"favorited\": 0"}, {"type": "DELETE", "lineNumber": 223, "oldContent": "    },"}, {"type": "DELETE", "lineNumber": 224, "oldContent": "    {"}, {"type": "DELETE", "lineNumber": 225, "oldContent": "    \"thread_id\": 89,"}, {"type": "DELETE", "lineNumber": 226, "oldContent": "    \"sender_id\": 1,"}, {"type": "DELETE", "lineNumber": 227, "oldContent": "    \"message\": \"heyy\","}, {"type": "DELETE", "lineNumber": 228, "oldContent": "    \"created_at\": 17545212513561,"}, {"type": "DELETE", "lineNumber": 229, "oldContent": "    \"updated_at\": 17545212513561,"}, {"type": "DELETE", "lineNumber": 230, "oldContent": "    \"temp_id\": \"\","}, {"type": "DELETE", "lineNumber": 231, "oldContent": "    \"message_id\": 143,"}, {"type": "DELETE", "lineNumber": 232, "oldContent": "    \"meta\": {},"}, {"type": "DELETE", "lineNumber": 233, "oldContent": "    \"favorited\": 0"}, {"type": "DELETE", "lineNumber": 234, "oldContent": "    },"}, {"type": "DELETE", "lineNumber": 235, "oldContent": "    {"}, {"type": "DELETE", "lineNumber": 236, "oldContent": "    \"thread_id\": 89,"}, {"type": "DELETE", "lineNumber": 237, "oldContent": "    \"sender_id\": 1,"}, {"type": "DELETE", "lineNumber": 238, "oldContent": "    \"message\": \"heyy\","}, {"type": "DELETE", "lineNumber": 239, "oldContent": "    \"created_at\": 17545212494108,"}, {"type": "DELETE", "lineNumber": 240, "oldContent": "    \"updated_at\": 17545212494108,"}, {"type": "DELETE", "lineNumber": 241, "oldContent": "    \"temp_id\": \"\","}, {"type": "DELETE", "lineNumber": 242, "oldContent": "    \"message_id\": 142,"}, {"type": "DELETE", "lineNumber": 243, "oldContent": "    \"meta\": {},"}, {"type": "DELETE", "lineNumber": 244, "oldContent": "    \"favorited\": 0"}, {"type": "DELETE", "lineNumber": 245, "oldContent": "    },"}, {"type": "DELETE", "lineNumber": 246, "oldContent": "    {"}, {"type": "DELETE", "lineNumber": 247, "oldContent": "    \"thread_id\": 89,"}, {"type": "DELETE", "lineNumber": 248, "oldContent": "    \"sender_id\": 50,"}, {"type": "DELETE", "lineNumber": 249, "oldContent": "    \"message\": \"Hey! This is a test message2.\","}, {"type": "DELETE", "lineNumber": 250, "oldContent": "    \"created_at\": 17544950740106,"}, {"type": "DELETE", "lineNumber": 251, "oldContent": "    \"updated_at\": 17544950740106,"}, {"type": "DELETE", "lineNumber": 252, "oldContent": "    \"temp_id\": \"\","}, {"type": "DELETE", "lineNumber": 253, "oldContent": "    \"message_id\": 133,"}, {"type": "DELETE", "lineNumber": 254, "oldContent": "    \"meta\": {},"}, {"type": "DELETE", "lineNumber": 255, "oldContent": "    \"favorited\": 0"}, {"type": "DELETE", "lineNumber": 256, "oldContent": "    },"}, {"type": "DELETE", "lineNumber": 257, "oldContent": "    {"}, {"type": "DELETE", "lineNumber": 258, "oldContent": "    \"thread_id\": 89,"}, {"type": "DELETE", "lineNumber": 259, "oldContent": "    \"sender_id\": 1,"}, {"type": "DELETE", "lineNumber": 260, "oldContent": "    \"message\": \"test hi\","}, {"type": "DELETE", "lineNumber": 261, "oldContent": "    \"created_at\": 17544946458296,"}, {"type": "DELETE", "lineNumber": 262, "oldContent": "    \"updated_at\": 17544946458296,"}, {"type": "DELETE", "lineNumber": 263, "oldContent": "    \"temp_id\": \"\","}, {"type": "DELETE", "lineNumber": 264, "oldContent": "    \"message_id\": 132,"}, {"type": "DELETE", "lineNumber": 265, "oldContent": "    \"meta\": {},"}, {"type": "DELETE", "lineNumber": 266, "oldContent": "    \"favorited\": 0"}, {"type": "DELETE", "lineNumber": 267, "oldContent": "    }"}, {"type": "DELETE", "lineNumber": 268, "oldContent": "    ],"}, {"type": "DELETE", "lineNumber": 269, "oldContent": "    \"serverTime\": 17550907507656"}, {"type": "DELETE", "lineNumber": 270, "oldContent": "    }"}, {"type": "DELETE", "lineNumber": 271, "oldContent": ""}, {"type": "DELETE", "lineNumber": 272, "oldContent": "    and this is it's model @/lib/models/chat_models/chat_screen_model.dart "}, {"type": "DELETE", "lineNumber": 273, "oldContent": ""}, {"type": "DELETE", "lineNumber": 274, "oldContent": "    need now to show the time of sent messages in @/lib/screens/home_screens/chat_screen.dart"}, {"type": "DELETE", "lineNumber": 275, "oldContent": ""}, {"type": "DELETE", "lineNumber": 276, "oldContent": "    it's getting in api like \"created_at\": 17550904511930,"}, {"type": "DELETE", "lineNumber": 277, "oldContent": ""}, {"type": "DELETE", "lineNumber": 278, "oldContent": "    but i need to show with good ui like 12 Aug. 12:30 PM"}, {"type": "DELETE", "lineNumber": 279, "oldContent": ""}, {"type": "DELETE", "lineNumber": 280, "oldContent": "    like that you handle"}, {"type": "DELETE", "lineNumber": 281, "oldContent": ""}, {"type": "DELETE", "lineNumber": 282, "oldContent": "    if (participants != myUserId) {"}, {"type": "DELETE", "lineNumber": 283, "oldContent": "    await context"}, {"type": "DELETE", "lineNumber": 284, "oldContent": "        .read(ProfileProviders.profileSettingsProvidersApis)"}, {"type": "DELETE", "lineNumber": 285, "oldContent": "        .getUserData("}, {"type": "DELETE", "lineNumber": 286, "oldContent": "    context: context,"}, {"type": "DELETE", "lineNumber": 287, "oldContent": "    getMyProfileData: false,"}, {"type": "DELETE", "lineNumber": 288, "oldContent": "    userID: participants,"}, {"type": "DELETE", "lineNumber": 289, "oldContent": "    )"}, {"type": "DELETE", "lineNumber": 290, "oldContent": "        .then((value) {"}, {"type": "DELETE", "lineNumber": 291, "oldContent": "    inboxList.add("}, {"type": "DELETE", "lineNumber": 292, "oldContent": "    InboxModel.fromJson("}, {"type": "DELETE", "lineNumber": 293, "oldContent": "    data,"}, {"type": "DELETE", "lineNumber": 294, "oldContent": "    userName: value.userName!,"}, {"type": "DELETE", "lineNumber": 295, "oldContent": "    userImage: value.userImage!,"}, {"type": "DELETE", "lineNumber": 296, "oldContent": "    ),"}, {"type": "DELETE", "lineNumber": 297, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 298, "oldContent": "    });"}, {"type": "DELETE", "lineNumber": 299, "oldContent": "    } @/lib/providers/chat_providers_apis/inbox_providers_apis.dart"}, {"type": "DELETE", "lineNumber": 300, "oldContent": ""}, {"type": "DELETE", "lineNumber": 301, "oldContent": "  another thing this take so many time because it's sent like 12 api request "}, {"type": "DELETE", "lineNumber": 302, "oldContent": ""}, {"type": "DELETE", "lineNumber": 303, "oldContent": "  we make new api request that take multi ids and get the response"}, {"type": "DELETE", "lineNumber": 304, "oldContent": ""}, {"type": "DELETE", "lineNumber": 305, "oldContent": "  url is https://arabfertilizer.org/technical-conference//wp-json/buddypress-ext/v1/attendancee/by-ids"}, {"type": "DELETE", "lineNumber": 306, "oldContent": ""}, {"type": "DELETE", "lineNumber": 307, "oldContent": "  and body is ids and take ids like [1, 12, 7, 42]"}, {"type": "DELETE", "lineNumber": 308, "oldContent": ""}, {"type": "DELETE", "lineNumber": 309, "oldContent": "  {"}, {"type": "DELETE", "lineNumber": 310, "oldContent": "  \"ids\": [1, 12]"}, {"type": "DELETE", "lineNumber": 311, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 312, "oldContent": ""}, {"type": "DELETE", "lineNumber": 313, "oldContent": "  and it's response is "}, {"type": "DELETE", "lineNumber": 314, "oldContent": ""}, {"type": "DELETE", "lineNumber": 315, "oldContent": "  {"}, {"type": "DELETE", "lineNumber": 316, "oldContent": "  \"items\": ["}, {"type": "DELETE", "lineNumber": 317, "oldContent": "  {"}, {"type": "DELETE", "lineNumber": 318, "oldContent": "  \"id\": 1,"}, {"type": "DELETE", "lineNumber": 319, "oldContent": "  \"name\": \"<PERSON>\","}, {"type": "DELETE", "lineNumber": 320, "oldContent": "  \"avatar\": \"https://arabfertilizer.org/technical-conference/wp-content/uploads/sites/3/2025/07/scaled_1000180490-1-scaled.jpg\","}, {"type": "DELETE", "lineNumber": 321, "oldContent": "  \"email\": \"<EMAIL>\","}, {"type": "DELETE", "lineNumber": 322, "oldContent": "  \"type\": \"deletages\""}, {"type": "DELETE", "lineNumber": 323, "oldContent": "  },"}, {"type": "DELETE", "lineNumber": 324, "oldContent": "  {"}, {"type": "DELETE", "lineNumber": 325, "oldContent": "  \"id\": 12,"}, {"type": "DELETE", "lineNumber": 326, "oldContent": "  \"name\": \"mfcru\","}, {"type": "DELETE", "lineNumber": 327, "oldContent": "  \"avatar\": \"https://secure.gravatar.com/avatar/aca95db55556e012885b43e8b897cd45cf19fab322f5a7aaabfe18503a2abf78?s=96&d=mm&r=g\","}, {"type": "DELETE", "lineNumber": 328, "oldContent": "  \"email\": \"<EMAIL>\","}, {"type": "DELETE", "lineNumber": 329, "oldContent": "  \"type\": \"\""}, {"type": "DELETE", "lineNumber": 330, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 331, "oldContent": "  ],"}, {"type": "DELETE", "lineNumber": 332, "oldContent": "  \"not_found\": []"}, {"type": "DELETE", "lineNumber": 333, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 334, "oldContent": ""}, {"type": "DELETE", "lineNumber": 335, "oldContent": "  and inject the user name and avatar instead of this every time"}, {"type": "DELETE", "lineNumber": 336, "oldContent": ""}, {"type": "DELETE", "lineNumber": 337, "oldContent": "  await context"}, {"type": "DELETE", "lineNumber": 338, "oldContent": "      .read(ProfileProviders.profileSettingsProvidersApis)"}, {"type": "DELETE", "lineNumber": 339, "oldContent": "      .getUserData("}, {"type": "DELETE", "lineNumber": 340, "oldContent": "  context: context,"}, {"type": "DELETE", "lineNumber": 341, "oldContent": "  getMyProfileData: false,"}, {"type": "DELETE", "lineNumber": 342, "oldContent": "  userID: participants,"}, {"type": "DELETE", "lineNumber": 343, "oldContent": "  )"}, {"type": "DELETE", "lineNumber": 344, "oldContent": "      .then((value) {"}, {"type": "DELETE", "lineNumber": 345, "oldContent": "  inboxList.add("}, {"type": "DELETE", "lineNumber": 346, "oldContent": "  InboxModel.fromJson("}, {"type": "DELETE", "lineNumber": 347, "oldContent": "  data,"}, {"type": "DELETE", "lineNumber": 348, "oldContent": "  userName: value.userName!,"}, {"type": "DELETE", "lineNumber": 349, "oldContent": "  userImage: value.userImage!,"}, {"type": "DELETE", "lineNumber": 350, "oldContent": "  ),"}, {"type": "DELETE", "lineNumber": 351, "oldContent": "  );"}, {"type": "DELETE", "lineNumber": 352, "oldContent": "  });"}, {"type": "INSERT", "lineNumber": 4, "content": "    required String image,"}]}, {"timestamp": 1755092270207, "changes": [{"type": "INSERT", "lineNumber": 10, "content": "    // Parse timestamp from created_at field (milliseconds)"}, {"type": "INSERT", "lineNumber": 11, "content": "    if (jsonData['created_at'] != null) {"}, {"type": "INSERT", "lineNumber": 12, "content": "      timestamp = DateTime.fromMillisecondsSinceEpoch("}, {"type": "INSERT", "lineNumber": 13, "content": "        jsonData['created_at'] is int"}, {"type": "INSERT", "lineNumber": 14, "content": "            ? jsonData['created_at']"}, {"type": "INSERT", "lineNumber": 15, "content": "            : int.parse(jsonData['created_at'].toString()),"}, {"type": "INSERT", "lineNumber": 16, "content": "      );"}, {"type": "INSERT", "lineNumber": 17, "content": "    }"}, {"type": "DELETE", "lineNumber": 12, "oldContent": "  ChatScreenModel({this.message, this.userName, this.userImage, this.senderID});"}, {"type": "INSERT", "lineNumber": 20, "content": "  ChatScreenModel({"}, {"type": "INSERT", "lineNumber": 21, "content": "    this.message,"}, {"type": "INSERT", "lineNumber": 22, "content": "    this.userName,"}, {"type": "INSERT", "lineNumber": 23, "content": "    this.userImage,"}, {"type": "INSERT", "lineNumber": 24, "content": "    this.senderID,"}, {"type": "INSERT", "lineNumber": 25, "content": "    this.timestamp,"}, {"type": "INSERT", "lineNumber": 26, "content": "  });"}, {"type": "INSERT", "lineNumber": 27, "content": ""}, {"type": "INSERT", "lineNumber": 30, "content": "  DateTime? timestamp;"}, {"type": "INSERT", "lineNumber": 31, "content": ""}, {"type": "INSERT", "lineNumber": 32, "content": "  /// Format timestamp to readable format like \"12 Aug. 12:30 PM\""}, {"type": "INSERT", "lineNumber": 33, "content": "  String get formattedTimestamp {"}, {"type": "INSERT", "lineNumber": 34, "content": "    if (timestamp == null) return '';"}, {"type": "INSERT", "lineNumber": 35, "content": ""}, {"type": "INSERT", "lineNumber": 36, "content": "    final now = DateTime.now();"}, {"type": "INSERT", "lineNumber": 37, "content": "    final today = DateTime(now.year, now.month, now.day);"}, {"type": "INSERT", "lineNumber": 38, "content": "    final messageDate = DateTime("}, {"type": "INSERT", "lineNumber": 39, "content": "      timestamp!.year,"}, {"type": "INSERT", "lineNumber": 40, "content": "      timestamp!.month,"}, {"type": "INSERT", "lineNumber": 41, "content": "      timestamp!.day,"}, {"type": "INSERT", "lineNumber": 42, "content": "    );"}, {"type": "INSERT", "lineNumber": 43, "content": ""}, {"type": "INSERT", "lineNumber": 44, "content": "    // Format time as \"12:30 PM\""}, {"type": "INSERT", "lineNumber": 45, "content": "    final timeFormat = timestamp!.hour == 0"}, {"type": "INSERT", "lineNumber": 46, "content": "        ? '12:${timestamp!.minute.toString().padLeft(2, '0')} AM'"}, {"type": "INSERT", "lineNumber": 47, "content": "        : timestamp!.hour <= 12"}, {"type": "INSERT", "lineNumber": 48, "content": "        ? '${timestamp!.hour}:${timestamp!.minute.toString().padLeft(2, '0')} ${timestamp!.hour == 12 ? 'PM' : 'AM'}'"}, {"type": "INSERT", "lineNumber": 49, "content": "        : '${timestamp!.hour - 12}:${timestamp!.minute.toString().padLeft(2, '0')} PM';"}, {"type": "INSERT", "lineNumber": 50, "content": ""}, {"type": "INSERT", "lineNumber": 51, "content": "    // If message is from today, show only time"}, {"type": "INSERT", "lineNumber": 52, "content": "    if (messageDate == today) {"}, {"type": "INSERT", "lineNumber": 53, "content": "      return timeFormat;"}, {"type": "INSERT", "lineNumber": 54, "content": "    }"}, {"type": "INSERT", "lineNumber": 55, "content": ""}, {"type": "INSERT", "lineNumber": 56, "content": "    // If message is from yesterday"}, {"type": "INSERT", "lineNumber": 57, "content": "    final yesterday = today.subtract(const Duration(days: 1));"}, {"type": "INSERT", "lineNumber": 58, "content": "    if (messageDate == yesterday) {"}, {"type": "INSERT", "lineNumber": 59, "content": "      return 'Yesterday $timeFormat';"}, {"type": "INSERT", "lineNumber": 60, "content": "    }"}, {"type": "INSERT", "lineNumber": 61, "content": ""}, {"type": "INSERT", "lineNumber": 62, "content": "    // For older messages, show date and time"}, {"type": "INSERT", "lineNumber": 63, "content": "    final months = ["}, {"type": "INSERT", "lineNumber": 64, "content": "      'Jan',"}, {"type": "INSERT", "lineNumber": 65, "content": "      'Feb',"}, {"type": "INSERT", "lineNumber": 66, "content": "      'Mar',"}, {"type": "INSERT", "lineNumber": 67, "content": "      'Apr',"}, {"type": "INSERT", "lineNumber": 68, "content": "      'May',"}, {"type": "INSERT", "lineNumber": 69, "content": "      'Jun',"}, {"type": "INSERT", "lineNumber": 70, "content": "      'Jul',"}, {"type": "INSERT", "lineNumber": 71, "content": "      'Aug',"}, {"type": "INSERT", "lineNumber": 72, "content": "      'Sep',"}, {"type": "INSERT", "lineNumber": 73, "content": "      'Oct',"}, {"type": "INSERT", "lineNumber": 74, "content": "      'Nov',"}, {"type": "INSERT", "lineNumber": 75, "content": "      'Dec',"}, {"type": "INSERT", "lineNumber": 76, "content": "    ];"}, {"type": "INSERT", "lineNumber": 77, "content": ""}, {"type": "INSERT", "lineNumber": 78, "content": "    return '${timestamp!.day} ${months[timestamp!.month - 1]}. $timeFormat';"}, {"type": "INSERT", "lineNumber": 79, "content": "  }"}]}]}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/models/profile_models/profile_user_data_model.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/models/profile_models/profile_user_data_model.dart", "baseContent": "class ProfileUserDataModel {\n  ProfileUserDataModel.fromJson(Map<String, dynamic> jsonData) {\n    userID = jsonData['id'];\n    userName = jsonData['name'];\n    userImage = jsonData['avatar'];\n    userEmail = jsonData['email'];\n    country = jsonData['country'] ?? \"Egypt\";\n    position = jsonData['position'];\n    bio = jsonData['bio'];\n    phoneNumber = jsonData['phone'];\n  }\n\n  ProfileUserDataModel({\n    this.userID,\n    this.userName,\n    this.userImage,\n    this.userEmail,\n    this.country,\n    this.position,\n    this.bio,\n    this.phoneNumber,\n  });\n\n  int? userID;\n  dynamic userName, userImage, userEmail, country, position, bio, phoneNumber;\n}\n", "baseTimestamp": 1755092331285}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/screens/home_screens/home_categories_screens/accomodation_screen.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/screens/home_screens/home_categories_screens/accomodation_screen.dart", "baseContent": "import 'package:afa_app/app_config/api_providers/home_screen_categories_providers.dart';\nimport 'package:afa_app/app_config/app_colors.dart';\nimport 'package:afa_app/app_config/common_components.dart';\nimport 'package:afa_app/models/home_screens_models/venue_screen_model.dart';\nimport 'package:flutter/material.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:google_maps_flutter/google_maps_flutter.dart';\nimport 'package:riverpod_context/riverpod_context.dart';\n\nclass AccomodationScreen extends StatefulWidget {\n  const AccomodationScreen({super.key});\n\n  @override\n  State<AccomodationScreen> createState() => _AccomodationScreenState();\n}\n\nclass _AccomodationScreenState extends State<AccomodationScreen> {\n  Future<List<VenueScreenModel>>? _fetchVenueDetails;\n\n  @override\n  void initState() {\n    _fetchVenueDetails = context\n        .read(HomeScreenCategoriesProviders.venueScreenProvidersApis)\n        .getVenueDetails(context: context);\n    super.initState();\n  }\n\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      body: FutureBuilder(\n        future: _fetchVenueDetails,\n        builder: (context, AsyncSnapshot<List<VenueScreenModel>> snapshot) {\n          if (snapshot.connectionState == ConnectionState.waiting) {\n            return Center(child: CommonComponents.loadingDataFromServer());\n          } else if (snapshot.data == null) {\n            return Center(child: CommonComponents.noDataFoundWidget());\n          } else {\n            return SafeArea(\n              child: Column(\n                crossAxisAlignment: CrossAxisAlignment.start,\n                children: [\n                  CommonComponents.comonTitleScreen(\n                    context: context,\n                    title: \"Venue\",\n                  ),\n                  SizedBox(height: 10.0.h),\n\n                  Expanded(\n                    child: ListView.separated(\n                      padding: EdgeInsets.all(10.0.h),\n                      separatorBuilder: (context, venueIndex) =>\n                          const Divider(color: AppColors.greyColor),\n                      itemCount: snapshot.data!.length,\n                      itemBuilder: (context, venueIndex) => Column(\n                        crossAxisAlignment: CrossAxisAlignment.start,\n                        children: [\n                          ClipRRect(\n                            borderRadius: BorderRadiusGeometry.all(\n                              Radius.circular(16.0.r),\n                            ),\n                            child: CommonComponents.imageWithNetworkCache(\n                              context: context,\n                              image: snapshot.data![venueIndex].featuredImage!,\n                              height: 213.0.h,\n                              width: 398.0.w,\n                              fit: BoxFit.fill,\n                            ),\n                          ),\n                          SizedBox(height: 10.0.h),\n                          Text(\n                            snapshot.data![venueIndex].title!,\n                            style: TextStyle(\n                              fontSize: 18.0.sp,\n                              fontWeight: FontWeight.bold,\n                              color: AppColors.blackColor,\n                            ),\n                          ),\n                          SizedBox(height: 10.0.h),\n                          Text(\n                            snapshot.data![venueIndex].description!,\n                            style: TextStyle(fontSize: 14.0.sp),\n                          ),\n                          SizedBox(height: 15.0.h),\n                          SizedBox(\n                            height: 120.0.h,\n                            child: ListView.separated(\n                              scrollDirection: Axis.horizontal,\n                              physics: const BouncingScrollPhysics(),\n                              separatorBuilder: (context, imageIndex) =>\n                                  SizedBox(width: 20.0.w),\n                              itemCount:\n                                  snapshot.data![venueIndex].images!.length,\n                              itemBuilder: (context, imageIndex) => ClipRRect(\n                                borderRadius: BorderRadius.all(\n                                  Radius.circular(10.0.r),\n                                ),\n                                child: CommonComponents.imageWithNetworkCache(\n                                  context: context,\n                                  image: snapshot\n                                      .data![venueIndex]\n                                      .images![imageIndex],\n                                  height: 100.0.h,\n                                  width: 131.0.w,\n                                  fit: BoxFit.fill,\n                                ),\n                              ),\n                            ),\n                          ),\n                          SizedBox(height: 20.0.h),\n                          Text(\n                            \"Location\",\n                            style: TextStyle(\n                              fontSize: 16.0.sp,\n                              fontWeight: FontWeight.bold,\n                            ),\n                          ),\n                          SizedBox(height: 10.0.h),\n                          SizedBox(\n                            height: 255.0.h,\n                            width: 398.0.w,\n\n                            child: ClipRRect(\n                              borderRadius: BorderRadius.all(\n                                Radius.circular(12.0.r),\n                              ),\n                              child: GoogleMap(\n                                initialCameraPosition: CameraPosition(\n                                  target: LatLng(\n                                    snapshot.data![venueIndex].locationLat!,\n                                    snapshot.data![venueIndex].locationLong!,\n                                  ),\n                                  zoom: 18.0.h,\n                                ),\n                                zoomControlsEnabled: true,\n\n                                markers: {\n                                  Marker(\n                                    markerId: const MarkerId(\"location\"),\n                                    position: LatLng(\n                                      snapshot.data![venueIndex].locationLat!,\n                                      snapshot.data![venueIndex].locationLong!,\n                                    ),\n                                  ),\n                                },\n                              ),\n                            ),\n                          ),\n                          SizedBox(height: 20.0.h),\n                        ],\n                      ),\n                    ),\n                  ),\n\n                  // SizedBox(height: 20.0.h),\n                  // ElevatedButton(\n                  //   onPressed: () {},\n\n                  //   style: ElevatedButton.styleFrom(\n                  //     foregroundColor: Colors.white,\n                  //     backgroundColor: AppColors.midLevelGreenColor,\n                  //     textStyle: TextStyle(\n                  //       fontSize: 16.0.sp,\n                  //       fontWeight: FontWeight.bold,\n                  //     ),\n                  //     minimumSize: Size(398.0.w, 46.0.h),\n                  //     shape: RoundedRectangleBorder(\n                  //       borderRadius: BorderRadius.all(\n                  //         Radius.circular(32.0.r),\n                  //       ),\n                  //     ),\n                  //   ),\n                  //   child: const Text(\"Book Your room now\"),\n                  // ),\n                ],\n              ),\n            );\n          }\n        },\n      ),\n    );\n  }\n}\n", "baseTimestamp": 1755100462845}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/widgets/home_screens_widgets.dart/agenda_screens_widgets/agenda_details_speaker_widgets.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/widgets/home_screens_widgets.dart/agenda_screens_widgets/agenda_details_speaker_widgets.dart", "baseContent": "import 'package:afa_app/app_config/api_providers/chat_providers.dart';\nimport 'package:afa_app/app_config/api_providers/profile_providers.dart';\nimport 'package:afa_app/app_config/app_colors.dart';\nimport 'package:afa_app/app_config/common_components.dart';\nimport 'package:flutter/material.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:riverpod_context/riverpod_context.dart';\n\nclass AgendaDetailsSpeakerWidgets {\n  static Widget sessionWidgetFileds({\n    required BuildContext context,\n    required String image,\n    required String title,\n    required String subTitle,\n  }) {\n    return Row(\n      mainAxisAlignment: MainAxisAlignment.spaceBetween,\n      children: [\n        Row(\n          children: [\n            CommonComponents.imageAssetWithCache(\n              context: context,\n              image: image,\n              height: 16.0.h,\n              width: 16.0.w,\n              fit: BoxFit.contain,\n            ),\n            SizedBox(width: 10.0.w),\n            Text(\n              title,\n              style: TextStyle(\n                fontSize: 12.0.sp,\n                color: AppColors.greyColor,\n                fontWeight: FontWeight.bold,\n              ),\n            ),\n          ],\n        ),\n        Text(\n          subTitle,\n          style: TextStyle(\n            fontSize: 12.0.sp,\n            color: AppColors.midLevelGreenColor,\n            fontWeight: FontWeight.bold,\n          ),\n        ),\n      ],\n    );\n  }\n\n  static Widget sessionButtonsWidget({\n    required BuildContext context,\n    required String image,\n    required Function() onPress,\n  }) {\n    return InkWell(\n      onTap: onPress,\n      child: Container(\n        alignment: Alignment.center,\n        padding: EdgeInsets.all(10.0.h),\n        decoration: const BoxDecoration(\n          color: AppColors.lightGreenColor,\n          shape: BoxShape.circle,\n        ),\n        child: CommonComponents.imageAssetWithCache(\n          context: context,\n          image: image,\n          height: 16.0.h,\n          width: 16.0.w,\n          fit: BoxFit.contain,\n        ),\n      ),\n    );\n  }\n\n  static Future showNoteAlertWidget({\n    required BuildContext context,\n    required TextEditingController controller,\n    required GlobalKey<FormState> formKey,\n  }) async {\n    return await showGeneralDialog(\n      context: context,\n      barrierDismissible: true,\n      barrierLabel: \"\",\n      pageBuilder: (context, animation1, animation2) => Container(),\n      transitionDuration: const Duration(milliseconds: 400),\n      transitionBuilder: (context, animation1, animation2, child) =>\n          ScaleTransition(\n            scale: animation1,\n            child: AlertDialog(\n              contentPadding: EdgeInsets.all(10.0.h),\n              shape: RoundedRectangleBorder(\n                borderRadius: BorderRadius.circular(20.0),\n              ),\n              backgroundColor: Colors.white,\n              title: Center(\n                child: Text(\n                  \"Add A Note\",\n                  style: TextStyle(\n                    fontSize: 20.0.sp,\n                    fontWeight: FontWeight.bold,\n                    color: AppColors.blackColor,\n                  ),\n                ),\n              ),\n              content: Form(\n                key: formKey,\n                child: Column(\n                  crossAxisAlignment: CrossAxisAlignment.start,\n                  mainAxisSize: MainAxisSize.min,\n                  children: [\n                    Text(\n                      \"Note\",\n                      style: TextStyle(\n                        fontSize: 14.0.sp,\n                        fontWeight: FontWeight.bold,\n                      ),\n                    ),\n                    SizedBox(height: 5.0.h),\n                    TextFormField(\n                      controller: controller,\n                      validator: (value) =>\n                          value!.isEmpty ? \"Please Enter Note\" : null,\n                      maxLines: 3,\n                      style: TextStyle(\n                        fontSize: 12.0.sp,\n                        fontWeight: FontWeight.bold,\n                      ),\n                      decoration: InputDecoration(\n                        hintText: \"Enter\",\n                        hintStyle: TextStyle(\n                          fontSize: 12.0.sp,\n                          fontWeight: FontWeight.bold,\n                          color: AppColors.greyColor,\n                        ),\n                        border: OutlineInputBorder(\n                          borderSide: const BorderSide(\n                            color: AppColors.greyColor,\n                          ),\n                          borderRadius: BorderRadius.all(\n                            Radius.circular(16.0.r),\n                          ),\n                        ),\n                      ),\n                    ),\n                    SizedBox(height: 25.0.h),\n                    ElevatedButton(\n                      onPressed: () async {\n                        if (formKey.currentState!.validate()) {\n                          await context\n                              .read(ProfileProviders.commonApis)\n                              .sendNote(context: context, note: controller.text)\n                              .then((value) => controller.clear());\n                        }\n                      },\n                      style: ElevatedButton.styleFrom(\n                        foregroundColor: Colors.white,\n                        backgroundColor: AppColors.midLevelGreenColor,\n                        textStyle: TextStyle(\n                          fontSize: 16.0.sp,\n                          fontWeight: FontWeight.bold,\n                        ),\n                        minimumSize: Size(398.0.w, 46.0.h),\n                      ),\n                      child: const Text(\"Save\"),\n                    ),\n                  ],\n                ),\n              ),\n            ),\n          ),\n    );\n  }\n\n  static Future showSendNewMessageAlertWidget({\n    required BuildContext context,\n    required TextEditingController controller,\n    required GlobalKey<FormState> formKey,\n    required int speakerID,\n  }) async {\n    return await showGeneralDialog(\n      context: context,\n\n      barrierDismissible: true,\n      barrierLabel: \"\",\n      pageBuilder: (context, animation1, animation2) => Container(),\n      transitionDuration: const Duration(milliseconds: 400),\n      transitionBuilder: (context, animation1, animation2, child) =>\n          ScaleTransition(\n            scale: animation1,\n\n            child: AlertDialog(\n              contentPadding: EdgeInsets.all(10.0.h),\n              shape: RoundedRectangleBorder(\n                borderRadius: BorderRadius.circular(20.0),\n              ),\n              backgroundColor: Colors.white,\n              title: Center(\n                child: Text(\n                  \"Send First Message\",\n                  style: TextStyle(\n                    fontSize: 20.0.sp,\n                    fontWeight: FontWeight.bold,\n                    color: AppColors.blackColor,\n                  ),\n                ),\n              ),\n              content: Form(\n                key: formKey,\n                child: Column(\n                  crossAxisAlignment: CrossAxisAlignment.start,\n                  mainAxisSize: MainAxisSize.min,\n                  children: [\n                    Text(\n                      \"Message\",\n                      style: TextStyle(\n                        fontSize: 14.0.sp,\n                        fontWeight: FontWeight.bold,\n                      ),\n                    ),\n                    SizedBox(height: 5.0.h),\n                    TextFormField(\n                      controller: controller,\n                      validator: (value) =>\n                          value!.isEmpty ? \"Please Enter First Message\" : null,\n                      // maxLines: 3,\n                      style: TextStyle(\n                        fontSize: 12.0.sp,\n                        fontWeight: FontWeight.bold,\n                      ),\n                      decoration: InputDecoration(\n                        hintText: \"Enter\",\n                        hintStyle: TextStyle(\n                          fontSize: 12.0.sp,\n                          fontWeight: FontWeight.bold,\n                          color: AppColors.greyColor,\n                        ),\n                        border: OutlineInputBorder(\n                          borderSide: const BorderSide(\n                            color: AppColors.greyColor,\n                          ),\n                          borderRadius: BorderRadius.all(\n                            Radius.circular(16.0.r),\n                          ),\n                        ),\n                      ),\n                    ),\n                    SizedBox(height: 25.0.h),\n                    ElevatedButton(\n                      onPressed: () async {\n                        if (formKey.currentState!.validate()) {\n                          await context\n                              .read(ChatProviders.newMessageProvidersApis)\n                              .sendNewMessage(\n                                context: context,\n                                recipientId: speakerID,\n                                message: controller.text,\n                              );\n                        }\n                      },\n                      style: ElevatedButton.styleFrom(\n                        foregroundColor: Colors.white,\n                        backgroundColor: AppColors.midLevelGreenColor,\n                        textStyle: TextStyle(\n                          fontSize: 16.0.sp,\n                          fontWeight: FontWeight.bold,\n                        ),\n                        minimumSize: Size(398.0.w, 46.0.h),\n                      ),\n                      child: const Text(\"Send\"),\n                    ),\n                  ],\n                ),\n              ),\n            ),\n          ),\n    );\n  }\n}\n", "baseTimestamp": 1755100872511}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/models/polls_screen_models/polls_questions_model.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/models/polls_screen_models/polls_questions_model.dart", "baseContent": "import 'dart:developer';\n\nimport 'package:afa_app/models/polls_screen_models/poll_options_model.dart';\n\nclass PollsQuestionsModel {\n  PollsQuestionsModel.fromJson(Map<String, dynamic> jsonData) {\n    log('afasfsasafsaf ${jsonData}');\n    userId = jsonData['created_by'] != null ? jsonData['created_by']['id'] : 0;\n    userName = jsonData['created_by'] != null\n        ? jsonData['created_by']['name']\n        : \"\";\n    userImage = jsonData['created_by'] != null\n        ? jsonData['created_by']['avatar']\n        : \"\";\n    pollID = jsonData['id'];\n    question = jsonData['title'];\n    pollsOption = (jsonData['choices'] as List)\n        .map((options) => PollOptionsModel.fromJson(options))\n        .toList();\n  }\n\n  PollsQuestionsModel({\n    this.question,\n    this.pollsOption,\n    this.pollID,\n    this.userName,\n    this.userId,\n    this.userImage,\n  });\n\n  int? pollID, userId;\n  String? userName, userImage, question;\n  List<PollOptionsModel>? pollsOption;\n\n  Map<String, dynamic> toJson() => {\n    \"title\": question,\n    \"question\": question,\n    \"choices\": pollsOption!\n        .map(\n          (options) => {\n            \"label\": options.optionTitle,\n            \"uid\": options.optionID,\n            \"votes\": options.votesCount,\n          },\n        )\n        .toList(),\n  };\n}\n", "baseTimestamp": 1755101120060, "deltas": [{"timestamp": 1755101144481, "changes": [{"type": "INSERT", "lineNumber": 19, "content": "    uI"}]}, {"timestamp": 1755101148528, "changes": [{"type": "MODIFY", "lineNumber": 19, "content": "    userImage = jsonData['created_by'] != null", "oldContent": "    uI"}, {"type": "INSERT", "lineNumber": 20, "content": "        ? jsonData['created_by']['avatar']"}, {"type": "INSERT", "lineNumber": 21, "content": "        : \"\";"}]}, {"timestamp": 1755101192468, "changes": [{"type": "DELETE", "lineNumber": 19, "oldContent": "    userImage = jsonData['created_by'] != null"}, {"type": "DELETE", "lineNumber": 20, "oldContent": "        ? jsonData['created_by']['avatar']"}, {"type": "DELETE", "lineNumber": 22, "oldContent": "        : \"\";"}]}]}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/screens/home_screens/home_categories_screens/speakers_screen.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/screens/home_screens/home_categories_screens/speakers_screen.dart", "baseContent": "import 'package:afa_app/app_config/api_providers/home_screen_categories_providers.dart';\nimport 'package:afa_app/app_config/app_colors.dart';\nimport 'package:afa_app/app_config/app_images.dart';\nimport 'package:afa_app/app_config/common_components.dart';\nimport 'package:afa_app/app_config/routes.dart';\nimport 'package:afa_app/models/home_screens_models/speakers_screens_models/speeaker_screen_model.dart';\nimport 'package:afa_app/screens/home_screens/home_categories_screens/speakers_screens/speaker_details_screen.dart';\nimport 'package:afa_app/widgets/home_screens_widgets.dart/speakers_screen_widgets.dart';\nimport 'package:flutter/material.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:riverpod_context/riverpod_context.dart';\n\nclass SpeakersScreen extends StatefulWidget {\n  const SpeakersScreen({super.key});\n\n  @override\n  State<SpeakersScreen> createState() => _SpeakersScreenState();\n}\n\nclass _SpeakersScreenState extends State<SpeakersScreen> {\n  final TextEditingController _addNoteController = TextEditingController();\n  final TextEditingController _addNewMessageController =\n      TextEditingController();\n  final _formKey = GlobalKey<FormState>();\n  Future<List<SpeakerScreenModel>>? _fetchSpeakersList;\n\n  @override\n  void initState() {\n    _fetchSpeakersList = context\n        .read(HomeScreenCategoriesProviders.speakersScreenProvidersApis)\n        .getSpeakersList(context: context);\n    super.initState();\n  }\n\n  @override\n  void dispose() {\n    _addNoteController.dispose();\n    super.dispose();\n  }\n\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      body: SafeArea(\n        child: FutureBuilder(\n          future: _fetchSpeakersList,\n          builder: (context, AsyncSnapshot<List<SpeakerScreenModel>> snapshot) {\n            if (snapshot.connectionState == ConnectionState.waiting) {\n              return Center(child: CommonComponents.loadingDataFromServer());\n            } else if (snapshot.data == null) {\n              return Center(child: CommonComponents.noDataFoundWidget());\n            } else {\n              return Padding(\n                padding: EdgeInsets.all(10.0.h),\n                child: Column(\n                  crossAxisAlignment: CrossAxisAlignment.start,\n                  children: [\n                    CommonComponents.comonTitleScreen(\n                      context: context,\n                      title: \"Speakers\",\n                    ),\n                    SizedBox(height: 10.0.h),\n                    Expanded(\n                      child: ListView.separated(\n                        physics: const BouncingScrollPhysics(),\n                        separatorBuilder: (context, index) =>\n                            SizedBox(height: 10.0.h),\n                        itemCount: snapshot.data!.length,\n                        itemBuilder: (context, index) => Container(\n                          padding: EdgeInsets.all(10.0.h),\n                          decoration: BoxDecoration(\n                            border: Border.all(color: AppColors.greyColor),\n                            borderRadius: BorderRadius.all(\n                              Radius.circular(16.0.r),\n                            ),\n                          ),\n                          child: Column(\n                            children: [\n                              Row(\n                                mainAxisAlignment:\n                                    MainAxisAlignment.spaceBetween,\n                                crossAxisAlignment: CrossAxisAlignment.start,\n                                children: [\n                                  Row(\n                                    mainAxisAlignment: MainAxisAlignment.start,\n                                    crossAxisAlignment:\n                                        CrossAxisAlignment.start,\n\n                                    children: [\n                                      ClipOval(\n                                        child:\n                                            CommonComponents.imageWithNetworkCache(\n                                              context: context,\n                                              image:\n                                                  snapshot.data![index].image!,\n                                              height: 40.0.h,\n                                              width: 40.0.w,\n                                              fit: BoxFit.contain,\n                                            ),\n                                      ),\n                                      SizedBox(width: 5.0.w),\n                                      Column(\n                                        crossAxisAlignment:\n                                            CrossAxisAlignment.start,\n                                        children: [\n                                          Text(\n                                            snapshot.data![index].name!,\n                                            style: TextStyle(\n                                              fontSize: 14.0.sp,\n                                              color: AppColors.blackColor,\n                                              fontWeight: FontWeight.bold,\n                                            ),\n                                          ),\n                                          // SizedBox(height: 10.0.h),\n                                          Container(\n                                            padding: EdgeInsets.symmetric(\n                                              horizontal: 15.0.w,\n                                              vertical: 7.0.h,\n                                            ),\n                                            alignment: Alignment.center,\n\n                                            decoration: BoxDecoration(\n                                              borderRadius: BorderRadius.all(\n                                                Radius.circular(16.0.r),\n                                              ),\n                                              color: AppColors.lightGreenColor,\n                                            ),\n                                            child: Text(\n                                              snapshot.data![index].category!,\n                                              style: TextStyle(\n                                                fontSize: 14.0.sp,\n                                                color: AppColors\n                                                    .midLevelGreenColor,\n                                              ),\n                                            ),\n                                          ),\n                                        ],\n                                      ),\n                                    ],\n                                  ),\n                                  Container(\n                                    alignment: Alignment.center,\n                                    padding: EdgeInsets.all(10.0.h),\n                                    decoration: const BoxDecoration(\n                                      shape: BoxShape.circle,\n                                      color: AppColors.lightgreyColor,\n                                    ),\n                                    child: Icon(\n                                      Icons.bookmark_border,\n                                      size: 15.0.h,\n                                      color: AppColors.midLevelGreenColor,\n                                    ),\n                                  ),\n                                ],\n                              ),\n                              SizedBox(height: 10.0.h),\n                              SpeakersScreenWidgets.sessionWidgetFileds(\n                                context: context,\n                                image: AppImages.jobIcon,\n                                title: \"Job :\",\n                                subTitle: snapshot.data![index].jobTitle!,\n                              ),\n                              SizedBox(height: 10.0.h),\n                              SpeakersScreenWidgets.sessionWidgetFileds(\n                                context: context,\n                                image: AppImages.companyIcon,\n                                title: \"Company:\",\n                                subTitle: snapshot.data![index].companyName!,\n                              ),\n                              SizedBox(height: 10.0.h),\n                              SpeakersScreenWidgets.sessionWidgetFileds(\n                                context: context,\n                                image: AppImages.countryIcon,\n                                title: \"Country :\",\n                                subTitle: snapshot.data![index].country!,\n                              ),\n                              SizedBox(height: 20.0.h),\n                              const Divider(color: AppColors.greyColor),\n                              Row(\n                                mainAxisAlignment:\n                                    MainAxisAlignment.spaceBetween,\n                                children: [\n                                  Row(\n                                    children: [\n                                      SpeakersScreenWidgets.sessionButtonsWidget(\n                                        context: context,\n                                        image: AppImages.noteIcon,\n                                        onPress: () async {\n                                          await SpeakersScreenWidgets.showNoteAlertWidget(\n                                            context: context,\n                                            controller: _addNoteController,\n                                            formKey: _formKey,\n                                          );\n                                        },\n                                      ),\n                                      SizedBox(width: 10.0.w),\n                                      SpeakersScreenWidgets.sessionButtonsWidget(\n                                        context: context,\n                                        image: AppImages.messageIcon,\n                                        onPress: () async {\n                                          _addNewMessageController\n                                          await SpeakersScreenWidgets.showSendNewMessageAlertWidget(\n                                            context: context,\n                                            controller:\n                                                _addNewMessageController,\n                                            formKey: _formKey,\n                                            speakerID: int.parse(\n                                              snapshot.data![index].speakerID!,\n                                            ),\n                                          );\n                                        },\n                                      ),\n                                      SizedBox(width: 10.0.w),\n                                      SpeakersScreenWidgets.sessionButtonsWidget(\n                                        context: context,\n                                        image: AppImages.videoIcon,\n                                        onPress: () {\n                                          Navigator.pushNamed(\n                                            context,\n                                            PATHS.requestMeetingScreenSpeaker,\n                                          );\n                                        },\n                                      ),\n                                    ],\n                                  ),\n                                  TextButton(\n                                    onPressed: () {\n                                      Navigator.pushNamed(\n                                        context,\n                                        PATHS.speakerDetailsScreen,\n                                        arguments: SpeakerDetailsScreen(\n                                          speakerDetails: snapshot.data![index],\n                                        ),\n                                      );\n                                    },\n                                    style: TextButton.styleFrom(\n                                      foregroundColor:\n                                          AppColors.midLevelGreenColor,\n                                      padding: const EdgeInsets.all(0.0),\n                                      tapTargetSize:\n                                          MaterialTapTargetSize.shrinkWrap,\n                                      minimumSize: Size(27.0.w, 17.0.h),\n                                      textStyle: TextStyle(\n                                        fontSize: 12.0.sp,\n                                        fontWeight: FontWeight.bold,\n                                        decoration: TextDecoration.underline,\n                                        height: 1.0.h,\n                                      ),\n                                    ),\n                                    child: const Text(\"View\"),\n                                  ),\n                                ],\n                              ),\n                            ],\n                          ),\n                        ),\n                      ),\n                    ),\n                  ],\n                ),\n              );\n            }\n          },\n        ),\n      ),\n    );\n  }\n}\n", "baseTimestamp": 1755102298444, "deltas": [{"timestamp": 1755102302897, "changes": [{"type": "MODIFY", "lineNumber": 200, "content": "                                          _addNewMessageController.clear();", "oldContent": "                                          _addNewMessageController"}]}, {"timestamp": 1755102318395, "changes": [{"type": "DELETE", "lineNumber": 201, "oldContent": "                                          await SpeakersScreenWidgets.showSendNewMessageAlertWidget("}, {"type": "DELETE", "lineNumber": 202, "oldContent": "                                            context: context,"}, {"type": "DELETE", "lineNumber": 203, "oldContent": "                                            controller:"}, {"type": "DELETE", "lineNumber": 204, "oldContent": "                                                _addNewMessageController,"}, {"type": "DELETE", "lineNumber": 205, "oldContent": "                                            formKey: _formKey,"}, {"type": "DELETE", "lineNumber": 206, "oldContent": "                                            speakerID: int.parse("}, {"type": "DELETE", "lineNumber": 207, "oldContent": "                                              snapshot.data![index].speakerID!,"}, {"type": "DELETE", "lineNumber": 208, "oldContent": "                                            ),"}, {"type": "DELETE", "lineNumber": 209, "oldContent": "                                          );"}, {"type": "INSERT", "lineNumber": 201, "content": "                                          // await SpeakersScreenWidgets.showSendNewMessageAlertWidget("}, {"type": "INSERT", "lineNumber": 202, "content": "                                          //   context: context,"}, {"type": "INSERT", "lineNumber": 203, "content": "                                          //   controller:"}, {"type": "INSERT", "lineNumber": 204, "content": "                                          //       _addNewMessageController,"}, {"type": "INSERT", "lineNumber": 205, "content": "                                          //   formKey: _formKey,"}, {"type": "INSERT", "lineNumber": 206, "content": "                                          //   speakerID: int.parse("}, {"type": "INSERT", "lineNumber": 207, "content": "                                          //     snapshot.data![index].speakerID!,"}, {"type": "INSERT", "lineNumber": 208, "content": "                                          //   ),"}, {"type": "INSERT", "lineNumber": 209, "content": "                                          // );"}]}, {"timestamp": 1755102328272, "changes": [{"type": "DELETE", "lineNumber": 201, "oldContent": "                                          // await SpeakersScreenWidgets.showSendNewMessageAlertWidget("}, {"type": "DELETE", "lineNumber": 202, "oldContent": "                                          //   context: context,"}, {"type": "DELETE", "lineNumber": 203, "oldContent": "                                          //   controller:"}, {"type": "DELETE", "lineNumber": 204, "oldContent": "                                          //       _addNewMessageController,"}, {"type": "DELETE", "lineNumber": 205, "oldContent": "                                          //   formKey: _formKey,"}, {"type": "DELETE", "lineNumber": 206, "oldContent": "                                          //   speakerID: int.parse("}, {"type": "DELETE", "lineNumber": 207, "oldContent": "                                          //     snapshot.data![index].speakerID!,"}, {"type": "DELETE", "lineNumber": 208, "oldContent": "                                          //   ),"}, {"type": "DELETE", "lineNumber": 209, "oldContent": "                                          // );"}, {"type": "INSERT", "lineNumber": 201, "content": "                                          await SpeakersScreenWidgets.showSendNewMessageAlertWidget("}, {"type": "INSERT", "lineNumber": 202, "content": "                                            context: context,"}, {"type": "INSERT", "lineNumber": 203, "content": "                                            controller:"}, {"type": "INSERT", "lineNumber": 204, "content": "                                                _addNewMessageController,"}, {"type": "INSERT", "lineNumber": 205, "content": "                                            formKey: _formKey,"}, {"type": "INSERT", "lineNumber": 206, "content": "                                            speakerID: int.parse("}, {"type": "INSERT", "lineNumber": 207, "content": "                                              snapshot.data![index].speakerID!,"}, {"type": "INSERT", "lineNumber": 208, "content": "                                            ),"}, {"type": "INSERT", "lineNumber": 209, "content": "                                          );"}]}]}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/screens/home_screens/home_categories_screens/speakers_screens/speaker_details_screen.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/screens/home_screens/home_categories_screens/speakers_screens/speaker_details_screen.dart", "baseContent": "import 'package:afa_app/app_config/app_colors.dart';\nimport 'package:afa_app/app_config/app_images.dart';\nimport 'package:afa_app/app_config/common_components.dart';\nimport 'package:afa_app/app_config/routes.dart';\nimport 'package:afa_app/models/home_screens_models/speakers_screens_models/speeaker_screen_model.dart';\nimport 'package:afa_app/widgets/home_screens_widgets.dart/speakers_screen_widgets.dart';\nimport 'package:flutter/material.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\n\nclass SpeakerDetailsScreen extends StatefulWidget {\n  const SpeakerDetailsScreen({super.key, this.speakerDetails});\n  final SpeakerScreenModel? speakerDetails;\n\n  @override\n  State<SpeakerDetailsScreen> createState() => _SpeakerDetailsScreenState();\n}\n\nclass _SpeakerDetailsScreenState extends State<SpeakerDetailsScreen> {\n  final TextEditingController _addNoteController = TextEditingController();\n  final TextEditingController _addNewMessageController =\n      TextEditingController();\n  final _formKey = GlobalKey<FormState>();\n  @override\n  void dispose() {\n    _addNoteController.dispose();\n    _addNewMessageController.dispose();\n    super.dispose();\n  }\n\n  @override\n  Widget build(BuildContext context) {\n    final SpeakerDetailsScreen args =\n        ModalRoute.of(context)!.settings.arguments as SpeakerDetailsScreen;\n    return Scaffold(\n      body: SafeArea(\n        child: SingleChildScrollView(\n          padding: EdgeInsets.all(10.0.h),\n          physics: const BouncingScrollPhysics(),\n          child: Column(\n            children: [\n              Container(\n                padding: EdgeInsets.all(10.0.h),\n                decoration: BoxDecoration(\n                  border: Border.all(color: AppColors.greyColor),\n                  borderRadius: BorderRadius.all(Radius.circular(16.0.r)),\n                ),\n                child: Column(\n                  children: [\n                    Row(\n                      mainAxisAlignment: MainAxisAlignment.spaceBetween,\n                      crossAxisAlignment: CrossAxisAlignment.start,\n                      children: [\n                        Row(\n                          mainAxisAlignment: MainAxisAlignment.start,\n                          crossAxisAlignment: CrossAxisAlignment.start,\n\n                          children: [\n                            ClipOval(\n                              child: CommonComponents.imageWithNetworkCache(\n                                context: context,\n                                image: args.speakerDetails!.image!,\n                                height: 40.0.h,\n                                width: 40.0.w,\n                                fit: BoxFit.contain,\n                              ),\n                            ),\n                            SizedBox(width: 5.0.w),\n                            Column(\n                              crossAxisAlignment: CrossAxisAlignment.start,\n                              children: [\n                                Text(\n                                  args.speakerDetails!.name!,\n                                  style: TextStyle(\n                                    fontSize: 14.0.sp,\n                                    color: AppColors.blackColor,\n                                    fontWeight: FontWeight.bold,\n                                  ),\n                                ),\n                                // SizedBox(height: 10.0.h),\n                                Container(\n                                  padding: EdgeInsets.symmetric(\n                                    horizontal: 15.0.w,\n                                    vertical: 7.0.h,\n                                  ),\n                                  alignment: Alignment.center,\n\n                                  decoration: BoxDecoration(\n                                    borderRadius: BorderRadius.all(\n                                      Radius.circular(16.0.r),\n                                    ),\n                                    color: AppColors.lightGreenColor,\n                                  ),\n                                  child: Text(\n                                    args.speakerDetails!.category!,\n                                    style: TextStyle(\n                                      fontSize: 14.0.sp,\n                                      color: AppColors.midLevelGreenColor,\n                                    ),\n                                  ),\n                                ),\n                              ],\n                            ),\n                          ],\n                        ),\n                        Container(\n                          alignment: Alignment.center,\n                          padding: EdgeInsets.all(10.0.h),\n                          decoration: const BoxDecoration(\n                            shape: BoxShape.circle,\n                            color: AppColors.lightgreyColor,\n                          ),\n                          child: Icon(\n                            Icons.bookmark_border,\n                            size: 15.0.h,\n                            color: AppColors.midLevelGreenColor,\n                          ),\n                        ),\n                      ],\n                    ),\n                    SizedBox(height: 10.0.h),\n                    SpeakersScreenWidgets.sessionWidgetFileds(\n                      context: context,\n                      image: AppImages.jobIcon,\n                      title: \"Job :\",\n                      subTitle: args.speakerDetails!.jobTitle!,\n                    ),\n                    SizedBox(height: 10.0.h),\n                    SpeakersScreenWidgets.sessionWidgetFileds(\n                      context: context,\n                      image: AppImages.companyIcon,\n                      title: \"Company:\",\n                      subTitle: args.speakerDetails!.companyName!,\n                    ),\n                    SizedBox(height: 10.0.h),\n                    SpeakersScreenWidgets.sessionWidgetFileds(\n                      context: context,\n                      image: AppImages.countryIcon,\n                      title: \"Country :\",\n                      subTitle: args.speakerDetails!.country!,\n                    ),\n                    SizedBox(height: 20.0.h),\n                    const Divider(color: AppColors.greyColor),\n                    Row(\n                      children: [\n                        SpeakersScreenWidgets.sessionButtonsWidget(\n                          context: context,\n                          image: AppImages.noteIcon,\n                          onPress: () async {\n                            await SpeakersScreenWidgets.showNoteAlertWidget(\n                              context: context,\n                              controller: _addNoteController,\n                              formKey: _formKey,\n                            );\n                          },\n                        ),\n                        SizedBox(width: 10.0.w),\n                        SpeakersScreenWidgets.sessionButtonsWidget(\n                          context: context,\n                          image: AppImages.messageIcon,\n                          onPress: () async {\n                            _addNewMessageController.clear();\n\n                            await SpeakersScreenWidgets.showSendNewMessageAlertWidget(\n                              context: context,\n                              controller: _addNewMessageController,\n                              formKey: _formKey,\n                              speakerID: int.parse(\n                                args.speakerDetails!.speakerID!,\n                              ),\n                            );\n                          },\n                        ),\n                        SizedBox(width: 10.0.w),\n                        SpeakersScreenWidgets.sessionButtonsWidget(\n                          context: context,\n                          image: AppImages.videoIcon,\n                          onPress: () {\n                            Navigator.pushNamed(\n                              context,\n                              PATHS.requestMeetingScreenSpeaker,\n                            );\n                          },\n                        ),\n                      ],\n                    ),\n                  ],\n                ),\n              ),\n              SizedBox(height: 10.0.h),\n              Container(\n                padding: EdgeInsets.all(10.0.h),\n                decoration: BoxDecoration(\n                  border: Border.all(color: AppColors.greyColor),\n                  borderRadius: BorderRadius.all(Radius.circular(24.0.r)),\n                ),\n                child: Column(\n                  crossAxisAlignment: CrossAxisAlignment.start,\n                  children: [\n                    Row(\n                      mainAxisAlignment: MainAxisAlignment.start,\n                      children: [\n                        CommonComponents.imageAssetWithCache(\n                          context: context,\n                          image: AppImages.aboutIcon,\n                          height: 16.0.h,\n                          width: 16.0.w,\n                          fit: BoxFit.contain,\n                        ),\n                        SizedBox(width: 5.0.w),\n                        Text(\n                          \"About :\",\n                          style: TextStyle(\n                            fontSize: 12.0.sp,\n                            color: AppColors.greyColor,\n                          ),\n                        ),\n                      ],\n                    ),\n                    SizedBox(height: 10.0.h),\n                    Text(\n                      args.speakerDetails!.about!,\n                      style: TextStyle(\n                        fontSize: 10.0.sp,\n                        fontWeight: FontWeight.bold,\n                        color: AppColors.textColor,\n                      ),\n                    ),\n                    SizedBox(height: 10.0.h),\n                    const Divider(color: AppColors.greyColor),\n                    Row(\n                      mainAxisAlignment: MainAxisAlignment.start,\n                      children: [\n                        CommonComponents.imageAssetWithCache(\n                          context: context,\n                          image: AppImages.articeIcon,\n                          height: 16.0.h,\n                          width: 16.0.w,\n                          fit: BoxFit.contain,\n                        ),\n                        SizedBox(width: 5.0.w),\n                        Text(\n                          \"Speaking at :\",\n                          style: TextStyle(\n                            fontSize: 12.0.sp,\n                            color: AppColors.greyColor,\n                          ),\n                        ),\n                      ],\n                    ),\n                    SizedBox(height: 10.0.h),\n                    Text(\n                      args.speakerDetails!.speakingAt!,\n                      style: TextStyle(\n                        fontSize: 10.0.sp,\n                        fontWeight: FontWeight.bold,\n                        color: AppColors.textColor,\n                      ),\n                    ),\n                    SizedBox(height: 10.0.h),\n                    const Divider(color: AppColors.greyColor),\n                    Row(\n                      mainAxisAlignment: MainAxisAlignment.start,\n                      children: [\n                        CommonComponents.imageAssetWithCache(\n                          context: context,\n                          image: AppImages.expierenceIcon,\n                          height: 16.0.h,\n                          width: 16.0.w,\n                          fit: BoxFit.contain,\n                        ),\n                        SizedBox(width: 5.0.w),\n                        Text(\n                          \"Experience :\",\n                          style: TextStyle(\n                            fontSize: 12.0.sp,\n                            color: AppColors.greyColor,\n                          ),\n                        ),\n                      ],\n                    ),\n                    SizedBox(height: 10.0.h),\n                    ListView.separated(\n                      shrinkWrap: true,\n                      separatorBuilder: (context, index) =>\n                          SizedBox(height: 10.0.h),\n                      itemCount: args.speakerDetails!.experiences!.length,\n                      itemBuilder: (context, index) => Row(\n                        mainAxisAlignment: MainAxisAlignment.spaceBetween,\n                        children: [\n                          Row(\n                            children: [\n                              CommonComponents.imageWithNetworkCache(\n                                context: context,\n                                image:\n                                    args\n                                        .speakerDetails!\n                                        .experiences![index]\n                                        .companyImage ??\n                                    CommonComponents.imageNotFound,\n                                height: 24.0.h,\n                                width: 24.0.w,\n                                fit: BoxFit.contain,\n                              ),\n                              SizedBox(width: 5.0.w),\n                              Text(\n                                args\n                                    .speakerDetails!\n                                    .experiences![index]\n                                    .companyName!\n                                    .toString(),\n                                style: TextStyle(\n                                  fontSize: 10.0.sp,\n                                  color: AppColors.blackColor,\n                                ),\n                              ),\n                            ],\n                          ),\n                        ],\n                      ),\n                    ),\n\n                    const Divider(color: AppColors.greyColor),\n                    Row(\n                      mainAxisAlignment: MainAxisAlignment.start,\n                      children: [\n                        CommonComponents.imageAssetWithCache(\n                          context: context,\n                          image: AppImages.linksIcon,\n                          height: 16.0.h,\n                          width: 16.0.w,\n                          fit: BoxFit.contain,\n                        ),\n                        SizedBox(width: 5.0.w),\n                        Text(\n                          \"Links :\",\n                          style: TextStyle(\n                            fontSize: 12.0.sp,\n                            color: AppColors.greyColor,\n                          ),\n                        ),\n                      ],\n                    ),\n                    SizedBox(height: 10.0.h),\n                    ListView.separated(\n                      shrinkWrap: true,\n                      separatorBuilder: (context, index) =>\n                          SizedBox(height: 10.0.h),\n                      itemCount: args.speakerDetails!.socialMediaLinks!.length,\n                      itemBuilder: (context, index) => Row(\n                        mainAxisAlignment: MainAxisAlignment.spaceBetween,\n                        children: [\n                          Text(\n                            args.speakerDetails!.socialMediaLinks![index].name!,\n                            style: TextStyle(fontSize: 10.0.sp),\n                          ),\n                          InkWell(\n                            onTap: () async {\n                              await CommonComponents.launchOnBrowser(\n                                context: context,\n                                url: args\n                                    .speakerDetails!\n                                    .socialMediaLinks![index]\n                                    .url!,\n                              );\n                            },\n                            child: CommonComponents.imageAssetWithCache(\n                              context: context,\n                              image: AppImages.shareIcon,\n                              height: 20.0.h,\n                              width: 20.0.w,\n                              fit: BoxFit.contain,\n                            ),\n                          ),\n                        ],\n                      ),\n                    ),\n                  ],\n                ),\n              ),\n            ],\n          ),\n        ),\n      ),\n    );\n  }\n}\n", "baseTimestamp": 1755102341466, "deltas": [{"timestamp": 1755102354599, "changes": [{"type": "DELETE", "lineNumber": 162, "oldContent": "                            await SpeakersScreenWidgets.showSendNewMessageAlertWidget("}, {"type": "DELETE", "lineNumber": 163, "oldContent": "                              context: context,"}, {"type": "DELETE", "lineNumber": 164, "oldContent": "                              controller: _addNewMessageController,"}, {"type": "DELETE", "lineNumber": 165, "oldContent": "                              formKey: _formKey,"}, {"type": "DELETE", "lineNumber": 166, "oldContent": "                              speakerID: int.parse("}, {"type": "DELETE", "lineNumber": 167, "oldContent": "                                args.speakerDetails!.speakerID!,"}, {"type": "DELETE", "lineNumber": 168, "oldContent": "                              ),"}, {"type": "DELETE", "lineNumber": 169, "oldContent": "                            );"}, {"type": "INSERT", "lineNumber": 162, "content": "                            // await SpeakersScreenWidgets.showSendNewMessageAlertWidget("}, {"type": "INSERT", "lineNumber": 163, "content": "                            //   context: context,"}, {"type": "INSERT", "lineNumber": 164, "content": "                            //   controller: _addNewMessageController,"}, {"type": "INSERT", "lineNumber": 165, "content": "                            //   formKey: _formKey,"}, {"type": "INSERT", "lineNumber": 166, "content": "                            //   speakerID: int.parse("}, {"type": "INSERT", "lineNumber": 167, "content": "                            //     args.speakerDetails!.speakerID!,"}, {"type": "INSERT", "lineNumber": 168, "content": "                            //   ),"}, {"type": "INSERT", "lineNumber": 169, "content": "                            // );"}]}, {"timestamp": 1755102361458, "changes": [{"type": "DELETE", "lineNumber": 162, "oldContent": "                            // await SpeakersScreenWidgets.showSendNewMessageAlertWidget("}, {"type": "DELETE", "lineNumber": 163, "oldContent": "                            //   context: context,"}, {"type": "DELETE", "lineNumber": 164, "oldContent": "                            //   controller: _addNewMessageController,"}, {"type": "DELETE", "lineNumber": 165, "oldContent": "                            //   formKey: _formKey,"}, {"type": "DELETE", "lineNumber": 166, "oldContent": "                            //   speakerID: int.parse("}, {"type": "DELETE", "lineNumber": 167, "oldContent": "                            //     args.speakerDetails!.speakerID!,"}, {"type": "DELETE", "lineNumber": 168, "oldContent": "                            //   ),"}, {"type": "DELETE", "lineNumber": 169, "oldContent": "                            // );"}, {"type": "INSERT", "lineNumber": 162, "content": "                            await SpeakersScreenWidgets.showSendNewMessageAlertWidget("}, {"type": "INSERT", "lineNumber": 163, "content": "                              context: context,"}, {"type": "INSERT", "lineNumber": 164, "content": "                              controller: _addNewMessageController,"}, {"type": "INSERT", "lineNumber": 165, "content": "                              formKey: _formKey,"}, {"type": "INSERT", "lineNumber": 166, "content": "                              speakerID: int.parse("}, {"type": "INSERT", "lineNumber": 167, "content": "                                args.speakerDetails!.speakerID!,"}, {"type": "INSERT", "lineNumber": 168, "content": "                              ),"}, {"type": "INSERT", "lineNumber": 169, "content": "                            );"}]}, {"timestamp": 1755102363553, "changes": [{"type": "MODIFY", "lineNumber": 160, "content": "                            // _addNewMessageController.clear();", "oldContent": "                            _addNewMessageController.clear();"}]}]}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/widgets/home_screens_widgets.dart/attendance_screen_widgets.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/widgets/home_screens_widgets.dart/attendance_screen_widgets.dart", "baseContent": "import 'package:afa_app/app_config/api_providers/chat_providers.dart';\nimport 'package:afa_app/app_config/api_providers/profile_providers.dart';\nimport 'package:afa_app/app_config/app_colors.dart';\nimport 'package:afa_app/app_config/common_components.dart';\nimport 'package:afa_app/models/home_screens_models/attendance_screens_models/attendance_categories_model.dart';\nimport 'package:flutter/material.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:riverpod_context/riverpod_context.dart';\n\nclass AttendanceScreenWidgets {\n  static Widget bublesWidget({\n    required String title,\n    required Color containerColor,\n    required Color textColor,\n    required double height,\n    required double width,\n    required double top,\n    required double left,\n  }) {\n    return Container(\n      height: height,\n      width: width,\n      decoration: BoxDecoration(\n        color: containerColor,\n        border: Border.all(color: containerColor),\n        shape: BoxShape.circle,\n      ),\n      child: Center(\n        child: Text(\n          title,\n          style: TextStyle(\n            fontSize: 18.0.sp,\n            fontWeight: FontWeight.bold,\n            color: textColor,\n          ),\n        ),\n      ),\n    );\n  }\n\n  static Widget headerAttendanceWidget({\n    required List<AttendanceCategoriesModel> attendanceCategories,\n  }) {\n    return GridView.builder(\n      shrinkWrap: true,\n      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(\n        crossAxisCount: 3,\n        mainAxisExtent: 90.0.h,\n        crossAxisSpacing: 10.0.w,\n        mainAxisSpacing: 10.0.h,\n      ),\n      itemCount: attendanceCategories.length,\n      itemBuilder: (context, index) => AttendanceScreenWidgets.bublesWidget(\n        title: attendanceCategories[index].count.toString(),\n        containerColor: attendanceCategories[index].color!,\n        textColor: Colors.white,\n        height: 90.0.h,\n        width: 90.0.w,\n        top: 40.0.h,\n        left: 0.0.w,\n      ),\n    );\n  }\n\n  static Widget statisticsWidget({\n    required String title,\n    required String subTitle,\n    required Color color,\n  }) {\n    return Row(\n      crossAxisAlignment: CrossAxisAlignment.start,\n      children: [\n        Container(\n          height: 15.0.h,\n          width: 8.0.w,\n          decoration: BoxDecoration(color: color, shape: BoxShape.circle),\n        ),\n        SizedBox(width: 5.0.w),\n        Column(\n          crossAxisAlignment: CrossAxisAlignment.center,\n          mainAxisSize: MainAxisSize.min,\n\n          children: [\n            Text(\n              title,\n              style: TextStyle(\n                fontSize: 10.0.sp,\n                fontWeight: FontWeight.bold,\n                color: AppColors.blackColor,\n              ),\n            ),\n            SizedBox(height: 5.0.h),\n            Text(\n              subTitle,\n              style: TextStyle(\n                fontSize: 10.0.sp,\n                fontWeight: FontWeight.bold,\n                color: AppColors.blackColor,\n              ),\n            ),\n          ],\n        ),\n      ],\n    );\n  }\n\n  static Widget sessionWidgetFileds({\n    required BuildContext context,\n    required String image,\n    required String title,\n    required String subTitle,\n  }) {\n    return Row(\n      mainAxisAlignment: MainAxisAlignment.spaceBetween,\n      children: [\n        Row(\n          children: [\n            CommonComponents.imageAssetWithCache(\n              context: context,\n              image: image,\n              height: 16.0.h,\n              width: 16.0.w,\n              fit: BoxFit.contain,\n            ),\n            SizedBox(width: 10.0.w),\n            Text(\n              title,\n              style: TextStyle(\n                fontSize: 12.0.sp,\n                color: AppColors.greyColor,\n                fontWeight: FontWeight.bold,\n              ),\n            ),\n          ],\n        ),\n        Expanded(\n          child: Text(\n            subTitle,\n            style: TextStyle(\n              fontSize: 12.0.sp,\n              color: AppColors.midLevelGreenColor,\n              fontWeight: FontWeight.bold,\n            ),\n          ),\n        ),\n      ],\n    );\n  }\n\n  static Widget sessionButtonsWidget({\n    required BuildContext context,\n    required String image,\n    required Function() onPress,\n  }) {\n    return InkWell(\n      onTap: onPress,\n      child: Container(\n        alignment: Alignment.center,\n        padding: EdgeInsets.all(10.0.h),\n        decoration: const BoxDecoration(\n          color: AppColors.lightGreenColor,\n          shape: BoxShape.circle,\n        ),\n        child: CommonComponents.imageAssetWithCache(\n          context: context,\n          image: image,\n          height: 16.0.h,\n          width: 16.0.w,\n          fit: BoxFit.contain,\n        ),\n      ),\n    );\n  }\n\n  static Future showNoteAlertWidget({\n    required BuildContext context,\n    required TextEditingController controller,\n    required GlobalKey<FormState> formKey,\n  }) async {\n    return await showGeneralDialog(\n      context: context,\n      barrierDismissible: true,\n      barrierLabel: \"\",\n      pageBuilder: (context, animation1, animation2) => Container(),\n      transitionDuration: const Duration(milliseconds: 400),\n      transitionBuilder: (context, animation1, animation2, child) =>\n          ScaleTransition(\n            scale: animation1,\n            child: AlertDialog(\n              contentPadding: EdgeInsets.all(10.0.h),\n              shape: RoundedRectangleBorder(\n                borderRadius: BorderRadius.circular(20.0),\n              ),\n              backgroundColor: Colors.white,\n              title: Center(\n                child: Text(\n                  \"Add A Note\",\n                  style: TextStyle(\n                    fontSize: 20.0.sp,\n                    fontWeight: FontWeight.bold,\n                    color: AppColors.blackColor,\n                  ),\n                ),\n              ),\n              content: Form(\n                key: formKey,\n                child: Column(\n                  crossAxisAlignment: CrossAxisAlignment.start,\n                  mainAxisSize: MainAxisSize.min,\n                  children: [\n                    Text(\n                      \"Note\",\n                      style: TextStyle(\n                        fontSize: 14.0.sp,\n                        fontWeight: FontWeight.bold,\n                      ),\n                    ),\n                    SizedBox(height: 5.0.h),\n                    TextFormField(\n                      controller: controller,\n                      validator: (value) =>\n                          value!.isEmpty ? \"Please Enter Note\" : null,\n                      maxLines: 3,\n                      style: TextStyle(\n                        fontSize: 12.0.sp,\n                        fontWeight: FontWeight.bold,\n                      ),\n                      decoration: InputDecoration(\n                        hintText: \"Enter\",\n                        hintStyle: TextStyle(\n                          fontSize: 12.0.sp,\n                          fontWeight: FontWeight.bold,\n                          color: AppColors.greyColor,\n                        ),\n                        border: OutlineInputBorder(\n                          borderSide: const BorderSide(\n                            color: AppColors.greyColor,\n                          ),\n                          borderRadius: BorderRadius.all(\n                            Radius.circular(16.0.r),\n                          ),\n                        ),\n                      ),\n                    ),\n                    SizedBox(height: 25.0.h),\n                    ElevatedButton(\n                      onPressed: () async {\n                        if (formKey.currentState!.validate()) {\n                          await context\n                              .read(ProfileProviders.commonApis)\n                              .sendNote(\n                                context: context,\n                                note: controller.text,\n                              );\n                        }\n                      },\n                      style: ElevatedButton.styleFrom(\n                        foregroundColor: Colors.white,\n                        backgroundColor: AppColors.midLevelGreenColor,\n                        textStyle: TextStyle(\n                          fontSize: 16.0.sp,\n                          fontWeight: FontWeight.bold,\n                        ),\n                        minimumSize: Size(398.0.w, 46.0.h),\n                      ),\n                      child: const Text(\"Save\"),\n                    ),\n                  ],\n                ),\n              ),\n            ),\n          ),\n    );\n  }\n\n  static Future showSendNewMessageAlertWidget({\n    required BuildContext context,\n    required TextEditingController controller,\n    required GlobalKey<FormState> formKey,\n    required int attendanceID,\n  }) async {\n    return await showGeneralDialog(\n      context: context,\n      barrierDismissible: true,\n      barrierLabel: \"\",\n      pageBuilder: (context, animation1, animation2) => Container(),\n      transitionDuration: const Duration(milliseconds: 400),\n      transitionBuilder: (context, animation1, animation2, child) =>\n          ScaleTransition(\n            scale: animation1,\n            child: AlertDialog(\n              contentPadding: EdgeInsets.all(10.0.h),\n              shape: RoundedRectangleBorder(\n                borderRadius: BorderRadius.circular(20.0),\n              ),\n              backgroundColor: Colors.white,\n              title: Center(\n                child: Text(\n                  \"fsSend First Message\",\n                  style: TextStyle(\n                    fontSize: 20.0.sp,\n                    fontWeight: FontWeight.bold,\n                    color: AppColors.blackColor,\n                  ),\n                ),\n              ),\n              content: Form(\n                key: formKey,\n                child: Column(\n                  crossAxisAlignment: CrossAxisAlignment.start,\n                  mainAxisSize: MainAxisSize.min,\n                  children: [\n                    Text(\n                      \"Message\",\n                      style: TextStyle(\n                        fontSize: 14.0.sp,\n                        fontWeight: FontWeight.bold,\n                      ),\n                    ),\n                    SizedBox(height: 5.0.h),\n                    TextFormField(\n                      controller: controller,\n                      validator: (value) =>\n                          value!.isEmpty ? \"Please Enter First Message\" : null,\n                      // maxLines: 3,\n                      style: TextStyle(\n                        fontSize: 12.0.sp,\n                        fontWeight: FontWeight.bold,\n                      ),\n                      decoration: InputDecoration(\n                        hintText: \"Enter\",\n                        hintStyle: TextStyle(\n                          fontSize: 12.0.sp,\n                          fontWeight: FontWeight.bold,\n                          color: AppColors.greyColor,\n                        ),\n                        border: OutlineInputBorder(\n                          borderSide: const BorderSide(\n                            color: AppColors.greyColor,\n                          ),\n                          borderRadius: BorderRadius.all(\n                            Radius.circular(16.0.r),\n                          ),\n                        ),\n                      ),\n                    ),\n                    SizedBox(height: 25.0.h),\n                    ElevatedButton(\n                      onPressed: () async {\n                        if (formKey.currentState!.validate()) {\n                          await context\n                              .read(ChatProviders.newMessageProvidersApis)\n                              .sendNewMessage(\n                                context: context,\n                                recipientId: attendanceID,\n                                message: controller.text,\n                              );\n                        }\n                      },\n                      style: ElevatedButton.styleFrom(\n                        foregroundColor: Colors.white,\n                        backgroundColor: AppColors.midLevelGreenColor,\n                        textStyle: TextStyle(\n                          fontSize: 16.0.sp,\n                          fontWeight: FontWeight.bold,\n                        ),\n                        minimumSize: Size(398.0.w, 46.0.h),\n                      ),\n                      child: const Text(\"Send\"),\n                    ),\n                  ],\n                ),\n              ),\n            ),\n          ),\n    );\n  }\n}\n", "baseTimestamp": 1755102433179, "deltas": [{"timestamp": 1755102503957, "changes": [{"type": "MODIFY", "lineNumber": 298, "content": "                  \"Send First Message\",", "oldContent": "                  \"fsSend First Message\","}]}, {"timestamp": 1755102631414, "changes": [{"type": "INSERT", "lineNumber": 254, "content": "                          controller.clear();"}]}, {"timestamp": 1755102676249, "changes": [{"type": "INSERT", "lineNumber": 358, "content": "                          controller.clear();"}]}, {"timestamp": 1755103570640, "changes": [{"type": "MODIFY", "lineNumber": 377, "content": "    ).then((value) {", "oldContent": "    );"}, {"type": "INSERT", "lineNumber": 378, "content": "      controller.clear();"}, {"type": "INSERT", "lineNumber": 379, "content": "    },);"}]}, {"timestamp": 1755103579010, "changes": [{"type": "INSERT", "lineNumber": 379, "content": "    });"}, {"type": "DELETE", "lineNumber": 380, "oldContent": "    },);"}]}]}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/screens/home_screens/home_categories_screens/attendance_screen.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/screens/home_screens/home_categories_screens/attendance_screen.dart", "baseContent": "import 'package:afa_app/app_config/api_providers/home_screen_categories_providers.dart';\nimport 'package:afa_app/app_config/app_colors.dart';\nimport 'package:afa_app/app_config/app_images.dart';\nimport 'package:afa_app/app_config/common_components.dart';\nimport 'package:afa_app/app_config/routes.dart';\nimport 'package:afa_app/models/home_screens_models/attendance_screens_models/attendance_categories_model.dart';\nimport 'package:afa_app/models/home_screens_models/attendance_screens_models/attendance_screen_model.dart';\nimport 'package:afa_app/screens/home_screens/home_categories_screens/attendance_screens/attendance_details_screen.dart';\nimport 'package:afa_app/screens/home_screens/home_categories_screens/attendance_screens/request_meeting_screen_attendance.dart';\nimport 'package:afa_app/widgets/home_screens_widgets.dart/attendance_screen_widgets.dart';\nimport 'package:flutter/material.dart';\nimport 'package:flutter_riverpod/flutter_riverpod.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:pull_to_refresh/pull_to_refresh.dart';\nimport 'package:riverpod_context/riverpod_context.dart';\n\nclass AttendanceScreen extends StatefulWidget {\n  const AttendanceScreen({super.key});\n\n  @override\n  State<AttendanceScreen> createState() => _AttendanceScreenState();\n}\n\nclass _AttendanceScreenState extends State<AttendanceScreen> {\n  final TextEditingController _addNoteController = TextEditingController();\n  final TextEditingController _addFirstMessageController =\n      TextEditingController();\n  final _formKey = GlobalKey<FormState>();\n  Future<List<AttendanceScreenModel>>? _fetchAttendanceList;\n  Future<List<AttendanceCategoriesModel>>? _fetchAttendanceCategories;\n  final RefreshController _refreshController = RefreshController(\n    initialRefresh: false,\n  );\n\n  bool loadingAttendanceByCategory = false;\n\n  @override\n  void initState() {\n    _fetchAttendanceList = context\n        .read(HomeScreenCategoriesProviders.attendanceScreenProvidersApis)\n        .getAttendanceList(\n          context: context,\n          controller: _refreshController,\n          initialLoading: true,\n        );\n\n    _fetchAttendanceCategories = context\n        .read(HomeScreenCategoriesProviders.attendanceScreenProvidersApis)\n        .getAttendanceCategories(context: context);\n\n    super.initState();\n  }\n\n  void _onLoadingAttendancePgination() async {\n    await Future.delayed(Duration.zero, () async {\n      if (!mounted) return;\n      _fetchAttendanceList = context\n          .read(HomeScreenCategoriesProviders.attendanceScreenProvidersApis)\n          .getAttendanceList(\n            context: context,\n            controller: _refreshController,\n            isLoading: true,\n          );\n    });\n    setState(() {});\n  }\n\n  void _onRefreshAttendancePgination() async {\n    await Future.delayed(Duration.zero, () async {\n      if (!mounted) return;\n      _fetchAttendanceList = context\n          .read(HomeScreenCategoriesProviders.attendanceScreenProvidersApis)\n          .getAttendanceList(\n            context: context,\n            controller: _refreshController,\n            isRefresh: true,\n          );\n    });\n    setState(() {});\n  }\n\n  @override\n  void dispose() {\n    _addNoteController.dispose();\n    _refreshController.dispose();\n    _addFirstMessageController.dispose();\n    super.dispose();\n  }\n\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      body: SafeArea(\n        child: Consumer(\n          builder: (context, watch, child) => SmartRefresher(\n            controller: _refreshController,\n            enablePullUp: true,\n            enablePullDown: true,\n            onLoading: _onLoadingAttendancePgination,\n            onRefresh: _onRefreshAttendancePgination,\n            child: FutureBuilder(\n              future: Future.wait([\n                _fetchAttendanceList!,\n                _fetchAttendanceCategories!,\n              ]),\n              builder: (context, AsyncSnapshot<List<dynamic>> snapshot) {\n                if (snapshot.data == null) {\n                  return Center(\n                    child: CommonComponents.loadingDataFromServer(),\n                  );\n                } else {\n                  final List<AttendanceScreenModel> attendanceData =\n                      snapshot.data![0];\n                  final List<AttendanceCategoriesModel> attendanceCategories =\n                      snapshot.data![1];\n                  return SingleChildScrollView(\n                    physics: const BouncingScrollPhysics(),\n                    padding: EdgeInsets.all(10.0.h),\n                    child: Column(\n                      crossAxisAlignment: CrossAxisAlignment.start,\n                      children: [\n                        CommonComponents.comonTitleScreen(\n                          context: context,\n                          title: \"Attendees\",\n                        ),\n                        SizedBox(height: 20.0.h),\n                        AttendanceScreenWidgets.headerAttendanceWidget(\n                          attendanceCategories: attendanceCategories,\n                        ),\n                        SizedBox(height: 60.0.h),\n                        GridView.builder(\n                          shrinkWrap: true,\n                          itemCount: attendanceCategories.length,\n                          gridDelegate:\n                              SliverGridDelegateWithFixedCrossAxisCount(\n                                crossAxisCount: 4,\n                                crossAxisSpacing: 0.0.h,\n                                mainAxisSpacing: 0.0.h,\n                                mainAxisExtent: 50.0.h,\n                              ),\n                          itemBuilder: (context, index) =>\n                              AttendanceScreenWidgets.statisticsWidget(\n                                title: attendanceCategories[index].name!,\n                                subTitle: attendanceCategories[index].count!\n                                    .toString(),\n                                color: attendanceCategories[index].color!,\n                              ),\n                        ),\n\n                        SizedBox(height: 10.0.h),\n                        SizedBox(\n                          height: 50.0.h,\n                          child: ListView.separated(\n                            physics: const BouncingScrollPhysics(),\n                            scrollDirection: Axis.horizontal,\n                            // shrinkWrap: true,\n                            separatorBuilder: (context, index) =>\n                                SizedBox(width: 10.0.w),\n                            itemCount: attendanceCategories.length,\n                            itemBuilder: (context, index) {\n                              return InkWell(\n                                onTap: () {\n                                  context\n                                      .read(\n                                        HomeScreenCategoriesProviders\n                                            .attendanceScreenProvidersApis,\n                                      )\n                                      .setCategorySelected(\n                                        attendanceCategories[index],\n                                      );\n\n                                  setState(() {\n                                    _fetchAttendanceList = context\n                                        .read(\n                                          HomeScreenCategoriesProviders\n                                              .attendanceScreenProvidersApis,\n                                        )\n                                        .getAttendanceList(\n                                          context: context,\n                                          controller: _refreshController,\n                                          initialLoading: true,\n                                          showLoading: true,\n                                        );\n                                  });\n                                },\n                                child: Container(\n                                  padding: EdgeInsets.all(15.0.h),\n                                  alignment: Alignment.center,\n                                  decoration: BoxDecoration(\n                                    borderRadius: BorderRadius.all(\n                                      Radius.circular(100.0.r),\n                                    ),\n                                    color:\n                                        watch\n                                                .watch(\n                                                  HomeScreenCategoriesProviders\n                                                      .attendanceScreenProvidersApis,\n                                                )\n                                                .categorySelected ==\n                                            attendanceCategories[index]\n                                        ? AppColors.midLevelGreenColor\n                                        : AppColors.lightGreenColor,\n                                  ),\n                                  child: Text(\n                                    attendanceCategories[index].name!,\n                                    style: TextStyle(\n                                      fontSize: 14.0.sp,\n                                      color:\n                                          watch\n                                                  .watch(\n                                                    HomeScreenCategoriesProviders\n                                                        .attendanceScreenProvidersApis,\n                                                  )\n                                                  .categorySelected ==\n                                              attendanceCategories[index]\n                                          ? Colors.white\n                                          : AppColors.midLevelGreenColor,\n                                    ),\n                                  ),\n                                ),\n                              );\n                            },\n                          ),\n                        ),\n                        SizedBox(height: 10.0.h),\n\n                        ListView.separated(\n                          shrinkWrap: true,\n                          physics: const BouncingScrollPhysics(),\n                          separatorBuilder: (context, index) =>\n                              SizedBox(height: 10.0.h),\n                          itemCount: attendanceData.length,\n                          itemBuilder: (context, index) => Container(\n                            padding: EdgeInsets.all(10.0.h),\n                            decoration: BoxDecoration(\n                              border: Border.all(color: AppColors.greyColor),\n                              borderRadius: BorderRadius.all(\n                                Radius.circular(16.0.r),\n                              ),\n                            ),\n                            child: Column(\n                              children: [\n                                Row(\n                                  mainAxisAlignment:\n                                      MainAxisAlignment.spaceBetween,\n                                  crossAxisAlignment: CrossAxisAlignment.start,\n                                  children: [\n                                    Row(\n                                      mainAxisAlignment:\n                                          MainAxisAlignment.start,\n                                      crossAxisAlignment:\n                                          CrossAxisAlignment.start,\n\n                                      children: [\n                                        ClipOval(\n                                          child:\n                                              CommonComponents.imageWithNetworkCache(\n                                                context: context,\n                                                image: attendanceData[index]\n                                                    .userImage!,\n                                                height: 40.0.h,\n                                                width: 40.0.w,\n                                                fit: BoxFit.contain,\n                                              ),\n                                        ),\n                                        SizedBox(width: 5.0.w),\n                                        Column(\n                                          crossAxisAlignment:\n                                              CrossAxisAlignment.start,\n                                          children: [\n                                            Text(\n                                              attendanceData[index].userName!,\n                                              style: TextStyle(\n                                                fontSize: 14.0.sp,\n                                                color: AppColors.blackColor,\n                                                fontWeight: FontWeight.bold,\n                                              ),\n                                            ),\n                                            // SizedBox(height: 10.0.h),\n                                            Container(\n                                              padding: EdgeInsets.symmetric(\n                                                horizontal: 15.0.w,\n                                                vertical: 7.0.h,\n                                              ),\n                                              alignment: Alignment.center,\n\n                                              decoration: BoxDecoration(\n                                                borderRadius: BorderRadius.all(\n                                                  Radius.circular(16.0.r),\n                                                ),\n                                                color:\n                                                    AppColors.lightGreenColor,\n                                              ),\n                                              child: Text(\n                                                attendanceData[index]\n                                                    .categorySelected!,\n                                                style: TextStyle(\n                                                  fontSize: 14.0.sp,\n                                                  color: AppColors\n                                                      .midLevelGreenColor,\n                                                ),\n                                              ),\n                                            ),\n                                          ],\n                                        ),\n                                      ],\n                                    ),\n                                    Container(\n                                      alignment: Alignment.center,\n                                      padding: EdgeInsets.all(10.0.h),\n                                      decoration: const BoxDecoration(\n                                        shape: BoxShape.circle,\n                                        color: AppColors.lightgreyColor,\n                                      ),\n                                      child: Icon(\n                                        Icons.bookmark_border,\n                                        size: 15.0.h,\n                                        color: AppColors.midLevelGreenColor,\n                                      ),\n                                    ),\n                                  ],\n                                ),\n                                SizedBox(height: 10.0.h),\n                                AttendanceScreenWidgets.sessionWidgetFileds(\n                                  context: context,\n                                  image: AppImages.jobIcon,\n                                  title: \"Job :\",\n                                  subTitle: attendanceData[index].jobName!,\n                                ),\n                                SizedBox(height: 10.0.h),\n                                AttendanceScreenWidgets.sessionWidgetFileds(\n                                  context: context,\n                                  image: AppImages.companyIcon,\n                                  title: \"Company:\",\n                                  subTitle: attendanceData[index].company!,\n                                ),\n                                SizedBox(height: 10.0.h),\n                                AttendanceScreenWidgets.sessionWidgetFileds(\n                                  context: context,\n                                  image: AppImages.countryIcon,\n                                  title: \"Country :\",\n                                  subTitle: attendanceData[index].country!,\n                                ),\n                                SizedBox(height: 20.0.h),\n                                const Divider(color: AppColors.greyColor),\n                                Row(\n                                  mainAxisAlignment:\n                                      MainAxisAlignment.spaceBetween,\n                                  children: [\n                                    Row(\n                                      children: [\n                                        AttendanceScreenWidgets.sessionButtonsWidget(\n                                          context: context,\n                                          image: AppImages.noteIcon,\n                                          onPress: () async {\n                                            _addNoteController.clear();\n                                            await AttendanceScreenWidgets.showNoteAlertWidget(\n                                              context: context,\n                                              controller: _addNoteController,\n                                              formKey: _formKey,\n                                            );\n                                          },\n                                        ),\n                                        SizedBox(width: 10.0.w),\n                                        AttendanceScreenWidgets.sessionButtonsWidget(\n                                          context: context,\n                                          image: AppImages.messageIcon,\n                                          onPress: () async {\n                                            await AttendanceScreenWidgets.showSendNewMessageAlertWidget(\n                                              context: context,\n                                              controller:\n                                                  _addFirstMessageController,\n                                              formKey: _formKey,\n                                              attendanceID: int.parse(\n                                                attendanceData[index]\n                                                    .attendanceID!,\n                                              ),\n                                            );\n                                          },\n                                        ),\n                                        SizedBox(width: 10.0.w),\n                                        AttendanceScreenWidgets.sessionButtonsWidget(\n                                          context: context,\n                                          image: AppImages.videoIcon,\n                                          onPress: () {\n                                            Navigator.pushNamed(\n                                              context,\n                                              PATHS\n                                                  .requestMeetingScreenAttendance,\n                                              arguments:\n                                                  RequestMeetingScreenAttendance(\n                                                    attendanceDetails:\n                                                        attendanceData[index],\n                                                  ),\n                                            );\n                                          },\n                                        ),\n                                      ],\n                                    ),\n                                    TextButton(\n                                      onPressed: () {\n                                        Navigator.pushNamed(\n                                          context,\n                                          PATHS.attendanceDetailsScreen,\n                                          arguments: AttendanceDetailsScreen(\n                                            attendanceDetails:\n                                                attendanceData[index],\n                                          ),\n                                        );\n                                      },\n                                      style: TextButton.styleFrom(\n                                        foregroundColor:\n                                            AppColors.midLevelGreenColor,\n                                        padding: const EdgeInsets.all(0.0),\n                                        tapTargetSize:\n                                            MaterialTapTargetSize.shrinkWrap,\n                                        minimumSize: Size(27.0.w, 17.0.h),\n                                        textStyle: TextStyle(\n                                          fontSize: 12.0.sp,\n                                          fontWeight: FontWeight.bold,\n                                          decoration: TextDecoration.underline,\n                                          height: 1.0.h,\n                                        ),\n                                      ),\n                                      child: const Text(\"View\"),\n                                    ),\n                                  ],\n                                ),\n                              ],\n                            ),\n                          ),\n                        ),\n                      ],\n                    ),\n                  );\n                }\n              },\n            ),\n          ),\n        ),\n      ),\n    );\n  }\n}\n", "baseTimestamp": 1755102596275}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/screens/home_screens/home_categories_screens/attendance_screens/attendance_details_screen.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/screens/home_screens/home_categories_screens/attendance_screens/attendance_details_screen.dart", "baseContent": "import 'package:afa_app/app_config/app_colors.dart';\nimport 'package:afa_app/app_config/app_images.dart';\nimport 'package:afa_app/app_config/common_components.dart';\nimport 'package:afa_app/app_config/routes.dart';\nimport 'package:afa_app/models/home_screens_models/attendance_screens_models/attendance_screen_model.dart';\nimport 'package:afa_app/screens/home_screens/home_categories_screens/attendance_screens/request_meeting_screen_attendance.dart';\nimport 'package:afa_app/widgets/home_screens_widgets.dart/attendance_screen_widgets.dart';\nimport 'package:flutter/material.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\n\nclass AttendanceDetailsScreen extends StatefulWidget {\n  const AttendanceDetailsScreen({super.key, this.attendanceDetails});\n  final AttendanceScreenModel? attendanceDetails;\n\n  @override\n  State<AttendanceDetailsScreen> createState() =>\n      _AttendanceDetailsScreenState();\n}\n\nclass _AttendanceDetailsScreenState extends State<AttendanceDetailsScreen> {\n  final TextEditingController _addNoteController = TextEditingController();\n  final TextEditingController _addFirstMessageController =\n      TextEditingController();\n  final _formKey = GlobalKey<FormState>();\n  @override\n  void dispose() {\n    _addNoteController.dispose();\n    _addFirstMessageController.dispose();\n    super.dispose();\n  }\n\n  @override\n  Widget build(BuildContext context) {\n    final AttendanceDetailsScreen args =\n        ModalRoute.of(context)!.settings.arguments as AttendanceDetailsScreen;\n    return Scaffold(\n      body: SafeArea(\n        child: SingleChildScrollView(\n          padding: EdgeInsets.all(10.0.h),\n          physics: const BouncingScrollPhysics(),\n          child: Column(\n            children: [\n              Container(\n                padding: EdgeInsets.all(10.0.h),\n                decoration: BoxDecoration(\n                  border: Border.all(color: AppColors.greyColor),\n                  borderRadius: BorderRadius.all(Radius.circular(16.0.r)),\n                ),\n                child: Column(\n                  children: [\n                    Row(\n                      mainAxisAlignment: MainAxisAlignment.spaceBetween,\n                      crossAxisAlignment: CrossAxisAlignment.start,\n                      children: [\n                        Row(\n                          mainAxisAlignment: MainAxisAlignment.start,\n                          crossAxisAlignment: CrossAxisAlignment.start,\n\n                          children: [\n                            ClipOval(\n                              child: CommonComponents.imageWithNetworkCache(\n                                context: context,\n                                image: args.attendanceDetails!.userImage!,\n                                height: 40.0.h,\n                                width: 40.0.w,\n                                fit: BoxFit.contain,\n                              ),\n                            ),\n                            SizedBox(width: 5.0.w),\n                            Column(\n                              crossAxisAlignment: CrossAxisAlignment.start,\n                              children: [\n                                Text(\n                                  args.attendanceDetails!.userName!,\n                                  style: TextStyle(\n                                    fontSize: 14.0.sp,\n                                    color: AppColors.blackColor,\n                                    fontWeight: FontWeight.bold,\n                                  ),\n                                ),\n                                // SizedBox(height: 10.0.h),\n                                Container(\n                                  padding: EdgeInsets.symmetric(\n                                    horizontal: 15.0.w,\n                                    vertical: 7.0.h,\n                                  ),\n                                  alignment: Alignment.center,\n\n                                  decoration: BoxDecoration(\n                                    borderRadius: BorderRadius.all(\n                                      Radius.circular(16.0.r),\n                                    ),\n                                    color: AppColors.lightGreenColor,\n                                  ),\n                                  child: Text(\n                                    args.attendanceDetails!.categorySelected!,\n                                    style: TextStyle(\n                                      fontSize: 14.0.sp,\n                                      color: AppColors.midLevelGreenColor,\n                                    ),\n                                  ),\n                                ),\n                              ],\n                            ),\n                          ],\n                        ),\n                        Container(\n                          alignment: Alignment.center,\n                          padding: EdgeInsets.all(10.0.h),\n                          decoration: const BoxDecoration(\n                            shape: BoxShape.circle,\n                            color: AppColors.lightgreyColor,\n                          ),\n                          child: Icon(\n                            Icons.bookmark_border,\n                            size: 15.0.h,\n                            color: AppColors.midLevelGreenColor,\n                          ),\n                        ),\n                      ],\n                    ),\n                    SizedBox(height: 10.0.h),\n                    AttendanceScreenWidgets.sessionWidgetFileds(\n                      context: context,\n                      image: AppImages.jobIcon,\n                      title: \"Job :\",\n                      subTitle: args.attendanceDetails!.jobName!,\n                    ),\n                    SizedBox(height: 10.0.h),\n                    AttendanceScreenWidgets.sessionWidgetFileds(\n                      context: context,\n                      image: AppImages.companyIcon,\n                      title: \"Company:\",\n                      subTitle: args.attendanceDetails!.company!,\n                    ),\n                    SizedBox(height: 10.0.h),\n                    AttendanceScreenWidgets.sessionWidgetFileds(\n                      context: context,\n                      image: AppImages.countryIcon,\n                      title: \"Country :\",\n                      subTitle: args.attendanceDetails!.country!,\n                    ),\n                    SizedBox(height: 20.0.h),\n                    const Divider(color: AppColors.greyColor),\n                    Row(\n                      mainAxisAlignment: MainAxisAlignment.spaceBetween,\n                      children: [\n                        Row(\n                          children: [\n                            AttendanceScreenWidgets.sessionButtonsWidget(\n                              context: context,\n                              image: AppImages.noteIcon,\n                              onPress: () async {\n                                _addNoteController.clear();\n                                await AttendanceScreenWidgets.showNoteAlertWidget(\n                                  context: context,\n                                  controller: _addNoteController,\n                                  formKey: _formKey,\n                                );\n                              },\n                            ),\n                            SizedBox(width: 10.0.w),\n                            AttendanceScreenWidgets.sessionButtonsWidget(\n                              context: context,\n                              image: AppImages.messageIcon,\n                              onPress: () async {\n                                await AttendanceScreenWidgets.showSendNewMessageAlertWidget(\n                                  context: context,\n                                  controller: _addFirstMessageController,\n                                  formKey: _formKey,\n                                  attendanceID: int.parse(\n                                    args.attendanceDetails!.attendanceID!,\n                                  ),\n                                );\n                              },\n                            ),\n                            SizedBox(width: 10.0.w),\n                            AttendanceScreenWidgets.sessionButtonsWidget(\n                              context: context,\n                              image: AppImages.videoIcon,\n                              onPress: () {\n                                Navigator.pushNamed(\n                                  context,\n                                  PATHS.requestMeetingScreenAttendance,\n                                  arguments: RequestMeetingScreenAttendance(\n                                    attendanceDetails: args.attendanceDetails,\n                                  ),\n                                );\n                              },\n                            ),\n                          ],\n                        ),\n                        // TextButton(\n                        //   onPressed: () {},\n                        //   style: TextButton.styleFrom(\n                        //     foregroundColor: AppColors.midLevelGreenColor,\n                        //     padding: const EdgeInsets.all(0.0),\n                        //     tapTargetSize: MaterialTapTargetSize.shrinkWrap,\n                        //     minimumSize: Size(27.0.w, 17.0.h),\n                        //     textStyle: TextStyle(\n                        //       fontSize: 12.0.sp,\n                        //       fontWeight: FontWeight.bold,\n                        //       decoration: TextDecoration.underline,\n                        //       height: 1.0.h,\n                        //     ),\n                        //   ),\n                        //   child: const Text(\"View\"),\n                        // ),\n                      ],\n                    ),\n                  ],\n                ),\n              ),\n              SizedBox(height: 10.0.h),\n              Container(\n                padding: EdgeInsets.all(10.0.h),\n                decoration: BoxDecoration(\n                  border: Border.all(color: AppColors.greyColor),\n                  borderRadius: BorderRadius.all(Radius.circular(24.0.r)),\n                ),\n                child: Column(\n                  crossAxisAlignment: CrossAxisAlignment.start,\n                  children: [\n                    Row(\n                      mainAxisAlignment: MainAxisAlignment.start,\n                      children: [\n                        CommonComponents.imageAssetWithCache(\n                          context: context,\n                          image: AppImages.aboutIcon,\n                          height: 16.0.h,\n                          width: 16.0.w,\n                          fit: BoxFit.contain,\n                        ),\n                        SizedBox(width: 5.0.w),\n                        Text(\n                          \"About :\",\n                          style: TextStyle(\n                            fontSize: 12.0.sp,\n                            color: AppColors.greyColor,\n                          ),\n                        ),\n                      ],\n                    ),\n                    SizedBox(height: 10.0.h),\n                    Text(\n                      args.attendanceDetails!.biography!,\n                      style: TextStyle(\n                        fontSize: 10.0.sp,\n                        fontWeight: FontWeight.bold,\n                        color: AppColors.textColor,\n                      ),\n                    ),\n                    SizedBox(height: 10.0.h),\n                    const Divider(color: AppColors.greyColor),\n                    Row(\n                      mainAxisAlignment: MainAxisAlignment.start,\n                      children: [\n                        CommonComponents.imageAssetWithCache(\n                          context: context,\n                          image: AppImages.articeIcon,\n                          height: 16.0.h,\n                          width: 16.0.w,\n                          fit: BoxFit.contain,\n                        ),\n                        SizedBox(width: 5.0.w),\n                        Text(\n                          \"Speaking at :\",\n                          style: TextStyle(\n                            fontSize: 12.0.sp,\n                            color: AppColors.greyColor,\n                          ),\n                        ),\n                      ],\n                    ),\n                    SizedBox(height: 10.0.h),\n                    Text(\n                      args.attendanceDetails!.speakingAt!,\n                      style: TextStyle(\n                        fontSize: 10.0.sp,\n                        fontWeight: FontWeight.bold,\n                        color: AppColors.textColor,\n                      ),\n                    ),\n                    SizedBox(height: 10.0.h),\n                    const Divider(color: AppColors.greyColor),\n                    Row(\n                      mainAxisAlignment: MainAxisAlignment.start,\n                      children: [\n                        CommonComponents.imageAssetWithCache(\n                          context: context,\n                          image: AppImages.expierenceIcon,\n                          height: 16.0.h,\n                          width: 16.0.w,\n                          fit: BoxFit.contain,\n                        ),\n                        SizedBox(width: 5.0.w),\n                        Text(\n                          \"Experience :\",\n                          style: TextStyle(\n                            fontSize: 12.0.sp,\n                            color: AppColors.greyColor,\n                          ),\n                        ),\n                      ],\n                    ),\n                    SizedBox(height: 10.0.h),\n\n                    ListView.separated(\n                      shrinkWrap: true,\n                      physics: const BouncingScrollPhysics(),\n                      separatorBuilder: (context, index) =>\n                          SizedBox(height: 10.0.h),\n                      itemCount: args.attendanceDetails!.experiences!.length,\n                      itemBuilder: (context, index) => Row(\n                        children: [\n                          CommonComponents.imageWithNetworkCache(\n                            image:\n                                args\n                                    .attendanceDetails!\n                                    .experiences![index]\n                                    .companyImage ??\n                                CommonComponents.imageNotFound,\n                            context: context,\n                            height: 24.0.h,\n                            width: 24.0.w,\n                            fit: BoxFit.contain,\n                          ),\n\n                          SizedBox(width: 5.0.w),\n                          Text(\n                            args\n                                .attendanceDetails!\n                                .experiences![index]\n                                .companyName!,\n                            style: TextStyle(\n                              fontSize: 10.0.sp,\n                              color: AppColors.blackColor,\n                            ),\n                          ),\n                        ],\n                      ),\n                    ),\n                    const Divider(color: AppColors.greyColor),\n\n                    Row(\n                      mainAxisAlignment: MainAxisAlignment.start,\n                      children: [\n                        CommonComponents.imageAssetWithCache(\n                          context: context,\n                          image: AppImages.linksIcon,\n                          height: 16.0.h,\n                          width: 16.0.w,\n                          fit: BoxFit.contain,\n                        ),\n                        SizedBox(width: 5.0.w),\n                        Text(\n                          \"Links :\",\n                          style: TextStyle(\n                            fontSize: 12.0.sp,\n                            color: AppColors.greyColor,\n                          ),\n                        ),\n                      ],\n                    ),\n                    SizedBox(height: 10.0.h),\n                    ListView.separated(\n                      shrinkWrap: true,\n                      physics: const BouncingScrollPhysics(),\n                      separatorBuilder: (context, index) =>\n                          SizedBox(height: 10.0.h),\n                      itemCount: args.attendanceDetails!.socialLinks!.length,\n                      itemBuilder: (context, index) => Row(\n                        mainAxisAlignment: MainAxisAlignment.spaceBetween,\n                        children: [\n                          Text(\n                            args.attendanceDetails!.socialLinks![index].title!,\n                            style: TextStyle(fontSize: 10.0.sp),\n                          ),\n                          InkWell(\n                            onTap: () async {\n                              await CommonComponents.launchOnBrowser(\n                                context: context,\n                                url: args\n                                    .attendanceDetails!\n                                    .socialLinks![index]\n                                    .url!,\n                              );\n                            },\n                            child: CommonComponents.imageAssetWithCache(\n                              context: context,\n                              image: AppImages.shareIcon,\n                              height: 20.0.h,\n                              width: 20.0.w,\n                              fit: BoxFit.contain,\n                            ),\n                          ),\n                        ],\n                      ),\n                    ),\n                  ],\n                ),\n              ),\n            ],\n          ),\n        ),\n      ),\n    );\n  }\n}\n", "baseTimestamp": 1755102601091}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/providers/chat_providers_apis/new_message_providers_apis.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/providers/chat_providers_apis/new_message_providers_apis.dart", "baseContent": "import 'dart:convert';\n\nimport 'package:afa_app/app_config/api_keys.dart';\nimport 'package:afa_app/app_config/api_providers/chat_providers.dart';\nimport 'package:afa_app/app_config/api_requests.dart';\nimport 'package:afa_app/app_config/common_components.dart';\nimport 'package:afa_app/app_config/routes.dart';\nimport 'package:flutter/material.dart';\nimport 'package:riverpod_context/riverpod_context.dart';\n\nclass NewMessageProvidersApis extends ChangeNotifier {\n  Future<void> sendNewMessage({\n    required BuildContext context,\n    required int recipientId,\n    required String message,\n  }) async {\n    if (!context.mounted) return;\n\n    // Check if a thread already exists with this user\n    final existingThreadId = context\n        .read(ChatProviders.inboxProvidersApis)\n        .getExistingThreadId(recipientId);\n\n    if (existingThreadId != null) {\n      // Use existing thread - send message to existing thread\n      await _sendToExistingThread(\n        context: context,\n        threadId: existingThreadId,\n        message: message,\n      );\n    } else {\n      // Create new thread\n      await _createNewThread(\n        context: context,\n        recipientId: recipientId,\n        message: message,\n      );\n    }\n  }\n\n  /// Send message to existing thread\n  Future<void> _sendToExistingThread({\n    required BuildContext context,\n    required int threadId,\n    required String message,\n  }) async {\n    if (!context.mounted) return;\n\n    // Set the thread ID and navigate to chat\n    context.read(ChatProviders.inboxProvidersApis).setThreadID(threadId);\n\n    // Send the message to the existing thread\n    await context\n        .read(ChatProviders.chatProvidersApis)\n        .sendMessage(context: context, threadId: threadId, message: message);\n\n    if (!context.mounted) return;\n    Navigator.pop(context);\n    Navigator.pushNamed(context, PATHS.chatScreen);\n  }\n\n  /// Create new thread with recipient\n  Future<void> _createNewThread({\n    required BuildContext context,\n    required int recipientId,\n    required String message,\n  }) async {\n    final String myToken = await CommonComponents.getSavedData(\n      ApiKeys.userToken,\n    );\n\n    if (!context.mounted) return;\n\n    final Map<String, dynamic>? data = await ApiRequests.postApiRequest(\n      context: context,\n      baseUrl: ApiKeys.baseUrl,\n      apiUrl: \"wp-json/better-messages/v1/thread/new\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        \"Authorization\": \"Bearer $myToken\",\n      },\n      body: json.encode({\n        \"recipients\": [recipientId.toString()],\n        \"message\": message,\n        \"subject\": message,\n      }),\n    );\n\n    if (data != null) {\n      if (!context.mounted) return;\n      final threadId = data['thread_id'];\n\n      // Update thread cache with new thread\n      context\n          .read(ChatProviders.inboxProvidersApis)\n          ._updateThreadCache(recipientId, threadId);\n\n      context.read(ChatProviders.inboxProvidersApis).setThreadID(threadId);\n      Navigator.pop(context);\n      Navigator.pushNamed(context, PATHS.chatScreen);\n    } else {\n      debugPrint(\"ERROR WITH NewMessageProvidersApis FUNCTION\");\n    }\n  }\n}\n", "baseTimestamp": 1755103083747, "deltas": [{"timestamp": 1755103117490, "changes": [{"type": "MODIFY", "lineNumber": 95, "content": "          .updateThreadCache(recipientId, threadId);", "oldContent": "          ._updateThreadCache(recipientId, threadId);"}]}, {"timestamp": 1755103147828, "changes": [{"type": "INSERT", "lineNumber": 18, "content": "    // Refresh thread cache to ensure we have latest data"}, {"type": "INSERT", "lineNumber": 19, "content": "    await context"}, {"type": "INSERT", "lineNumber": 20, "content": "        .read(ChatProviders.inboxProvidersApis)"}, {"type": "INSERT", "lineNumber": 21, "content": "        .refreshThreadCache(context);"}, {"type": "INSERT", "lineNumber": 22, "content": ""}, {"type": "INSERT", "lineNumber": 23, "content": "    if (!context.mounted) return;"}, {"type": "INSERT", "lineNumber": 24, "content": ""}]}]}}}