import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/app_images.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../models/home_screens_models/sessions_q&a_models/answers_model.dart';

class QuestionsScreenWidgets {
  static Future showAnswerAlertWidget({
    required BuildContext context,
    required List<AnswersModel> answersList,
  }) async {
    return await showGeneralDialog(
      context: context,
      pageBuilder: (context, animation1, animation2) => Container(),
      transitionDuration: const Duration(milliseconds: 400),
      transitionBuilder: (context, animation1, animation2, child) =>
          ScaleTransition(
            scale: animation1,
            child: AlertDialog(
              contentPadding: EdgeInsets.all(10.0.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20.0),
              ),
              backgroundColor: Colors.white,
              title: Center(
                child: Text(
                  "Answers",
                  style: TextStyle(
                    fontSize: 20.0.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.blackColor,
                  ),
                ),
              ),
              content: SizedBox(
                height: 200.0.h,
                width: double.maxFinite,
                child: Column(
                  children: [
                    Expanded(
                      child: ListView.separated(
                        separatorBuilder: (context, index) =>
                            SizedBox(height: 10.0.h),
                        itemCount: answersList.length,
                        itemBuilder: (context, index) => Container(
                          padding: EdgeInsets.all(10.0.h),
                          decoration: BoxDecoration(
                            border: Border.all(color: AppColors.greyColor),
                            borderRadius: BorderRadius.all(
                              Radius.circular(16.0.r),
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  ClipOval(
                                    child: answersList[index].userImage != null
                                        ? CommonComponents.imageWithNetworkCache(
                                            context: context,
                                            image:
                                                answersList[index].userImage!,
                                            height: 40.0.h,
                                            width: 40.0.w,
                                            fit: BoxFit.contain,
                                          )
                                        : CommonComponents.imageAssetWithCache(
                                            context: context,
                                            image: AppImages.userPhoto,
                                            height: 40.0.h,
                                            width: 40.0.w,
                                            fit: BoxFit.contain,
                                          ),
                                  ),
                                  SizedBox(width: 5.0.w),
                                  Text(
                                    answersList[index].userName!,
                                    style: TextStyle(
                                      fontSize: 14.0.sp,
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.blackColor,
                                    ),
                                  ),
                                ],
                              ),
                              const Divider(color: AppColors.greyColor),
                              Text(
                                answersList[index].answer!,
                                style: TextStyle(
                                  fontSize: 12.0.sp,
                                  color: AppColors.blackColor,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      style: ElevatedButton.styleFrom(
                        foregroundColor: Colors.white,
                        backgroundColor: AppColors.midLevelGreenColor,
                        textStyle: TextStyle(
                          fontSize: 16.0.sp,
                          fontWeight: FontWeight.bold,
                        ),
                        minimumSize: Size(398.0.w, 46.0.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.all(
                            Radius.circular(32.0.r),
                          ),
                        ),
                      ),
                      child: const Text("OK"),
                    ),
                  ],
                ),
              ),
            ),
          ),
    );
  }
}
