import 'package:afa_app/app_config/api_providers/chat_providers.dart';
import 'package:afa_app/app_config/api_providers/profile_providers.dart';
import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/models/home_screens_models/attendance_screens_models/attendance_categories_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

class AttendanceScreenWidgets {
  static Widget bublesWidget({
    required String title,
    required Color containerColor,
    required Color textColor,
    required double height,
    required double width,
    required double top,
    required double left,
  }) {
    return Container(
      height: height,
      width: width,
      decoration: BoxDecoration(
        color: containerColor,
        border: Border.all(color: containerColor),
        shape: BoxShape.circle,
      ),
      child: Center(
        child: Text(
          title,
          style: TextStyle(
            fontSize: 18.0.sp,
            fontWeight: FontWeight.bold,
            color: textColor,
          ),
        ),
      ),
    );
  }

  static Widget headerAttendanceWidget({
    required List<AttendanceCategoriesModel> attendanceCategories,
  }) {
    return GridView.builder(
      shrinkWrap: true,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        mainAxisExtent: 90.0.h,
        crossAxisSpacing: 10.0.w,
        mainAxisSpacing: 10.0.h,
      ),
      itemCount: attendanceCategories.length,
      itemBuilder: (context, index) => AttendanceScreenWidgets.bublesWidget(
        title: attendanceCategories[index].count.toString(),
        containerColor: attendanceCategories[index].color!,
        textColor: Colors.white,
        height: 90.0.h,
        width: 90.0.w,
        top: 40.0.h,
        left: 0.0.w,
      ),
    );
  }

  static Widget statisticsWidget({
    required String title,
    required String subTitle,
    required Color color,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: 15.0.h,
          width: 8.0.w,
          decoration: BoxDecoration(color: color, shape: BoxShape.circle),
        ),
        SizedBox(width: 5.0.w),
        Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,

          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 10.0.sp,
                fontWeight: FontWeight.bold,
                color: AppColors.blackColor,
              ),
            ),
            SizedBox(height: 5.0.h),
            Text(
              subTitle,
              style: TextStyle(
                fontSize: 10.0.sp,
                fontWeight: FontWeight.bold,
                color: AppColors.blackColor,
              ),
            ),
          ],
        ),
      ],
    );
  }

  static Widget sessionWidgetFileds({
    required BuildContext context,
    required String image,
    required String title,
    required String subTitle,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            CommonComponents.imageAssetWithCache(
              context: context,
              image: image,
              height: 16.0.h,
              width: 16.0.w,
              fit: BoxFit.contain,
            ),
            SizedBox(width: 10.0.w),
            Text(
              title,
              style: TextStyle(
                fontSize: 12.0.sp,
                color: AppColors.greyColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        Expanded(
          child: Text(
            subTitle,
            style: TextStyle(
              fontSize: 12.0.sp,
              color: AppColors.midLevelGreenColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  static Widget sessionButtonsWidget({
    required BuildContext context,
    required String image,
    required Function() onPress,
  }) {
    return InkWell(
      onTap: onPress,
      child: Container(
        alignment: Alignment.center,
        padding: EdgeInsets.all(10.0.h),
        decoration: const BoxDecoration(
          color: AppColors.lightGreenColor,
          shape: BoxShape.circle,
        ),
        child: CommonComponents.imageAssetWithCache(
          context: context,
          image: image,
          height: 16.0.h,
          width: 16.0.w,
          fit: BoxFit.contain,
        ),
      ),
    );
  }

  static Future showNoteAlertWidget({
    required BuildContext context,
    required TextEditingController controller,
    required GlobalKey<FormState> formKey,
  }) async {
    return await showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: "",
      pageBuilder: (context, animation1, animation2) => Container(),
      transitionDuration: const Duration(milliseconds: 400),
      transitionBuilder: (context, animation1, animation2, child) =>
          ScaleTransition(
            scale: animation1,
            child: AlertDialog(
              contentPadding: EdgeInsets.all(10.0.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20.0),
              ),
              backgroundColor: Colors.white,
              title: Center(
                child: Text(
                  "Add A Note",
                  style: TextStyle(
                    fontSize: 20.0.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.blackColor,
                  ),
                ),
              ),
              content: Form(
                key: formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      "Note",
                      style: TextStyle(
                        fontSize: 14.0.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 5.0.h),
                    TextFormField(
                      controller: controller,
                      validator: (value) =>
                          value!.isEmpty ? "Please Enter Note" : null,
                      maxLines: 3,
                      style: TextStyle(
                        fontSize: 12.0.sp,
                        fontWeight: FontWeight.bold,
                      ),
                      decoration: InputDecoration(
                        hintText: "Enter",
                        hintStyle: TextStyle(
                          fontSize: 12.0.sp,
                          fontWeight: FontWeight.bold,
                          color: AppColors.greyColor,
                        ),
                        border: OutlineInputBorder(
                          borderSide: const BorderSide(
                            color: AppColors.greyColor,
                          ),
                          borderRadius: BorderRadius.all(
                            Radius.circular(16.0.r),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: 25.0.h),
                    ElevatedButton(
                      onPressed: () async {
                        if (formKey.currentState!.validate()) {
                          await context
                              .read(ProfileProviders.commonApis)
                              .sendNote(
                                context: context,
                                note: controller.text,
                              );
                          controller.clear();
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        foregroundColor: Colors.white,
                        backgroundColor: AppColors.midLevelGreenColor,
                        textStyle: TextStyle(
                          fontSize: 16.0.sp,
                          fontWeight: FontWeight.bold,
                        ),
                        minimumSize: Size(398.0.w, 46.0.h),
                      ),
                      child: const Text("Save"),
                    ),
                  ],
                ),
              ),
            ),
          ),
    );
  }

  static Future showSendNewMessageAlertWidget({
    required BuildContext context,
    required TextEditingController controller,
    required GlobalKey<FormState> formKey,
    required int attendanceID,
  }) async {
    // Load thread cache when dialog opens
    context
        .read(ChatProviders.inboxProvidersApis)
        .loadThreadCacheForScreen(context);

    return await showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: "",
      pageBuilder: (context, animation1, animation2) => Container(),
      transitionDuration: const Duration(milliseconds: 400),
      transitionBuilder: (context, animation1, animation2, child) =>
          ScaleTransition(
            scale: animation1,
            child: AlertDialog(
              contentPadding: EdgeInsets.all(10.0.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20.0),
              ),
              backgroundColor: Colors.white,
              title: Center(
                child: Text(
                  "Send First Message",
                  style: TextStyle(
                    fontSize: 20.0.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.blackColor,
                  ),
                ),
              ),
              content: Form(
                key: formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      "Message",
                      style: TextStyle(
                        fontSize: 14.0.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 5.0.h),
                    TextFormField(
                      controller: controller,
                      validator: (value) =>
                          value!.isEmpty ? "Please Enter First Message" : null,
                      // maxLines: 3,
                      style: TextStyle(
                        fontSize: 12.0.sp,
                        fontWeight: FontWeight.bold,
                      ),
                      decoration: InputDecoration(
                        hintText: "Enter",
                        hintStyle: TextStyle(
                          fontSize: 12.0.sp,
                          fontWeight: FontWeight.bold,
                          color: AppColors.greyColor,
                        ),
                        border: OutlineInputBorder(
                          borderSide: const BorderSide(
                            color: AppColors.greyColor,
                          ),
                          borderRadius: BorderRadius.all(
                            Radius.circular(16.0.r),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: 25.0.h),
                    ElevatedButton(
                      onPressed: () async {
                        if (formKey.currentState!.validate()) {
                          await context
                              .read(ChatProviders.newMessageProvidersApis)
                              .sendNewMessage(
                                context: context,
                                recipientId: attendanceID,
                                message: controller.text,
                              );
                          controller.clear();
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        foregroundColor: Colors.white,
                        backgroundColor: AppColors.midLevelGreenColor,
                        textStyle: TextStyle(
                          fontSize: 16.0.sp,
                          fontWeight: FontWeight.bold,
                        ),
                        minimumSize: Size(398.0.w, 46.0.h),
                      ),
                      child: const Text("Send"),
                    ),
                  ],
                ),
              ),
            ),
          ),
    ).then((value) {
      controller.clear();
    });
  }
}
