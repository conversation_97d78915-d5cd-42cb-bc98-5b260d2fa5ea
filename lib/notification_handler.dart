import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

class NotificationHandler {
  static final _notification = FlutterLocalNotificationsPlugin();
  static Future<void> init() async {
    await FirebaseMessaging.instance.requestPermission();
    _notification.initialize(
      const InitializationSettings(
        android: AndroidInitializationSettings('@mipmap/ic_launcher'),
        iOS: DarwinInitializationSettings(),
      ),
      onDidReceiveNotificationResponse: (data) {},
    );
  }

  static Future<void> pushNotification(RemoteMessage message) async {
    const androidNotificationDetails = AndroidNotificationDetails(
      "high_importance_channelss",
      'High Importance Notifications',
      channelDescription: 'channel description',
      importance: Importance.max,
      priority: Priority.high,
      enableVibration: true,
      playSound: true,
    );

    const darwinNotificationDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const notificationDetails = NotificationDetails(
      android: androidNotificationDetails,
      iOS: darwinNotificationDetails,
    );

    await _notification.show(
      0,
      message.notification!.title,
      message.notification!.body,
      notificationDetails,
    );
  }
}
