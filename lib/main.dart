import 'dart:io';
import 'package:afa_app/app_config/routes.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

import 'notification_handler.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: FirebaseOptions(
      apiKey: Platform.isAndroid
          ? "AIzaSyCE9JqVCG_Whw3SY9pQ0S31GGPI2h1ajyc"
          : "AIzaSyB5Ofub3ytPQElCglUR49t3kMdfsgL3xus",
      appId: Platform.isAndroid
          ? "1:686384903399:android:1ed0ac4fd0502ce2145e42"
          : "1:686384903399:ios:b8d3ee2d026ce2ed145e42",
      messagingSenderId: "686384903399",
      projectId: "arab-fertilizer",
    ),
  );

  try {
    await NotificationHandler.init();
  } catch (e) {
    debugPrint("NotificationHandler.init() error::=>$e");
  }

  runApp(const ProviderScope(child: InheritedConsumer(child: AfaApp())));
}

class AfaApp extends StatefulWidget {
  const AfaApp({super.key});

  @override
  State<AfaApp> createState() => _AfaAppState();
}

class _AfaAppState extends State<AfaApp> {
  @override
  Widget build(BuildContext context) {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    debugInvertOversizedImages = true;
    return ScreenUtilInit(
      designSize: const Size(430, 932),
      minTextAdapt: true,
      builder: (context, child) => MaterialApp(
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          fontFamily: "Roboto",
          scaffoldBackgroundColor: Colors.white,
        ),
        initialRoute: PATHS.splashScreen,
        routes: routes,
      ),
    );
  }
}
