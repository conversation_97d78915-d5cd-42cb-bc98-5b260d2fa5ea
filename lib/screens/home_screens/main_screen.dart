import 'package:afa_app/app_config/api_keys.dart';
import 'package:afa_app/app_config/api_providers/home_screen_categories_providers.dart';
import 'package:afa_app/app_config/api_providers/profile_providers.dart';
import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/app_config/routes.dart';
import 'package:afa_app/notification_handler.dart';
import 'package:afa_app/screens/home_screens/announcement_screen.dart';
import 'package:afa_app/screens/home_screens/home_screen.dart';
import 'package:afa_app/screens/home_screens/messages_screen.dart';
import 'package:afa_app/screens/home_screens/polls_screen.dart';
import 'package:afa_app/widgets/home_screens_widgets.dart/main_screen_widgets.dart';
import 'package:badges/badges.dart' as badges;
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

// Here the _messageHandler callback, will be called when application is in background or terminated state
@pragma('vm:entry-point')
Future<void> _messageHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
}

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  final List<Widget> _pages = const [
    HomeScreen(),
    AnnouncementScreen(),
    PollsScreen(),
    MessagesScreen(),
  ];

  String? userImage, userName, userPosition;

  @override
  void initState() {
    //USE FIREBASE TOKEN TO REGITSER DEVICE TO FIREBASE MESSAGING AND EVERY TIME RUN APP THIS TOKEN CHANGED.

    FirebaseMessaging.instance.getToken().then((value) => debugPrint(value));

    // // execute navigation when app in background
    FirebaseMessaging.onMessageOpenedApp.listen((event) {
      // print(message.data['info']);
    });

    // ///THIS [onMessage] USE TO RETRIVE ALERT DIALIG WHEN APP IN FOREGROUND NOT SHOW NOTIFICATION ON STATUS BAR
    FirebaseMessaging.onMessage.listen((RemoteMessage event) async {
      NotificationHandler.init();
      await NotificationHandler.pushNotification(event);
    });

    FirebaseMessaging.onBackgroundMessage(_messageHandler);

    Future.delayed(Duration.zero, () async {
      final String userImageCached = await CommonComponents.getSavedData(
        ApiKeys.userImage,
      );
      final String userNameCached = await CommonComponents.getSavedData(
        ApiKeys.userName,
      );
      final String userPositionCached = await CommonComponents.getSavedData(
        ApiKeys.userPosition,
      );
      if (!mounted) return;

      setState(() {
        userName = userNameCached;
        userImage = userImageCached;
        userPosition = userPositionCached;
      });
    });

    super.initState();
  }

  @override
  void didChangeDependencies() {
    context
        .read(HomeScreenCategoriesProviders.notificationsScreenProvidersApis)
        .getAllNotifications(context: context, showBadgeWithHome: true);
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    if (userName == null || userImage == null || userPosition == null) {
      return Center(child: CommonComponents.loadingDataFromServer());
    }
    return PopScope(
      onPopInvokedWithResult: (value, popInvokedBy) {
        context.read(ProfileProviders.commonApis).setCurrentIndex(0);
      },
      canPop: false,
      child: Consumer(
        builder: (context, watch, child) => Scaffold(
          bottomNavigationBar: BottomNavigationBar(
            selectedFontSize: 12.0.sp,
            unselectedFontSize: 12.0.sp,
            selectedItemColor: AppColors.midLevelGreenColor,
            unselectedItemColor: AppColors.greyColor,
            selectedLabelStyle: const TextStyle(fontWeight: FontWeight.bold),
            unselectedLabelStyle: const TextStyle(fontWeight: FontWeight.bold),
            type: BottomNavigationBarType.fixed,
            onTap: (value) {
              context.read(ProfileProviders.commonApis).setCurrentIndex(value);
            },
            currentIndex: watch
                .watch(ProfileProviders.commonApis)
                .currentIndexForMainScreen,
            items: MainScreenWidgets.bottomBarItemsList
                .map(
                  (items) => BottomNavigationBarItem(
                    activeIcon: CommonComponents.imageAssetWithCache(
                      context: context,
                      image: items['active_icon'],
                      height: 24.0.h,
                      width: 24.0.w,
                      fit: BoxFit.contain,
                    ),
                    icon: CommonComponents.imageAssetWithCache(
                      context: context,
                      image: items['icon'],
                      height: 24.0.h,
                      width: 24.0.w,
                      fit: BoxFit.contain,
                    ),
                    label: items['title'],
                  ),
                )
                .toList(),
          ),
          body: SafeArea(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(15.0.h),
              physics: const BouncingScrollPhysics(),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      InkWell(
                        onTap: () {
                          Navigator.pushNamed(context, PATHS.profileScreen);
                        },
                        child: Row(
                          children: [
                            ClipOval(
                              child: CommonComponents.imageWithNetworkCache(
                                context: context,
                                image: userImage!,
                                height: 48.0.h,
                                width: 48.0.w,
                                fit: BoxFit.contain,
                              ),
                            ),
                            SizedBox(width: 10.0.w),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  userPosition!,
                                  style: TextStyle(
                                    fontSize: 14.0.sp,
                                    color: AppColors.greyColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Text(
                                  userName!,
                                  style: TextStyle(
                                    fontSize: 16.0.sp,
                                    color: AppColors.blackColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          Navigator.pushNamed(
                            context,
                            PATHS.notificationScreen,
                          );
                        },
                        child: badges.Badge(
                          showBadge:
                              watch
                                      .watch(
                                        HomeScreenCategoriesProviders
                                            .notificationsScreenProvidersApis,
                                      )
                                      .unreadNotificationCount ==
                                  0
                              ? false
                              : true,

                          badgeContent: Text(
                            watch
                                .watch(
                                  HomeScreenCategoriesProviders
                                      .notificationsScreenProvidersApis,
                                )
                                .unreadNotificationCount
                                .toString(),
                            style: TextStyle(
                              fontSize: 14.0.sp,
                              color: Colors.white,
                            ),
                          ),
                          badgeStyle: badges.BadgeStyle(
                            padding: EdgeInsetsGeometry.all(10.h),
                          ),
                          child: Icon(Icons.notifications, size: 48.0.h),
                        ),
                      ),
                      // InkWell(
                      //   onTap: () {
                      //     Navigator.pushNamed(context, PATHS.notificationScreen);
                      //   },
                      //   child: Container(
                      //     height: 48.0.h,
                      //     width: 48.0.w,
                      //     padding: EdgeInsets.all(10.0.h),
                      //     decoration: const BoxDecoration(
                      //       shape: BoxShape.circle,
                      //       color: AppColors.darkGreenColor,
                      //     ),
                      //     child: Icon(
                      //       Icons.notifications,
                      //       size: 27.0.h,
                      //       color: Colors.white,
                      //     ),
                      //   ),
                      // ),
                    ],
                  ),
                  SizedBox(height: 40.0.h),
                  _pages[watch
                      .watch(ProfileProviders.commonApis)
                      .currentIndexForMainScreen],
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
