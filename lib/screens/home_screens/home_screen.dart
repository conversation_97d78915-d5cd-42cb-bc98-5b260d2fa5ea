import 'dart:async';
import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/widgets/home_screens_widgets.dart/home_screen_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  Timer? timer;
  Duration? difference = const Duration();

  @override
  void initState() {
    _startTimer();
    super.initState();
  }

  void _startTimer() {
    timer = Timer.periodic(const Duration(seconds: 1), (time) {
      final now = DateTime.now();

      final targetDate = DateTime(now.year, 9, 16, 12, 0, 0);

      setState(() {
        difference = targetDate.difference(now);
      });
    });
  }

  List<Map<String, dynamic>> formatDuration(Duration d) {
    final days = d.inDays;
    final hours = d.inHours % 24;
    final minutes = d.inMinutes % 60;
    final seconds = d.inSeconds % 60;

    final List<Map<String, dynamic>> eventTimer = [
      {"time": days, "title": "Days"},
      {"time": hours, "title": "Hours"},
      {"time": minutes, "title": "Minute"},
      {"time": seconds, "title": "Seconds"},
    ];
    return eventTimer;
  }

  @override
  void dispose() {
    timer!.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Center(
          child: HomeScreenWidgets.headerScreenWidget(
            context: context,
            eventTimer: formatDuration(difference!),
          ),
        ),

        SizedBox(height: 35.0.h),
        GridView.builder(
          padding: EdgeInsets.all(10.0.h),
          shrinkWrap: true,
          itemCount: HomeScreenWidgets.homFiledsList.length,
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 4,
            mainAxisExtent: 100.0.h,
            mainAxisSpacing: 20.0.h,
          ),

          itemBuilder: (context, index) => InkWell(
            onTap: () {
              Navigator.pushNamed(
                context,
                HomeScreenWidgets.homFiledsList[index]['path'],
              );
            },
            child: Center(
              child: Column(
                children: [
                  Container(
                    padding: EdgeInsets.all(15.0.h),
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                      color: AppColors.darkGreenColor,
                    ),
                    child: CommonComponents.imageAssetWithCache(
                      context: context,
                      image: HomeScreenWidgets.homFiledsList[index]['image'],
                      height: 36.0.h,
                      width: 36.0.w,
                      fit: BoxFit.contain,
                    ),
                  ),
                  SizedBox(height: 10.0.h),
                  Text(
                    HomeScreenWidgets.homFiledsList[index]['title'],
                    style: TextStyle(
                      fontSize: 12.0.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.blackColor,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
