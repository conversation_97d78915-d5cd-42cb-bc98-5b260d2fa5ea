import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/app_images.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/app_config/routes.dart';
import 'package:afa_app/models/home_screens_models/speakers_screens_models/speeaker_screen_model.dart';
import 'package:afa_app/widgets/home_screens_widgets.dart/speakers_screen_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SpeakerDetailsScreen extends StatefulWidget {
  const SpeakerDetailsScreen({super.key, this.speakerDetails});
  final SpeakerScreenModel? speakerDetails;

  @override
  State<SpeakerDetailsScreen> createState() => _SpeakerDetailsScreenState();
}

class _SpeakerDetailsScreenState extends State<SpeakerDetailsScreen> {
  final TextEditingController _addNoteController = TextEditingController();
  final TextEditingController _addNewMessageController =
      TextEditingController();
  final _formKey = GlobalKey<FormState>();
  @override
  void dispose() {
    _addNoteController.dispose();
    _addNewMessageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final SpeakerDetailsScreen args =
        ModalRoute.of(context)!.settings.arguments as SpeakerDetailsScreen;
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(10.0.h),
          physics: const BouncingScrollPhysics(),
          child: Column(
            children: [
              Container(
                padding: EdgeInsets.all(10.0.h),
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.greyColor),
                  borderRadius: BorderRadius.all(Radius.circular(16.0.r)),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,

                          children: [
                            ClipOval(
                              child: CommonComponents.imageWithNetworkCache(
                                context: context,
                                image: args.speakerDetails!.image!,
                                height: 40.0.h,
                                width: 40.0.w,
                                fit: BoxFit.contain,
                              ),
                            ),
                            SizedBox(width: 5.0.w),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  args.speakerDetails!.name!,
                                  style: TextStyle(
                                    fontSize: 14.0.sp,
                                    color: AppColors.blackColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                // SizedBox(height: 10.0.h),
                                Container(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: 15.0.w,
                                    vertical: 7.0.h,
                                  ),
                                  alignment: Alignment.center,

                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.all(
                                      Radius.circular(16.0.r),
                                    ),
                                    color: AppColors.lightGreenColor,
                                  ),
                                  child: Text(
                                    args.speakerDetails!.category!,
                                    style: TextStyle(
                                      fontSize: 14.0.sp,
                                      color: AppColors.midLevelGreenColor,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        Container(
                          alignment: Alignment.center,
                          padding: EdgeInsets.all(10.0.h),
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            color: AppColors.lightgreyColor,
                          ),
                          child: Icon(
                            Icons.bookmark_border,
                            size: 15.0.h,
                            color: AppColors.midLevelGreenColor,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 10.0.h),
                    SpeakersScreenWidgets.sessionWidgetFileds(
                      context: context,
                      image: AppImages.jobIcon,
                      title: "Job :",
                      subTitle: args.speakerDetails!.jobTitle!,
                    ),
                    SizedBox(height: 10.0.h),
                    SpeakersScreenWidgets.sessionWidgetFileds(
                      context: context,
                      image: AppImages.companyIcon,
                      title: "Company:",
                      subTitle: args.speakerDetails!.companyName!,
                    ),
                    SizedBox(height: 10.0.h),
                    SpeakersScreenWidgets.sessionWidgetFileds(
                      context: context,
                      image: AppImages.countryIcon,
                      title: "Country :",
                      subTitle: args.speakerDetails!.country!,
                    ),
                    SizedBox(height: 20.0.h),
                    const Divider(color: AppColors.greyColor),
                    Row(
                      children: [
                        SpeakersScreenWidgets.sessionButtonsWidget(
                          context: context,
                          image: AppImages.noteIcon,
                          onPress: () async {
                            await SpeakersScreenWidgets.showNoteAlertWidget(
                              context: context,
                              controller: _addNoteController,
                              formKey: _formKey,
                            );
                          },
                        ),
                        SizedBox(width: 10.0.w),
                        SpeakersScreenWidgets.sessionButtonsWidget(
                          context: context,
                          image: AppImages.messageIcon,
                          onPress: () async {
                            // _addNewMessageController.clear();

                            await SpeakersScreenWidgets.showSendNewMessageAlertWidget(
                              context: context,
                              controller: _addNewMessageController,
                              formKey: _formKey,
                              speakerID: int.parse(
                                args.speakerDetails!.speakerID!,
                              ),
                            );
                          },
                        ),
                        SizedBox(width: 10.0.w),
                        SpeakersScreenWidgets.sessionButtonsWidget(
                          context: context,
                          image: AppImages.videoIcon,
                          onPress: () {
                            Navigator.pushNamed(
                              context,
                              PATHS.requestMeetingScreenSpeaker,
                            );
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              SizedBox(height: 10.0.h),
              Container(
                padding: EdgeInsets.all(10.0.h),
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.greyColor),
                  borderRadius: BorderRadius.all(Radius.circular(24.0.r)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        CommonComponents.imageAssetWithCache(
                          context: context,
                          image: AppImages.aboutIcon,
                          height: 16.0.h,
                          width: 16.0.w,
                          fit: BoxFit.contain,
                        ),
                        SizedBox(width: 5.0.w),
                        Text(
                          "About :",
                          style: TextStyle(
                            fontSize: 12.0.sp,
                            color: AppColors.greyColor,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 10.0.h),
                    Text(
                      args.speakerDetails!.about!,
                      style: TextStyle(
                        fontSize: 10.0.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textColor,
                      ),
                    ),
                    SizedBox(height: 10.0.h),
                    const Divider(color: AppColors.greyColor),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        CommonComponents.imageAssetWithCache(
                          context: context,
                          image: AppImages.articeIcon,
                          height: 16.0.h,
                          width: 16.0.w,
                          fit: BoxFit.contain,
                        ),
                        SizedBox(width: 5.0.w),
                        Text(
                          "Speaking at :",
                          style: TextStyle(
                            fontSize: 12.0.sp,
                            color: AppColors.greyColor,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 10.0.h),
                    Text(
                      args.speakerDetails!.speakingAt!,
                      style: TextStyle(
                        fontSize: 10.0.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textColor,
                      ),
                    ),
                    SizedBox(height: 10.0.h),
                    const Divider(color: AppColors.greyColor),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        CommonComponents.imageAssetWithCache(
                          context: context,
                          image: AppImages.expierenceIcon,
                          height: 16.0.h,
                          width: 16.0.w,
                          fit: BoxFit.contain,
                        ),
                        SizedBox(width: 5.0.w),
                        Text(
                          "Experience :",
                          style: TextStyle(
                            fontSize: 12.0.sp,
                            color: AppColors.greyColor,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 10.0.h),
                    ListView.separated(
                      shrinkWrap: true,
                      separatorBuilder: (context, index) =>
                          SizedBox(height: 10.0.h),
                      itemCount: args.speakerDetails!.experiences!.length,
                      itemBuilder: (context, index) => Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              CommonComponents.imageWithNetworkCache(
                                context: context,
                                image:
                                    args
                                        .speakerDetails!
                                        .experiences![index]
                                        .companyImage ??
                                    CommonComponents.imageNotFound,
                                height: 24.0.h,
                                width: 24.0.w,
                                fit: BoxFit.contain,
                              ),
                              SizedBox(width: 5.0.w),
                              Text(
                                args
                                    .speakerDetails!
                                    .experiences![index]
                                    .companyName!
                                    .toString(),
                                style: TextStyle(
                                  fontSize: 10.0.sp,
                                  color: AppColors.blackColor,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    const Divider(color: AppColors.greyColor),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        CommonComponents.imageAssetWithCache(
                          context: context,
                          image: AppImages.linksIcon,
                          height: 16.0.h,
                          width: 16.0.w,
                          fit: BoxFit.contain,
                        ),
                        SizedBox(width: 5.0.w),
                        Text(
                          "Links :",
                          style: TextStyle(
                            fontSize: 12.0.sp,
                            color: AppColors.greyColor,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 10.0.h),
                    ListView.separated(
                      shrinkWrap: true,
                      separatorBuilder: (context, index) =>
                          SizedBox(height: 10.0.h),
                      itemCount: args.speakerDetails!.socialMediaLinks!.length,
                      itemBuilder: (context, index) => Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            args.speakerDetails!.socialMediaLinks![index].name!,
                            style: TextStyle(fontSize: 10.0.sp),
                          ),
                          InkWell(
                            onTap: () async {
                              await CommonComponents.launchOnBrowser(
                                context: context,
                                url: args
                                    .speakerDetails!
                                    .socialMediaLinks![index]
                                    .url!,
                              );
                            },
                            child: CommonComponents.imageAssetWithCache(
                              context: context,
                              image: AppImages.shareIcon,
                              height: 20.0.h,
                              width: 20.0.w,
                              fit: BoxFit.contain,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
