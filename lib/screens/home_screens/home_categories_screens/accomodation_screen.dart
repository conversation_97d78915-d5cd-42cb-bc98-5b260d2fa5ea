import 'package:afa_app/app_config/api_providers/home_screen_categories_providers.dart';
import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/models/home_screens_models/venue_screen_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:riverpod_context/riverpod_context.dart';

class AccomodationScreen extends StatefulWidget {
  const AccomodationScreen({super.key});

  @override
  State<AccomodationScreen> createState() => _AccomodationScreenState();
}

class _AccomodationScreenState extends State<AccomodationScreen> {
  Future<List<VenueScreenModel>>? _fetchVenueDetails;

  @override
  void initState() {
    _fetchVenueDetails = context
        .read(HomeScreenCategoriesProviders.venueScreenProvidersApis)
        .getVenueDetails(context: context);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: FutureBuilder(
        future: _fetchVenueDetails,
        builder: (context, AsyncSnapshot<List<VenueScreenModel>> snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Center(child: CommonComponents.loadingDataFromServer());
          } else if (snapshot.data == null) {
            return Center(child: CommonComponents.noDataFoundWidget());
          } else {
            return SafeArea(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CommonComponents.comonTitleScreen(
                    context: context,
                    title: "Venue",
                  ),
                  SizedBox(height: 10.0.h),

                  Expanded(
                    child: ListView.separated(
                      padding: EdgeInsets.all(10.0.h),
                      separatorBuilder: (context, venueIndex) =>
                          const Divider(color: AppColors.greyColor),
                      itemCount: snapshot.data!.length,
                      itemBuilder: (context, venueIndex) => Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadiusGeometry.all(
                              Radius.circular(16.0.r),
                            ),
                            child: CommonComponents.imageWithNetworkCache(
                              context: context,
                              image: snapshot.data![venueIndex].featuredImage!,
                              height: 213.0.h,
                              width: 398.0.w,
                              fit: BoxFit.fill,
                            ),
                          ),
                          SizedBox(height: 10.0.h),
                          Text(
                            snapshot.data![venueIndex].title!,
                            style: TextStyle(
                              fontSize: 18.0.sp,
                              fontWeight: FontWeight.bold,
                              color: AppColors.blackColor,
                            ),
                          ),
                          SizedBox(height: 10.0.h),
                          Text(
                            snapshot.data![venueIndex].description!,
                            style: TextStyle(fontSize: 14.0.sp),
                          ),
                          SizedBox(height: 15.0.h),
                          SizedBox(
                            height: 120.0.h,
                            child: ListView.separated(
                              scrollDirection: Axis.horizontal,
                              physics: const BouncingScrollPhysics(),
                              separatorBuilder: (context, imageIndex) =>
                                  SizedBox(width: 20.0.w),
                              itemCount:
                                  snapshot.data![venueIndex].images!.length,
                              itemBuilder: (context, imageIndex) => ClipRRect(
                                borderRadius: BorderRadius.all(
                                  Radius.circular(10.0.r),
                                ),
                                child: CommonComponents.imageWithNetworkCache(
                                  context: context,
                                  image: snapshot
                                      .data![venueIndex]
                                      .images![imageIndex],
                                  height: 100.0.h,
                                  width: 131.0.w,
                                  fit: BoxFit.fill,
                                ),
                              ),
                            ),
                          ),
                          SizedBox(height: 20.0.h),
                          Text(
                            "Location",
                            style: TextStyle(
                              fontSize: 16.0.sp,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: 10.0.h),
                          SizedBox(
                            height: 255.0.h,
                            width: 398.0.w,

                            child: ClipRRect(
                              borderRadius: BorderRadius.all(
                                Radius.circular(12.0.r),
                              ),
                              child: GoogleMap(
                                initialCameraPosition: CameraPosition(
                                  target: LatLng(
                                    snapshot.data![venueIndex].locationLat!,
                                    snapshot.data![venueIndex].locationLong!,
                                  ),
                                  zoom: 18.0.h,
                                ),
                                zoomControlsEnabled: true,

                                markers: {
                                  Marker(
                                    markerId: const MarkerId("location"),
                                    position: LatLng(
                                      snapshot.data![venueIndex].locationLat!,
                                      snapshot.data![venueIndex].locationLong!,
                                    ),
                                  ),
                                },
                              ),
                            ),
                          ),
                          SizedBox(height: 20.0.h),
                        ],
                      ),
                    ),
                  ),

                  // SizedBox(height: 20.0.h),
                  // ElevatedButton(
                  //   onPressed: () {},

                  //   style: ElevatedButton.styleFrom(
                  //     foregroundColor: Colors.white,
                  //     backgroundColor: AppColors.midLevelGreenColor,
                  //     textStyle: TextStyle(
                  //       fontSize: 16.0.sp,
                  //       fontWeight: FontWeight.bold,
                  //     ),
                  //     minimumSize: Size(398.0.w, 46.0.h),
                  //     shape: RoundedRectangleBorder(
                  //       borderRadius: BorderRadius.all(
                  //         Radius.circular(32.0.r),
                  //       ),
                  //     ),
                  //   ),
                  //   child: const Text("Book Your room now"),
                  // ),
                ],
              ),
            );
          }
        },
      ),
    );
  }
}
