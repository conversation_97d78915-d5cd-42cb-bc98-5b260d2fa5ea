import 'dart:async';

import 'package:afa_app/app_config/api_keys.dart';
import 'package:afa_app/app_config/api_providers/chat_providers.dart';
import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/app_images.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/models/chat_models/chat_screen_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

class ChatScreen extends StatefulWidget {
  const ChatScreen({super.key});
  // final int? threadID;

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  Timer? _timer;
  final StateProvider<List<ChatScreenModel>> _messages = StateProvider(
    (ref) => [],
  );
  bool _initialLoading = true;
  int? _myUserID;
  String? _myUserName, _myUserImage;
  bool _isDisposed = false;

  @override
  void initState() {
    Future.delayed(Duration.zero, () async {
      _myUserID = await CommonComponents.getSavedData(ApiKeys.userID);
      _myUserName = await CommonComponents.getSavedData(ApiKeys.userName);
      _myUserImage = await CommonComponents.getSavedData(ApiKeys.userImage);

      if (!mounted) return;
      await context
          .read(ChatProviders.chatProvidersApis)
          .getAllChat(context: context)
          .then((value) {
            if (mounted) {
              setState(() {
                context.read(_messages).clear();
                context.read(_messages.notifier).state = value;
                _initialLoading = false;
              });
            }
          });
    }).then((value) {
      _timer = Timer.periodic(const Duration(seconds: 5), (timer) async {
        if (!mounted || _isDisposed) {
          _timer!.cancel();
          return;
        } else {
          final getNewMessage = await context
              .read(ChatProviders.chatProvidersApis)
              .getAllChat(context: context);
          if (!mounted) return;

          // Merge messages intelligently to avoid duplicates and flickering
          final currentMessages = context.read(_messages);
          final mergedMessages = <ChatScreenModel>[];

          // Add all new messages from server
          for (final newMsg in getNewMessage) {
            mergedMessages.add(newMsg);
          }

          // Add any local messages that don't exist on server yet (no messageId)
          for (final localMsg in currentMessages) {
            if (localMsg.messageId == null) {
              // Check if this local message is a duplicate of any server message
              final isDuplicate = mergedMessages.any(
                (serverMsg) =>
                    serverMsg.senderID == localMsg.senderID &&
                    serverMsg.message?.trim() == localMsg.message?.trim(),
              );

              // Only add local message if it's not a duplicate
              if (!isDuplicate) {
                mergedMessages.insert(0, localMsg);
              }
            }
          }

          context.read(_messages.notifier).state = mergedMessages;
        }
      });
    });
    super.initState();
  }

  @override
  void dispose() {
    _messageController.dispose();
    _timer?.cancel();
    _isDisposed = true;

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _initialLoading
          ? Center(child: CommonComponents.loadingDataFromServer())
          : SafeArea(
              child: Padding(
                padding: EdgeInsets.all(15.0.h),
                child: Column(
                  children: [
                    CommonComponents.comonTitleScreen(
                      context: context,
                      title: "Chat",
                    ),
                    SizedBox(height: 10.0.h),
                    Consumer(
                      builder: (context, watch, child) => Expanded(
                        child: ListView.separated(
                          reverse: true,
                          physics: const BouncingScrollPhysics(),
                          separatorBuilder: (context, index) =>
                              SizedBox(height: 10.0.h),
                          itemCount: watch.watch(_messages).length,
                          itemBuilder: (context, index) => Directionality(
                            textDirection:
                                watch.watch(_messages)[index].senderID ==
                                    _myUserID
                                ? TextDirection.rtl
                                : TextDirection.ltr,

                            child: Row(
                              children: [
                                ClipOval(
                                  child: CommonComponents.imageWithNetworkCache(
                                    context: context,
                                    image: watch
                                        .watch(_messages)[index]
                                        .userImage!,
                                    height: 40.0.h,
                                    width: 40.0.w,
                                    fit: BoxFit.contain,
                                  ),
                                ),
                                SizedBox(width: 5.0.w),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Align(
                                        alignment:
                                            watch
                                                    .watch(_messages)[index]
                                                    .senderID ==
                                                _myUserID
                                            ? Alignment.centerRight
                                            : Alignment.centerLeft,
                                        child: Text(
                                          watch
                                              .watch(_messages)[index]
                                              .userName!,
                                          style: TextStyle(
                                            fontSize: 12.0.sp,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                      SizedBox(height: 5.0.h),
                                      Container(
                                        padding: EdgeInsets.all(10.0.h),
                                        decoration: BoxDecoration(
                                          color:
                                              watch
                                                      .watch(_messages)[index]
                                                      .senderID ==
                                                  _myUserID
                                              ? AppColors.darkGreenColor
                                              : AppColors.lightgreyColor,
                                          borderRadius:
                                              // watch
                                              //         .watch(_messages)[index]
                                              //         .senderID ==
                                              //     _myUserID
                                              // ? BorderRadius.only(
                                              //     topLeft: Radius.circular(
                                              //       24.0.r,
                                              //     ),
                                              //     bottomLeft: Radius.circular(
                                              //       24.0.r,
                                              //     ),
                                              //   )
                                              // :
                                              BorderRadius.all(
                                                Radius.circular(15.0.r),
                                              ),
                                        ),
                                        child: Text(
                                          watch
                                              .watch(_messages)[index]
                                              .message!,
                                          style: TextStyle(
                                            fontSize: 12.0.sp,
                                            fontWeight: FontWeight.bold,
                                            color:
                                                watch
                                                        .watch(_messages)[index]
                                                        .senderID ==
                                                    _myUserID
                                                ? Colors.white
                                                : AppColors.blackColor,
                                          ),
                                        ),
                                      ),
                                      // Add timestamp display
                                      if (watch
                                          .watch(_messages)[index]
                                          .formattedTimestamp
                                          .isNotEmpty)
                                        Padding(
                                          padding: EdgeInsets.only(top: 4.0.h),
                                          child: Align(
                                            alignment:
                                                watch
                                                        .watch(_messages)[index]
                                                        .senderID ==
                                                    _myUserID
                                                ? Alignment.centerRight
                                                : Alignment.centerLeft,
                                            child: Text(
                                              watch
                                                  .watch(_messages)[index]
                                                  .formattedTimestamp,
                                              style: TextStyle(
                                                fontSize: 10.0.sp,
                                                color: Colors.grey[600],
                                                fontWeight: FontWeight.w400,
                                              ),
                                              textDirection: TextDirection.ltr,
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _messageController,
                            style: TextStyle(
                              fontSize: 14.0.sp,
                              color: AppColors.blackColor,
                              fontWeight: FontWeight.bold,
                            ),
                            decoration: InputDecoration(
                              hintText: "Type Message Here",
                              hintStyle: TextStyle(fontSize: 12.0.sp),

                              border: OutlineInputBorder(
                                borderSide: const BorderSide(
                                  color: AppColors.lightgreyColor,
                                ),
                                borderRadius: BorderRadius.all(
                                  Radius.circular(100.0.r),
                                ),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderSide: const BorderSide(
                                  color: AppColors.lightgreyColor,
                                ),
                                borderRadius: BorderRadius.all(
                                  Radius.circular(100.0.r),
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderSide: const BorderSide(
                                  color: AppColors.lightgreyColor,
                                ),
                                borderRadius: BorderRadius.all(
                                  Radius.circular(100.0.r),
                                ),
                              ),
                              disabledBorder: OutlineInputBorder(
                                borderSide: const BorderSide(
                                  color: AppColors.lightgreyColor,
                                ),
                                borderRadius: BorderRadius.all(
                                  Radius.circular(100.0.r),
                                ),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(width: 10.0.w),
                        InkWell(
                          onTap: () async {
                            if (_messageController.text.isNotEmpty) {
                              setState(() {
                                context
                                    .read(_messages.notifier)
                                    .state
                                    .insert(
                                      0,
                                      ChatScreenModel(
                                        senderID: _myUserID,
                                        message: _messageController.text,
                                        userName: _myUserName,
                                        userImage: _myUserImage,
                                        timestamp:
                                            DateTime.now(), // Add current timestamp for immediate display
                                      ),
                                    );
                              });
                              // _messageController.clear();

                              final message = _messageController.text;

                              _messageController.clear();
                              await context
                                  .read(ChatProviders.chatProvidersApis)
                                  .sendMessage(
                                    context: context,
                                    threadId: context
                                        .read(ChatProviders.inboxProvidersApis)
                                        .threadID!,
                                    message: message,
                                  );
                            }
                          },
                          child: Container(
                            alignment: Alignment.center,
                            padding: EdgeInsets.all(10.0.h),
                            decoration: const BoxDecoration(
                              color: AppColors.darkGreenColor,
                              shape: BoxShape.circle,
                            ),
                            child: CommonComponents.imageAssetWithCache(
                              context: context,
                              image: AppImages.sendMessageIncon,
                              height: 35.0.h,
                              width: 35.0.w,
                              fit: BoxFit.contain,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
