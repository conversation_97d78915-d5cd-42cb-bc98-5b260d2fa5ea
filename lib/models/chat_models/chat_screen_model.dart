class ChatScreenModel {
  ChatScreenModel.fromJson(
    Map<String, dynamic> jsonData, {
    required String name,
    required String image,
  }) {
    senderID = jsonData['sender_id'];
    message = jsonData['message'];
    messageId = jsonData['message_id'];
    userName = name;
    userImage = image;
    // Parse timestamp from created_at field (milliseconds)
    if (jsonData['created_at'] != null) {
      timestamp = DateTime.fromMillisecondsSinceEpoch(
        jsonData['created_at'] is int
            ? jsonData['created_at']
            : int.parse(jsonData['created_at'].toString()),
      );
    }
  }

  ChatScreenModel({
    this.message,
    this.userName,
    this.userImage,
    this.senderID,
    this.timestamp,
    this.messageId,
  });

  int? senderID, messageId;
  String? message, userName, userImage;
  DateTime? timestamp;

  /// Format timestamp to readable format like "3 Mar. 9:30 PM"
  String get formattedTimestamp {
    if (timestamp == null) return '';

    // Format time as "9:30 PM"
    final timeFormat = timestamp!.hour == 0
        ? '12:${timestamp!.minute.toString().padLeft(2, '0')} AM'
        : timestamp!.hour <= 12
        ? '${timestamp!.hour}:${timestamp!.minute.toString().padLeft(2, '0')} ${timestamp!.hour == 12 ? 'PM' : 'AM'}'
        : '${timestamp!.hour - 12}:${timestamp!.minute.toString().padLeft(2, '0')} PM';

    // Always show date and time format like "3 Mar. 9:30 PM"
    final months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];

    return '${timestamp!.day} ${months[timestamp!.month - 1]}. $timeFormat';
  }
}
