class ChatScreenModel {
  ChatScreenModel.fromJson(
    Map<String, dynamic> jsonData, {
    required String name,
    required String image,
  }) {
    senderID = jsonData['sender_id'];
    message = jsonData['message'];
    userName = name;
    userImage = image;
    // Parse timestamp from created_at field (milliseconds)
    if (jsonData['created_at'] != null) {
      timestamp = DateTime.fromMillisecondsSinceEpoch(
        jsonData['created_at'] is int
            ? jsonData['created_at']
            : int.parse(jsonData['created_at'].toString()),
      );
    }
  }

  ChatScreenModel({
    this.message,
    this.userName,
    this.userImage,
    this.senderID,
    this.timestamp,
  });

  int? senderID;
  String? message, userName, userImage;
  DateTime? timestamp;

  /// Format timestamp to readable format like "12 Aug. 12:30 PM"
  String get formattedTimestamp {
    if (timestamp == null) return '';

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final messageDate = DateTime(
      timestamp!.year,
      timestamp!.month,
      timestamp!.day,
    );

    // Format time as "12:30 PM"
    final timeFormat = timestamp!.hour == 0
        ? '12:${timestamp!.minute.toString().padLeft(2, '0')} AM'
        : timestamp!.hour <= 12
        ? '${timestamp!.hour}:${timestamp!.minute.toString().padLeft(2, '0')} ${timestamp!.hour == 12 ? 'PM' : 'AM'}'
        : '${timestamp!.hour - 12}:${timestamp!.minute.toString().padLeft(2, '0')} PM';

    // If message is from today, show only time
    if (messageDate == today) {
      return timeFormat;
    }

    // If message is from yesterday
    final yesterday = today.subtract(const Duration(days: 1));
    if (messageDate == yesterday) {
      return 'Yesterday $timeFormat';
    }

    // For older messages, show date and time
    final months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];

    return '${timestamp!.day} ${months[timestamp!.month - 1]}. $timeFormat';
  }
}
