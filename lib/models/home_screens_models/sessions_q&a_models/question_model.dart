import 'dart:developer';

import 'package:afa_app/models/home_screens_models/sessions_q&a_models/answers_model.dart';

class QuestionsModel {
  QuestionsModel.fromJson(Map<String, dynamic> jsonData) {
    log('Question data: ${jsonData}');

    // Handle both old and new API response formats
    if (jsonData.containsKey('content')) {
      // New API format
      questionId = jsonData['id'];
      question = jsonData['content'];
      date = jsonData['date'];
      isAnswer = jsonData['is_answer'] ?? false;
      parentId = jsonData['parent_id'] ?? 0;
      status = jsonData['status'];

      // Author information
      if (jsonData['author'] != null) {
        final author = jsonData['author'];
        userName = author['name'];
        userImage = author['avatar'];
        userEmail = author['email'];
        userPosition = author['position'];
        userCompany = author['company'];
        userCountry = author['country'];
        profileUrl = author['profile_url'];
      }

      // For new format, answers would be separate API calls or nested
      answers = [];
    } else {
      // Old API format (backward compatibility)
      question = jsonData['question'];
      userName = jsonData['user'];
      userImage = jsonData.containsKey('user_image')
          ? jsonData['user_image']
          : jsonData.containsKey('avatar')
          ? jsonData['avatar']
          : null;
      answers = (jsonData['answers'] as List)
          .map((answer) => AnswersModel.fromJson(answer))
          .toList();
    }
  }

  // New fields for updated API
  int? questionId, parentId;
  String? question, userName, userImage, date, status;
  String? userEmail, userPosition, userCompany, userCountry, profileUrl;
  bool? isAnswer;

  // Existing field
  List<AnswersModel>? answers;
}
