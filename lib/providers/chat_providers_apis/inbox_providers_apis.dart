import 'dart:developer';

import 'package:afa_app/app_config/api_keys.dart';
import 'package:afa_app/app_config/api_providers/profile_providers.dart';
import 'package:afa_app/app_config/api_requests.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/models/chat_models/inbox_model.dart';
import 'package:flutter/material.dart';
import 'package:riverpod_context/riverpod_context.dart';

class InboxProvidersApis extends ChangeNotifier {
  int? threadID;

  // Cache to store user_id -> thread_id mapping to prevent duplicate threads
  final Map<int, int> _userThreadCache = {};

  // Loading state for thread cache
  bool _isLoadingThreads = false;
  bool get isLoadingThreads => _isLoadingThreads;

  void setThreadID(int id) {
    threadID = id;
    notifyListeners();
  }

  /// Get existing thread ID for a user, returns null if no thread exists
  int? getExistingThreadId(int userId) {
    return _userThreadCache[userId];
  }

  /// Add or update user-thread mapping in cache
  void updateThreadCache(int userId, int threadId) {
    _userThreadCache[userId] = threadId;
  }

  /// Clear the thread cache (useful for logout or refresh)
  void clearThreadCache() {
    _userThreadCache.clear();
  }

  /// Load thread cache for screens - call this in initState of screens
  Future<void> loadThreadCacheForScreen(BuildContext context) async {
    if (_isLoadingThreads) return; // Prevent multiple simultaneous loads

    _isLoadingThreads = true;
    notifyListeners();

    try {
      await getChatInbox(context: context, getUsers: false);
    } finally {
      _isLoadingThreads = false;
      notifyListeners();
    }
  }

  /// Refresh thread cache by fetching latest inbox data (for messages screen)
  Future<void> refreshThreadCache(BuildContext context) async {
    await getChatInbox(context: context, getUsers: false);
  }

  Future<List<InboxModel>> getChatInbox({
    required BuildContext context,
    bool getUsers = true,
  }) async {
    final List<InboxModel> inboxList = [];
    final String myToken = await CommonComponents.getSavedData(
      ApiKeys.userToken,
    );

    final int myUserId = await CommonComponents.getSavedData(ApiKeys.userID);

    if (!context.mounted) return [];
    final Map<String, dynamic>? dataList = await ApiRequests.getApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/better-messages/v1/threads",
      headers: {"Authorization": "Bearer $myToken"},
    );

    log('FULL_URLL ${ApiKeys.baseUrl}wp-json/better-messages/v1/threads');
    if (dataList != null) {
      // Collect all participant IDs first
      final List<int> participantIds = [];
      final List<Map<String, dynamic>> threadsData = [];

      for (final data in dataList['threads']) {
        if (data['participantsCount'] == 2) {
          for (final participants in data['participants']) {
            if (participants != myUserId) {
              participantIds.add(participants);
              threadsData.add(data);
              break; // Only one other participant in 2-person chat
            }
          }
        }
      }

      if (!context.mounted) return [];

      // Update thread cache for all participants
      for (int i = 0; i < threadsData.length; i++) {
        final data = threadsData[i];
        final participantId = participantIds[i];
        // Always update thread cache with user-thread mapping
        updateThreadCache(participantId, data['thread_id']);
      }

      // Only get user data if getUsers is true (for messages screen)
      if (getUsers && participantIds.isNotEmpty) {
        final Map<int, dynamic> usersData = await context
            .read(ProfileProviders.profileSettingsProvidersApis)
            .getBulkUserData(context: context, userIds: participantIds);

        // Create inbox items using the bulk user data
        for (int i = 0; i < threadsData.length; i++) {
          final data = threadsData[i];
          final participantId = participantIds[i];
          final userData = usersData[participantId];

          if (userData != null) {
            inboxList.add(
              InboxModel.fromJson(
                data,
                userName: userData.userName ?? 'Unknown User',
                userImage: userData.userImage ?? '',
              ),
            );
          }
        }
      }
    } else {
      debugPrint("ERROR WITH getChatInbox FUNCTION");
    }

    return inboxList;
  }
}
