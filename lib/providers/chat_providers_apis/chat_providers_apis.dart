import 'dart:convert';
import 'dart:developer';

import 'package:afa_app/app_config/api_keys.dart';
import 'package:afa_app/app_config/api_providers/chat_providers.dart';
import 'package:afa_app/app_config/api_providers/profile_providers.dart';
import 'package:afa_app/app_config/api_requests.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/models/chat_models/chat_screen_model.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:riverpod_context/riverpod_context.dart';

class ChatProvidersApis extends ChangeNotifier {
  Future<List<ChatScreenModel>> getAllChat({
    required BuildContext context,
  }) async {
    log('FIRST_OF_Function');

    final List<ChatScreenModel> chatList = [];

    final String myToken = await CommonComponents.getSavedData(
      ApiKeys.userToken,
    );

    log('TOKEN: $myToken');
    if (!context.mounted) return [];

    final int threadID = context
        .read(ChatProviders.inboxProvidersApis)
        .threadID!;
    // print(threadID);
    if (!context.mounted) return [];
    log('BeforeGettting_Data');
    final Map<String, dynamic>? dataList = await ApiRequests.postApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/better-messages/v1/thread/$threadID",
      headers: {"Authorization": "Bearer $myToken"},
      body: {},
      showLoadingWidget: false,
    );

    log(
      'FULL_URL : ${ApiKeys.baseUrl}wp-json/better-messages/v1/thread/$threadID',
    );
    if (dataList != null) {
      final users = (dataList['users'] as List?) ?? [];

      for (final data in dataList['messages']) {
        if (!context.mounted) return [];
        final user = users.firstWhereOrNull(
          (user) => user['id'].toString() == data['sender_id'].toString(),
        );
        chatList.add(
          ChatScreenModel.fromJson(
            data,
            name: user['name'],
            image: user['avatar'],
          ),
        );
        // await context
        //     .read(ProfileProviders.profileSettingsProvidersApis)
        //     .getUserData(
        //       context: context,
        //       getMyProfileData: false,
        //       userID: data['sender_id'],
        //     )
        //     .then((value) {
        //
        //     });
      }
    } else {
      debugPrint("ERROR WITH getAllChat FUNCTION");
    }

    return chatList;
  }

  Future<void> sendMessage({
    required BuildContext context,
    required int threadId,
    required String message,
    bool showLoading = false,
  }) async {
    final String myToken = await CommonComponents.getSavedData(
      ApiKeys.userToken,
    );
    if (!context.mounted) return;

    final Map<String, dynamic>? data = await ApiRequests.postApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/better-messages/v1/thread/$threadId/send",
      headers: {
        "Authorization": "Bearer $myToken",
        "Content-Type": "application/json",
      },
      body: json.encode({"message": message}),
      showLoadingWidget: showLoading,
    );

    if (data != null) {
      debugPrint("Sent Message Successfully");
    } else {
      debugPrint("ERROR WITH sendMessage FUNCTION");
    }
  }
}
