import 'dart:convert';

import 'package:afa_app/app_config/api_keys.dart';
import 'package:afa_app/app_config/api_providers/chat_providers.dart';
import 'package:afa_app/app_config/api_requests.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/app_config/routes.dart';
import 'package:flutter/material.dart';
import 'package:riverpod_context/riverpod_context.dart';

class NewMessageProvidersApis extends ChangeNotifier {
  Future<void> sendNewMessage({
    required BuildContext context,
    required int recipientId,
    required String message,
  }) async {
    if (!context.mounted) return;

    final inboxProvider = context.read(ChatProviders.inboxProvidersApis);

    // Check if threads are still loading
    if (inboxProvider.isLoadingThreads) {
      // Show loading dialog
      _showLoadingDialog(context);
      return;
    }

    // Check if a thread already exists with this user
    final existingThreadId = inboxProvider.getExistingThreadId(recipientId);

    if (existingThreadId != null) {
      // Use existing thread - send message to existing thread
      await _sendToExistingThread(
        context: context,
        threadId: existingThreadId,
        message: message,
      );
    } else {
      // Create new thread
      await _createNewThread(
        context: context,
        recipientId: recipientId,
        message: message,
      );
    }
  }

  /// Send message to existing thread
  Future<void> _sendToExistingThread({
    required BuildContext context,
    required int threadId,
    required String message,
  }) async {
    if (!context.mounted) return;

    // Set the thread ID and navigate to chat
    context.read(ChatProviders.inboxProvidersApis).setThreadID(threadId);

    // Send the message to the existing thread
    await context
        .read(ChatProviders.chatProvidersApis)
        .sendMessage(
          context: context,
          threadId: threadId,
          message: message,
          showLoading: true,
        );

    if (!context.mounted) return;
    Navigator.pop(context);
    Navigator.pop(context);
    Navigator.pushNamed(context, PATHS.chatScreen);
  }

  /// Create new thread with recipient
  Future<void> _createNewThread({
    required BuildContext context,
    required int recipientId,
    required String message,
  }) async {
    final String myToken = await CommonComponents.getSavedData(
      ApiKeys.userToken,
    );

    if (!context.mounted) return;

    final Map<String, dynamic>? data = await ApiRequests.postApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/better-messages/v1/thread/new",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer $myToken",
      },
      body: json.encode({
        "recipients": [recipientId.toString()],
        "message": message,
        "subject": message,
      }),
    );

    if (data != null) {
      if (!context.mounted) return;
      final threadId = data['thread_id'];

      // Update thread cache with new thread
      context
          .read(ChatProviders.inboxProvidersApis)
          .updateThreadCache(recipientId, threadId);

      context.read(ChatProviders.inboxProvidersApis).setThreadID(threadId);
      Navigator.pop(context);
      Navigator.pushNamed(context, PATHS.chatScreen);
    } else {
      debugPrint("ERROR WITH NewMessageProvidersApis FUNCTION");
    }
  }

  /// Show loading dialog when threads are still loading
  void _showLoadingDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 16),
            const Text("Loading conversations..."),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text("Cancel"),
          ),
        ],
      ),
    );
  }
}
