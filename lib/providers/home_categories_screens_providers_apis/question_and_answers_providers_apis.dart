import 'dart:math';

import 'package:afa_app/app_config/api_keys.dart';
import 'package:afa_app/app_config/api_requests.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/models/home_screens_models/sessions_q&a_models/question_model.dart';
import 'package:afa_app/models/home_screens_models/sessions_q&a_models/session_questions_model.dart';
import 'package:afa_app/models/home_screens_models/sessions_q&a_models/session_time_periods_model.dart';
import 'package:flutter/material.dart';

class QuestionAndAnswersProvidersApis extends ChangeNotifier {
  SessionQAndADaysModel? getQandAByTimePeriod;
  Key? questionsKey;

  void selectTimePeriod(SessionQAndADaysModel? daysModel) {
    getQandAByTimePeriod = daysModel;
    notifyListeners();
  }

  void rebuildQandAWidget() {
    questionsKey = ValueKey(Random().nextInt(10000));
    notifyListeners();
  }

  Future<List<SessionQAndADaysModel>> getQAndADaysList({
    required BuildContext context,
  }) async {
    final List<SessionQAndADaysModel> daysList = [];

    final Map<String, dynamic>? dataList = await ApiRequests.getApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/agenda/filters-list",
      headers: {},
    );

    if (dataList != null) {
      for (final data in dataList['time_periods']) {
        daysList.add(SessionQAndADaysModel.fromJson(data));
      }
    } else {
      debugPrint("ERROR WITH getAgendaFiltersList FUNCTION");
    }
    daysList.removeAt(0);
    return daysList;
  }

  Future<List<SessionQuestionsModel>> getSessionQuestions({
    required BuildContext context,
    required bool setInitialTimePeriod,
  }) async {
    final List<SessionQuestionsModel> sessionList = [];
    if (setInitialTimePeriod) {
      await getQAndADaysList(context: context).then((value) {
        selectTimePeriod(value[0]);
      });
    }

    if (!context.mounted) return [];

    final List<dynamic>? dataList = await ApiRequests.getApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl:
          "wp-json/buddypress-ext/v1/agenda/filter?time_period=${getQandAByTimePeriod!.dayName}",
      headers: {},
    );

    print(
      'FULL_URRRRL ${ApiKeys.baseUrl}wp-json/buddypress-ext/v1/agenda/filter?time_period=${getQandAByTimePeriod!.dayName}',
    );

    if (dataList != null) {
      for (final data in dataList) {
        if (data['is_comment_open']) {
          sessionList.add(SessionQuestionsModel.fromJson(data));
        }
      }
    } else {
      debugPrint("ERROR WITH getQuestions FUNCTION");
    }

    return sessionList;
  }

  Future<List<QuestionsModel>> getQuestions({
    required BuildContext context,
  }) async {
    final List<QuestionsModel> questionList = [];

    final List<dynamic>? dataList = await ApiRequests.getApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl:
          "wp-json/buddypress-ext/v1/agenda/filter?time_period=${getQandAByTimePeriod!.dayName}",
      headers: {},
    );

    if (dataList != null) {
      for (final data in dataList) {
        for (final question in data['questions']) {
          questionList.add(QuestionsModel.fromJson(question));
        }
      }
    } else {
      debugPrint("ERROR WITH getQuestions FUNCTION");
    }

    return questionList;
  }

  Future<void> createQuestion({
    required BuildContext context,
    required String question,
    required int sessionID,
  }) async {
    final String myToken = await CommonComponents.getSavedData(
      ApiKeys.userToken,
    );

    if (!context.mounted) return;

    final Map<String, dynamic>? data = await ApiRequests.postApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/agenda/$sessionID/questions",
      headers: {"Authorization": "Bearer $myToken"},
      body: {"content": question},
    );
    if (data != null) {
      if (!context.mounted) return;
      CommonComponents.showCustomizedSnackBar(
        context: context,
        title: "Your Question Added Successfully",
      );
      Navigator.pop(context);
    } else {
      debugPrint("ERROR WITH createQuestion FUNCTION");
    }
  }
}
