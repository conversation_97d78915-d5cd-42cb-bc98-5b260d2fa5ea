import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:afa_app/app_config/api_keys.dart';
import 'package:afa_app/app_config/api_requests.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/models/profile_models/profile_user_data_model.dart';
import 'package:afa_app/models/profile_models/user_countries_model.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

class ProfileSettingsProvidersApis extends ChangeNotifier {
  // Key? profileKey;

  // void rebuildProfile() {
  //   profileKey = ValueKey(Random().nextInt(1000));
  //   notifyListeners();
  // }

  UserCountriesModel? selectedCountry;

  void setSelectedCountry(UserCountriesModel country) {
    selectedCountry = country;
    notifyListeners();
  }

  Future<ProfileUserDataModel> getUserData({
    required BuildContext context,
    required bool getMyProfileData,
    int? userID,
  }) async {
    ProfileUserDataModel? userData;

    final int myUserId = await CommonComponents.getSavedData(ApiKeys.userID);
    if (!context.mounted) return userData!;
    final Map<String, dynamic>? data = await ApiRequests.getApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: getMyProfileData
          ? "wp-json/buddypress-ext/v1/profile/$myUserId"
          : "wp-json/buddypress-ext/v1/profile/$userID",
      headers: {},
    );

    log(
      'FULL_URLrrrL ${ApiKeys.baseUrl}wp-json/buddypress-ext/v1/profile/$myUserId',
    );
    log('DATAA ${data}');

    if (data != null) {
      userData = ProfileUserDataModel.fromJson(data);
    } else {
      debugPrint("ERROR WITH userData FUNCTION");
    }

    return userData!;
  }

  /// Get multiple users data in a single API call for better performance
  Future<Map<int, ProfileUserDataModel>> getBulkUserData({
    required BuildContext context,
    required List<int> userIds,
  }) async {
    final Map<int, ProfileUserDataModel> usersData = {};

    if (userIds.isEmpty) return usersData;

    if (!context.mounted) return usersData;

    final Map<String, dynamic>? data = await ApiRequests.postApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/attendancee/by-ids",
      headers: {"Content-Type": "application/json"},
      body: json.encode({"ids": userIds}),
      showLoadingWidget: false,
    );

    log(
      'BULK_USER_DATA_URL: ${ApiKeys.baseUrl}wp-json/buddypress-ext/v1/attendancee/by-ids',
    );
    log('BULK_USER_DATA_RESPONSE: $data');

    if (data != null && data['items'] != null) {
      for (final item in data['items']) {
        final userData = ProfileUserDataModel(
          userID: item['id'],
          userName: item['name'],
          userImage: item['avatar'],
          userEmail: item['email'],
          country: null, // Not provided in bulk API
          position: null, // Not provided in bulk API
          bio: null, // Not provided in bulk API
          phoneNumber: null, // Not provided in bulk API
        );
        usersData[item['id']] = userData;
      }
    } else {
      debugPrint("ERROR WITH getBulkUserData FUNCTION");
    }

    return usersData;
  }

  Future<List<UserCountriesModel>> getAllCountries({
    required BuildContext context,
  }) async {
    final List<UserCountriesModel> countriesList = [];

    final Map<String, dynamic>? dataList = await ApiRequests.getApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/countries",
      headers: {},
    );

    if (dataList != null) {
      for (final data in dataList['countries']) {
        countriesList.add(UserCountriesModel.fromJson(data));
      }
    } else {
      debugPrint("ERROR WITH getAllCountries FUNCTION");
    }
    return countriesList;
  }

  Future userEditProfile({
    required BuildContext context,
    required File? userImage,
    required String userName,
    required String phoneNumber,
    required String userEmail,
    required String position,
    required String bio,
    required String country,
  }) async {
    try {
      final int userID = await CommonComponents.getSavedData(ApiKeys.userID);
      final String myToken = await CommonComponents.getSavedData(
        ApiKeys.userToken,
      );

      if (await CommonComponents.checkConnectivity()) {
        if (context.mounted) {
          CommonComponents.loading(context);
        } else {
          return;
        }
        final String url =
            "${ApiKeys.baseUrl}wp-json/buddypress-ext/v1/profile/$userID/form";

        final http.MultipartRequest response = http.MultipartRequest(
          "POST",
          Uri.parse(url),
        );

        response.headers["Content-Type"] = "application/json";
        response.headers["Authorization"] = "Bearer $myToken";

        if (userImage != null) {
          response.files.add(
            await http.MultipartFile.fromPath('profile_image', userImage.path),
          );
        }

        response.files.addAll([
          http.MultipartFile.fromString('name', userName),
          http.MultipartFile.fromString('email', userEmail),
          http.MultipartFile.fromString('phone', phoneNumber),
          http.MultipartFile.fromString('position', position),
          http.MultipartFile.fromString('bio', bio),
          // http.MultipartFile.fromString('country',country),
        ]);

        response.send().then((results) async {
          await http.Response.fromStream(results).then((response) async {
            if (response.statusCode == 200) {
              final decoddedData = jsonDecode(response.body);
              if (!context.mounted) return;
              Navigator.pop(context);
              await CommonComponents.saveData(
                key: ApiKeys.userName,
                value: decoddedData['user']['name'],
              );

              await CommonComponents.saveData(
                key: ApiKeys.userImage,
                value: decoddedData['user']['profile_image'],
              );

              if (context.mounted) {
                CommonComponents.showCustomizedSnackBar(
                  context: context,
                  title: "Your data has been Updated.",
                );
              }
            } else {
              if (!context.mounted) return;
              CommonComponents.showCustomizedSnackBar(
                context: context,
                title: "Server Error Request",
              );
              Navigator.pop(context);
            }
          });
        });
      } else {
        if (context.mounted) {
          Navigator.pop(context);
          await CommonComponents.notConnectionAlert(context);
        } else {
          return;
        }
      }
    } on TimeoutException catch (error) {
      if (context.mounted) {
        Navigator.pop(context);
      }
      debugPrint("Time Out Exception is::=>$error");
      if (context.mounted) {
        await CommonComponents.timeOutExceptionAlert(context);
      }
    } on SocketException catch (error) {
      if (context.mounted) {
        Navigator.pop(context);
      }
      debugPrint("Socket Exception is::=>$error");
      if (context.mounted) {
        await CommonComponents.socketExceptionAlert(context);
      }
    } catch (error) {
      if (context.mounted) {
        Navigator.pop(context);
      }
      debugPrint("General Exception is::=>$error");
    }
  }
}
