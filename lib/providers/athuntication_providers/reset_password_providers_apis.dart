import 'dart:convert';
import 'dart:developer';

import 'package:afa_app/app_config/api_keys.dart';
import 'package:afa_app/app_config/api_requests.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/models/athuntication_models/reset_password_model.dart';
import 'package:flutter/material.dart';

class ResetPasswordProvidersApis extends ChangeNotifier {
  Future<void> resetPassword({
    required BuildContext context,
    required String email,
  }) async {
    final ResetPasswordModel model = ResetPasswordModel(email: email);

    final Map<String, dynamic>? data = await ApiRequests.postApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/auth/password/reset",
      headers: {"Content-Type": "application/json"},
      body: jsonEncode(model.toJson()),
    );

    log('dataAAA ${data}');

    if (data != null) {
      if (!context.mounted) return;
      CommonComponents.showCustomizedSnackBar(
        context: context,
        title: data['message'],
      );

      Navigator.pop(context);
    } else {
      debugPrint("ERROR WITH resetPassword FUNCTION");
    }
  }
}
